import { Meta, StoryObj } from '@storybook/vue3';
import ExecuteItem from './ExecuteItem.vue';
import { MsgContentTypeEnum } from '../../common/constants';

// 更多关于如何设置故事：https://storybook.js.org/docs/writing-stories
const meta = {
  title: 'ChatItem/ExecuteItem',
  component: ExecuteItem,
  // 此组件将有一个自动生成的文档页面：https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  argTypes: {
    queryId: {
      control: 'number',
      description: '查询的唯一ID'
    },
    question: {
      control: 'text',
      description: '问题文本'
    },
    queryMode: {
      control: 'select',
      options: ['PLAIN_TEXT', 'WEB_SERVICE', 'WEB_PAGE', 'SQL'],
      description: '查询模式'
    },
    executeLoading: {
      control: 'boolean',
      description: '是否正在执行查询'
    },
    entitySwitchLoading: {
      control: 'boolean',
      description: '实体切换是否正在加载'
    },
    chartIndex: {
      control: 'number',
      description: '图表索引'
    },
    executeTip: {
      control: 'text',
      description: '执行提示信息'
    },
    executeErrorMsg: {
      control: 'text',
      description: '执行错误信息'
    },
    data: {
      control: 'object',
      description: '查询数据'
    },
    triggerResize: {
      control: 'boolean',
      description: '是否触发重新调整大小'
    },
    isDeveloper: {
      control: 'boolean',
      description: '是否为开发者模式'
    },
    isSimpleMode: {
      control: 'boolean',
      description: '是否为简单模式'
    }
  },
  // 参数的默认值
  args: {
    queryId: 1001,
    question: '如何提高销售额？',
    queryMode: 'PLAIN_TEXT',
    executeLoading: false,
    entitySwitchLoading: false,
    chartIndex: 0,
    isDeveloper: false,
    isSimpleMode: false
  },
} satisfies Meta<typeof ExecuteItem>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例 - 纯文本回答
export const 纯文本回答: Story = {
  args: {
    queryMode: 'PLAIN_TEXT',
    data: {
      id: 1001,
      queryId: 1001,
      question: '如何提高销售额？',
      queryMode: 'PLAIN_TEXT',
      queryState: 'SUCCEEDED',
      textResult: '提高销售额可以通过以下几种方式：\n1. 优化产品质量\n2. 提升客户服务\n3. 扩大营销渠道\n4. 针对性促销活动',
      textSummary: '提高销售额的多种策略',
      queryTimeCost: 234,
      queryColumns: [],
      queryResults: [],
      aggregateInfo: {},
      chatContext: {},
      entityInfo: {},
      queryAuthorization: {},
      queryText: '',
      response: {},
      similarQueries: [],
      recommendedDimensions: [],
      errorMsg: ''
    }
  }
};

// 加载中状态
export const 加载中状态: Story = {
  args: {
    executeLoading: true
  }
};

// 查询失败状态
export const 查询失败状态: Story = {
  args: {
    executeTip: '查询失败',
    executeErrorMsg: 'SELECT * FROM sales WHERE date > invalid_date'
  }
};

// 开发者模式
export const 开发者模式: Story = {
  args: {
    queryMode: 'PLAIN_TEXT',
    isDeveloper: true,
    data: {
      id: 1001,
      queryId: 1001,
      question: '如何提高销售额？',
      queryMode: 'PLAIN_TEXT',
      queryState: 'SUCCEEDED',
      textResult: '提高销售额可以通过以下几种方式：\n1. 优化产品质量\n2. 提升客户服务\n3. 扩大营销渠道\n4. 针对性促销活动',
      textSummary: '提高销售额的多种策略',
      queryTimeCost: 234,
      queryColumns: [],
      queryResults: [],
      aggregateInfo: {},
      chatContext: {},
      entityInfo: {},
      queryAuthorization: {},
      queryText: '',
      response: {},
      similarQueries: [],
      recommendedDimensions: [],
      errorMsg: ''
    }
  }
};

// 简洁模式
export const 简洁模式: Story = {
  args: {
    queryMode: 'PLAIN_TEXT',
    isSimpleMode: true,
    data: {
      id: 1001,
      queryId: 1001,
      question: '如何提高销售额？',
      queryMode: 'PLAIN_TEXT',
      queryState: 'SUCCEEDED',
      textResult: '提高销售额可以通过以下几种方式：\n1. 优化产品质量\n2. 提升客户服务\n3. 扩大营销渠道\n4. 针对性促销活动',
      textSummary: '提高销售额的多种策略',
      queryTimeCost: 234,
      queryColumns: [],
      queryResults: [],
      aggregateInfo: {},
      chatContext: {},
      entityInfo: {},
      queryAuthorization: {},
      queryText: '',
      response: {},
      similarQueries: [],
      recommendedDimensions: [],
      errorMsg: ''
    }
  }
};

// 网页内容示例
export const 网页内容: Story = {
  args: {
    queryMode: 'WEB_PAGE',
    data: {
      id: 1002,
      queryId: 1002,
      question: '显示公司网页',
      queryMode: 'WEB_PAGE',
      queryState: 'SUCCEEDED',
      textResult: '',
      textSummary: '公司网页',
      queryTimeCost: 156,
      queryColumns: [],
      queryResults: [],
      aggregateInfo: {},
      chatContext: {},
      entityInfo: {},
      queryAuthorization: {},
      queryText: '',
      response: {
        name: '公司主页',
        webPage: {
          url: 'https://example.com',
          params: [
            {
              key: 'height',
              value: 400,
              paramType: 'FORWARD'
            }
          ]
        }
      },
      similarQueries: [],
      recommendedDimensions: [],
      errorMsg: ''
    }
  }
};

// 数据可视化示例
export const 数据可视化: Story = {
  args: {
    queryMode: 'SQL',
    data: {
      id: 1003,
      queryId: 1003,
      question: '过去一年每季度的销售额是多少?',
      queryMode: 'SQL',
      queryState: 'SUCCEEDED',
      textResult: '',
      textSummary: '过去一年的季度销售额分析',
      queryTimeCost: 346,
      queryColumns: [
        { bizName: 'quarter', name: '季度', showType: 'DATE', type: 'STRING' },
        { bizName: 'sales', name: '销售额', showType: 'NUMBER', type: 'NUMBER' }
      ],
      queryResults: [
        { quarter: '2023-Q1', sales: 15000 },
        { quarter: '2023-Q2', sales: 18500 },
        { quarter: '2023-Q3', sales: 22000 },
        { quarter: '2023-Q4', sales: 25000 }
      ],
      aggregateInfo: {},
      chatContext: {},
      entityInfo: {},
      queryAuthorization: {},
      queryText: 'SELECT quarter, SUM(sales) as sales FROM sales_data GROUP BY quarter ORDER BY quarter',
      response: {},
      similarQueries: [],
      recommendedDimensions: [],
      errorMsg: ''
    }
  }
}; 