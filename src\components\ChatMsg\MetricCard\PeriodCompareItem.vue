<template>
  <div :class="`${prefixCls}-period-compare-item`">
    <div :class="`${prefixCls}-period-compare-item-title`">{{ title }}</div>
    <div :class="[
      `${prefixCls}-period-compare-item-value`,
      {
        [`${prefixCls}-period-compare-item-value-up`]: !value.includes('-'),
        [`${prefixCls}-period-compare-item-value-down`]: value.includes('-')
      }
    ]">
      <icon-font :type="!value.includes('-') ? 'icon-shangsheng' : 'icon-xiajiang'" />
      <div>{{ value }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { PREFIX_CLS } from '../../../common/constants';
import IconFont from '../../IconFont/index.vue';

export default defineComponent({
  name: 'PeriodCompareItem',
  components: {
    IconFont
  },
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: String,
      required: true
    }
  },
  setup() {
    const prefixCls = `${PREFIX_CLS}-metric-card`;

    return {
      prefixCls
    };
  }
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
