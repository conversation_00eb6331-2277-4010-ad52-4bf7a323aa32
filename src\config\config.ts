export const DIFY_API_PREFIX = "http://192.168.3.198";

export const API_PREFIX = "/api";

export let KK_SERVER_URL = "";

export let KK_LOCAL_URL = "";

export let IS_MICRO = true; //是否是微服务模式

// 获取KK_SERVER_URL
export function getKKServerUrl(): string {
  return KK_SERVER_URL;
}

// 设置KK_SERVER_URL
export function setKKServerUrl(url: string): void {
  KK_SERVER_URL = url;
}

// 获取KK_LOCAL_URL
export function getKKLocalUrl(): string {
  return KK_LOCAL_URL;
}

// 设置KK_LOCAL_URL
export function setKKLocalUrl(url: string): void {
  KK_LOCAL_URL = url;
}

export function setISMICRO(isMicro: boolean) {
  IS_MICRO = isMicro;
}

export function getISMICRO(): boolean {
  return IS_MICRO;
}

/**
 * 自动适配不同的后端架构
 * 1. 例如 /act/oa/task ,在微服务架构保持不变,在单体架构编程 /admin/oa/task
 * 2. 特殊 /gen/xxx ,在微服务架构、单体架构编程 都需保持不变
 *
 * @param originUrl 原始路径
 */
export const adaptationUrl = (originUrl?: string) => {
  // 微服务架构 不做路径转换,为空不做路径转换
  if (IS_MICRO) {
    return originUrl;
  }

  // 转为 /admin 路由前缀的请求
  return `/admin/${originUrl?.split("/").splice(2).join("/")}`;
};
