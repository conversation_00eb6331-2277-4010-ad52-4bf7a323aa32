import { Meta, StoryFn } from "@storybook/vue3";
import Tools from "./index.vue";
import { CLS_PREFIX } from "../../common/constants";

export default {
  title: "Components/Tools",
  component: Tools,
  tags: ["autodocs"],
  argTypes: {
    queryId: {
      control: { type: "number" },
      description: "查询ID",
    },
    scoreValue: {
      control: { type: "number" },
      description: "评分值",
    },
    isLastMessage: {
      control: { type: "boolean" },
      description: "是否为最后一条消息",
    },
    isParserError: {
      control: { type: "boolean" },
      description: "是否存在解析错误",
    },
    isSimpleMode: {
      control: { type: "boolean" },
      description: "是否为简单模式",
    },
    onExportData: {
      action: "onExportData",
      description: "导出数据的回调函数",
    },
    onReExecute: {
      action: "onReExecute",
      description: "重新执行的回调函数",
    },
  },
  parameters: {
    docs: {
      description: {
        component: "工具组件，提供点赞、导出数据、导出图片和重新执行等功能",
      },
    },
  },
} as Meta<typeof Tools>;

const Template: StoryFn<typeof Tools> = args => ({
  components: { Tools },
  setup() {
    return { args, CLS_PREFIX };
  },
  template: `
    <div style="padding: 20px;">
      <Tools v-bind="args" />
    </div>
  `,
});

export const 默认 = Template.bind({});
默认.args = {
  queryId: 1,
  scoreValue: 0,
  isLastMessage: true,
  isParserError: false,
  isSimpleMode: false,
};

export const 已点赞 = Template.bind({});
已点赞.args = {
  queryId: 1,
  scoreValue: 5,
  isLastMessage: true,
  isParserError: false,
  isSimpleMode: false,
};

export const 已踩 = Template.bind({});
已踩.args = {
  queryId: 1,
  scoreValue: 1,
  isLastMessage: true,
  isParserError: false,
  isSimpleMode: false,
};

export const 解析错误 = Template.bind({});
解析错误.args = {
  queryId: 1,
  scoreValue: 0,
  isLastMessage: true,
  isParserError: true,
  isSimpleMode: false,
};

export const 简单模式 = Template.bind({});
简单模式.args = {
  queryId: 1,
  scoreValue: 0,
  isLastMessage: true,
  isParserError: false,
  isSimpleMode: true,
};

export const 非最后消息 = Template.bind({});
非最后消息.args = {
  queryId: 1,
  scoreValue: 0,
  isLastMessage: false,
  isParserError: false,
  isSimpleMode: false,
};
