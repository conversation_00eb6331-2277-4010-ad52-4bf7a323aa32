import { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import MobileAgents from './index.vue';
import { AgentType } from '../type';

// 模拟Agent数据
const mockAgents: AgentType[] = [
  {
    id: 1,
    name: '数据分析助手',
    description: '专注于数据分析，帮助你理解和解释复杂的数据集。',
    examples: ['分析最近三个月的销售数据', '计算用户增长率', '对比不同地区的表现'],
    status: 1,
    createdBy: 'admin',
    updatedBy: 'admin',
    createdAt: '2023-01-01',
    updatedAt: '2023-03-15',
    enableSearch: 1,
    enableFeedback: 1,
    toolConfig: '',
    modelConfig: {
      provider: 'openai',
      baseUrl: 'https://api.openai.com',
      apiKey: '',
      modelName: 'gpt-4',
      temperature: 0.7,
      timeOut: 60000
    },
    multiTurnConfig: {
      enableMultiTurn: true
    },
    dataSetIds: [1, 2, 3]
  },
  {
    id: 2,
    name: '营销策略专家',
    description: '擅长制定营销策略，提供市场洞察和推广建议。',
    examples: ['如何提高品牌知名度', '设计社交媒体营销活动', '分析竞争对手的营销策略'],
    status: 1,
    createdBy: 'admin',
    updatedBy: 'admin',
    createdAt: '2023-02-10',
    updatedAt: '2023-04-20',
    enableSearch: 1,
    enableFeedback: 1,
    toolConfig: '',
    modelConfig: {
      provider: 'openai',
      baseUrl: 'https://api.openai.com',
      apiKey: '',
      modelName: 'gpt-4',
      temperature: 0.5,
      timeOut: 60000
    },
    multiTurnConfig: {
      enableMultiTurn: true
    },
    dataSetIds: [4, 5]
  },
  {
    id: 3,
    name: '客户服务顾问',
    description: '专注于客户服务，帮助解决用户问题和提升客户满意度。',
    examples: ['处理退款请求', '解决产品使用问题', '收集客户反馈'],
    status: 1,
    createdBy: 'admin',
    updatedBy: 'admin',
    createdAt: '2023-03-05',
    updatedAt: '2023-05-10',
    enableSearch: 1,
    enableFeedback: 1,
    toolConfig: '',
    modelConfig: {
      provider: 'openai',
      baseUrl: 'https://api.openai.com',
      apiKey: '',
      modelName: 'gpt-3.5-turbo',
      temperature: 0.3,
      timeOut: 30000
    },
    multiTurnConfig: {
      enableMultiTurn: true
    },
    dataSetIds: [6, 7]
  }
];

// 组件的Meta
const meta = {
  title: 'Chat/MobileAgents',
  component: MobileAgents,
  tags: ['autodocs'],
  argTypes: {
    open: { 
      control: 'boolean',
      description: '控制抽屉是否打开'
    },
    agentList: { 
      control: 'object',
      description: '智能助理列表'
    },
    currentAgent: { 
      control: 'object',
      description: '当前选中的智能助理'
    },
    onSelectAgent: { 
      action: 'selected',
      description: '选择智能助理时的回调函数'
    },
    onClose: { 
      action: 'closed',
      description: '关闭抽屉时的回调函数'
    }
  },
  args: {
    open: true,
    agentList: mockAgents,
    onSelectAgent: (agent: AgentType) => console.log('Selected agent:', agent),
    onClose: () => console.log('Drawer closed')
  }
} satisfies Meta<typeof MobileAgents>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基本示例 - 抽屉打开
export const 抽屉打开: Story = {
  args: {
    open: true,
    agentList: mockAgents,
    currentAgent: undefined
  }
};

// 有选中的智能助理
export const 选中智能助理: Story = {
  args: {
    open: true,
    agentList: mockAgents,
    currentAgent: mockAgents[1]
  }
};

// 抽屉关闭
export const 抽屉关闭: Story = {
  args: {
    open: false,
    agentList: mockAgents,
    currentAgent: undefined
  }
};

// 单个智能助理
export const 单个智能助理: Story = {
  args: {
    open: true,
    agentList: [mockAgents[0]],
    currentAgent: mockAgents[0]
  }
};

// 多个智能助理
export const 多个智能助理: Story = {
  args: {
    open: true,
    agentList: mockAgents,
    currentAgent: undefined
  }
}; 