import { Meta, StoryObj } from '@storybook/vue3';
import MetricTrendChart from './MetricTrendChart.vue';
import { ColumnType } from '../../../common/type';

const meta: Meta<typeof MetricTrendChart> = {
  component: MetricTrendChart,
  title: 'Components/ChatMsg/MetricTrendChart',
  tags: ['autodocs'],
  argTypes: {
    model: {
      control: 'text',
      description: '模型名称'
    },
    dateColumnName: {
      control: 'text',
      description: '日期列名'
    },
    categoryColumnName: {
      control: 'text',
      description: '分类列名'
    },
    metricField: {
      control: 'object',
      description: '指标字段'
    },
    resultList: {
      control: 'array',
      description: '结果列表'
    },
    triggerResize: {
      control: 'boolean',
      description: '触发图表重绘'
    },
    chartType: {
      control: 'select',
      options: ['line', 'bar'],
      description: '图表类型'
    }
  },
  parameters: {
    docs: {
      description: {
        component: '单指标趋势图组件，用于展示单个指标随时间的变化趋势'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof MetricTrendChart>;

// 基础趋势图
export const Basic: Story = {
  args: {
    model: '销售数据',
    dateColumnName: 'date',
    categoryColumnName: '',
    metricField: {
      name: '销售额',
      bizName: 'sales',
      showType: 'NUMBER',
      dataFormatType: 'decimal',
      dataFormat: {
        decimalPlaces: 2
      },
      authorized: true
    },
    resultList: [
      { date: '2023-01-01', sales: '15689234.56' },
      { date: '2023-01-02', sales: '16892345.67' },
      { date: '2023-01-03', sales: '18923456.78' },
      { date: '2023-01-04', sales: '17892345.89' },
      { date: '2023-01-05', sales: '19823456.90' }
    ],
    triggerResize: false,
    chartType: 'line'
  }
};

// 带分类的趋势图
export const WithCategory: Story = {
  args: {
    model: '销售数据',
    dateColumnName: 'date',
    categoryColumnName: 'category',
    metricField: {
      name: '销售额',
      bizName: 'sales',
      showType: 'NUMBER',
      dataFormatType: 'decimal',
      dataFormat: {
        decimalPlaces: 2
      },
      authorized: true
    },
    resultList: [
      { date: '2023-01-01', category: '产品A', sales: '15689234.56' },
      { date: '2023-01-01', category: '产品B', sales: '16892345.67' },
      { date: '2023-01-02', category: '产品A', sales: '18923456.78' },
      { date: '2023-01-02', category: '产品B', sales: '17892345.89' },
      { date: '2023-01-03', category: '产品A', sales: '19823456.90' },
      { date: '2023-01-03', category: '产品B', sales: '20892345.67' }
    ],
    triggerResize: false,
    chartType: 'line'
  }
};

// 柱状图
export const BarChart: Story = {
  args: {
    model: '销售数据',
    dateColumnName: 'date',
    categoryColumnName: '',
    metricField: {
      name: '销售额',
      bizName: 'sales',
      showType: 'NUMBER',
      dataFormatType: 'decimal',
      dataFormat: {
        decimalPlaces: 2
      },
      authorized: true
    },
    resultList: [
      { date: '2023-01-01', sales: '15689234.56' },
      { date: '2023-01-02', sales: '16892345.67' },
      { date: '2023-01-03', sales: '18923456.78' },
      { date: '2023-01-04', sales: '17892345.89' },
      { date: '2023-01-05', sales: '19823456.90' }
    ],
    triggerResize: false,
    chartType: 'bar'
  }
};

// 需要权限认证
export const NeedAuthorization: Story = {
  args: {
    model: '销售数据',
    dateColumnName: 'date',
    categoryColumnName: '',
    metricField: {
      name: '销售额',
      bizName: 'sales',
      showType: 'NUMBER',
      dataFormatType: 'decimal',
      dataFormat: {
        decimalPlaces: 2
      },
      authorized: false
    },
    resultList: [],
    triggerResize: false,
    chartType: 'line'
  }
};