<template>
  <div>
    <Drawer
      title="智能助理"
      placement="bottom"
      :open="open"
      height="85%"
      class="mobileAgents"
      @close="onClose"
    >
      <div class="agentListContent">
        <div
          v-for="(agent, index) in agentList"
          :key="agent.id"
          :class="['agentItem', { active: currentAgent?.id === agent.id }]"
          @click="handleSelectAgent(agent)"
        >
          <div class="agentTitleBar">
            <IconFont
              :type="AGENT_ICONS[index % AGENT_ICONS.length]"
              class="avatar"
            />
            <div class="agentName">{{ agent.name }}</div>
          </div>
          <div class="agentDesc">{{ agent.description }}</div>
        </div>
      </div>
    </Drawer>
  </div>
</template>

<script setup lang="ts">
import { Drawer } from 'ant-design-vue';
import IconFont from '../../components/IconFont/index.vue';
import { AGENT_ICONS } from '../constants';
import type { AgentType } from '../type';

const props = defineProps<{
  open: boolean;
  agentList: AgentType[];
  currentAgent?: AgentType;
  onSelectAgent: (agent: AgentType) => void;
  onClose: () => void;
}>();

const handleSelectAgent = (agent: AgentType) => {
  props.onSelectAgent(agent);
  props.onClose();
};
</script>

<style lang="less" scoped>
.mobileAgents {
  :deep(.ant-drawer-content) {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;

    .ant-drawer-header {
      padding: 16px 12px;
    }

    .ant-drawer-body {
      padding: 12px;
    }
  }

  .agentListContent {
    display: flex;
    flex-direction: column;
    row-gap: 12px;

    .agentItem {
      padding: 12px 16px;
      background-color: #f5f7f9;
      border: 1px solid transparent;
      border-radius: 10px;

      &.active {
        border: 1px solid var(--chat-blue);
      }

      .agentTitleBar {
        display: flex;
        align-items: center;
        column-gap: 6px;

        .avatar {
          font-size: 24px;
        }

        .agentName {
          color: var(--text-color);
          font-weight: 500;
        }
      }

      .agentDesc {
        margin-top: 8px;
        color: var(--text-color-third);
        font-size: 13px;
        line-height: 24px;
      }
    }
  }
}
</style> 