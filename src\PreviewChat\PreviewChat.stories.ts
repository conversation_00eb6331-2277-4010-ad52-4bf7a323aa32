import { <PERSON>a, <PERSON>Obj } from "@storybook/vue3";
import PreviewChat from "./index.vue";
import { AgentType } from "@/Chat/type";

const meta: Meta<typeof PreviewChat> = {
  title: "PreviewChat/PreviewChat",
  component: PreviewChat,
  tags: ["autodocs"],
  argTypes: {
    feedbackDisabled: { control: "boolean" },
    isDebugMode: { control: "boolean" },
    isUseUploadFile: { control: "boolean" },
  },
};

export default meta;
type Story = StoryObj<typeof PreviewChat>;

// 基础示例
export const Default: Story = {
  args: {
    feedbackDisabled: false,
    isDebugMode: true,
    isUseUploadFile: false,
    token: "db5dd52d-2479-41c9-a16c-0e21d92b4dbe",
    chartId: "1909059952639524866",
    kkServerUrl: "http://192.168.3.20:8012/onlinePreview",
    kkLocalUrl: "http://192.168.3.150:9999",
  },
  decorators: [
    () => ({
      template: '<div style="height: 600px;"><story /></div>',
      setup() {
        // 在组件挂载前模拟API
        return {};
      },
    }),
  ],
};
