import { fn } from "@storybook/test";
import type { Meta, StoryObj } from "@storybook/vue3";

import ChatFooter from "./index.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta = {
  title: "Chat/ChatFooter",
  component: ChatFooter,
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  argTypes: {
    onClick: fn(),
    sendMsg: fn(),
    toggleHistoryVisible: fn(),
    openAgents: fn(),
    addConversation: fn(),
    selectAgent: fn(),
    openShowcase: fn(),
    "update:inputMsg": fn(),
  },
  args: {
    inputMsg: "",
    chatId: 1,
    agentList: [
      {
        id: 1,
        name: "助手",
        description: "默认智能助手",
        avatar: ""
      },
      {
        id: 2,
        name: "分析师",
        description: "数据分析专家",
        avatar: ""
      }
    ],
    width: "500px",
    height: "auto",
  },
} satisfies Meta<typeof ChatFooter>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础使用
export const Default: Story = {
  args: {
    inputMsg: "",
  },
};

// 带有当前会话的助手
export const WithCurrentAgent: Story = {
  args: {
    currentAgent: {
      id: 1,
      name: "助手",
      description: "默认智能助手",
      avatar: ""
    },
  },
};

// 移动端样式
export const Mobile: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    },
  },
};