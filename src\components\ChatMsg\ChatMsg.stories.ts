import { Meta, StoryObj } from '@storybook/vue3';
import ChatMsg from './index.vue';
import { MsgContentTypeEnum } from '../../common/constants';

const meta: Meta<typeof ChatMsg> = {
  title: 'Components/ChatMsg',
  component: ChatMsg,
  tags: ['autodocs'],
  argTypes: {
    msgContentTypeChange: { action: 'msgContentTypeChange' },
    queryId: { control: 'number' },
    question: { control: 'text' },
    chartIndex: { control: 'number' },
    triggerResize: { control: 'boolean' },
    forceShowTable: { control: 'boolean' },
    isSimpleMode: { control: 'boolean' },
  },
};

export default meta;
type Story = StoryObj<typeof ChatMsg>;

// 模拟数据
const mockData = {
  id: 123,
  question: '过去30天销售额是多少?',
  queryId: 456,
  queryState: 'SUCCESS',
  queryMode: 'METRIC_AGG',
  queryText: 'SELECT sum(sales) FROM sales_table WHERE date >= "2023-01-01" AND date <= "2023-01-31"',
  textResult: '过去30天销售额为100000元',
  textSummary: '',
  errorMsg: '',
  similarQueries: [],
  queryResults: [
    { date: '2023-01-01', sales: 1000 },
    { date: '2023-01-02', sales: 1200 },
    { date: '2023-01-03', sales: 900 },
    { date: '2023-01-04', sales: 1500 },
    { date: '2023-01-05', sales: 2000 },
  ],
  queryColumns: [
    { authorized: true, name: '日期', nameEn: 'date', bizName: 'date', showType: 'DATE', type: 'DATE', dataFormatType: 'date', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
    { authorized: true, name: '销售额', nameEn: 'sales', bizName: 'sales', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 2, needMultiply100: false } },
  ],
  recommendedDimensions: [
    { id: 1, model: 101, name: '地区', bizName: 'region' },
    { id: 2, model: 101, name: '产品', bizName: 'product' },
  ],
  chatContext: {
    id: 789,
    dateInfo: {
      dateMode: 'RECENT',
      unit: 30,
    },
    dimensions: [],
    metrics: [
      { id: 3, model: 101, name: '销售额', bizName: 'sales' },
      { id: 4, model: 101, name: '订单数', bizName: 'orders' },
    ],
    dimensionFilters: [],
    elementMatches: [],
  },
  entityInfo: {
    dataSetInfo: {
      id: 101,
      name: '销售数据',
      nameEn: 'sales_data',
    },
    dimensions: [
      { id: 1, model: 101, name: '地区', bizName: 'region' },
      { id: 2, model: 101, name: '产品', bizName: 'product' },
    ],
    metrics: [
      { id: 3, model: 101, name: '销售额', bizName: 'sales' },
      { id: 4, model: 101, name: '订单数', bizName: 'orders' },
    ],
    entityId: 101,
  },
  queryAuthorization: { authorized: true },
  aggregateInfo: {},
  response: {},
  parseInfos: [],
};

// 基础示例
export const Default: Story = {
  args: {
    queryId: 456,
    question: '过去30天销售额是多少?',
    data: mockData,
    chartIndex: 0,
  },
};

// 指标卡片示例
export const MetricCard: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      queryResults: [{ sales: 100000 }],
      queryColumns: [
        { authorized: true, name: '销售额', nameEn: 'sales', bizName: 'sales', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 2, needMultiply100: false } },
      ],
    },
  },
};

// 趋势图示例
export const MetricTrend: Story = {
  args: {
    ...Default.args,
  },
};

// 表格示例
export const Table: Story = {
  args: {
    ...Default.args,
    forceShowTable: true,
  },
};

// 柱状图示例
export const BarChart: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      queryResults: [
        { region: '华北', sales: 5000 },
        { region: '华东', sales: 6000 },
        { region: '华南', sales: 4500 },
        { region: '西北', sales: 3500 },
      ],
      queryColumns: [
        { authorized: true, name: '地区', nameEn: 'region', bizName: 'region', showType: 'CATEGORY', type: 'CATEGORY', dataFormatType: 'string', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '销售额', nameEn: 'sales', bizName: 'sales', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 2, needMultiply100: false } },
      ],
    },
  },
};

// Markdown 示例
export const Markdown: Story = {
  args: {
    ...Default.args,
    isSimpleMode: true,
    data: {
      ...mockData,
      textResult: `# 销售数据分析
      
## 过去30天销售额情况
      
- 总销售额：100,000元
- 平均每日销售额：3,333元
- 最高销售日：1月5日，销售额2,000元
- 最低销售日：1月3日，销售额900元

销售额整体呈**上升趋势**，建议关注1月5日的销售策略，可能对提升销售有积极影响。`,
    },
  },
};

// 无数据示例
export const NoData: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      queryResults: [],
    },
  },
};

// 移动端视图示例
export const MobileView: Story = {
  args: {
    ...Default.args,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    mockData: {
      isMobile: true,
    },
  },
};

// 加载状态示例
export const Loading: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      queryState: 'LOADING',
    },
  },
  parameters: {
    mockData: {
      queryData: () => new Promise(resolve => setTimeout(() => {
        resolve({ code: 200, data: mockData });
      }, 3000))
    }
  }
};

// 错误状态示例
export const Error: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      queryState: 'ERROR',
      errorMsg: '查询失败，请稍后重试',
      queryResults: [],
    },
  },
};

// 指标选择示例
export const MetricSelection: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      chatContext: {
        ...mockData.chatContext,
        metrics: [
          { id: 3, model: 101, name: '销售额', bizName: 'sales' },
          { id: 4, model: 101, name: '订单数', bizName: 'orders' },
          { id: 5, model: 101, name: '客单价', bizName: 'avgOrderValue' },
          { id: 6, model: 101, name: '转化率', bizName: 'conversionRate' },
        ],
      },
    },
  },
};

// 维度下钻示例
export const DimensionDrillDown: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      queryMode: 'METRIC_DRILL',
      recommendedDimensions: [
        { id: 1, model: 101, name: '地区', bizName: 'region' },
        { id: 2, model: 101, name: '产品', bizName: 'product' },
        { id: 7, model: 101, name: '渠道', bizName: 'channel' },
        { id: 8, model: 101, name: '品类', bizName: 'category' },
      ],
    },
  },
  parameters: {
    mockData: {
      queryData: ({ dimensions }: { dimensions?: Array<{ id: number, model: number, name: string, bizName: string }> }) => {
        if (dimensions && dimensions.length > 0) {
          const dimension = dimensions[dimensions.length - 1];
          const newData = {
            ...mockData,
            queryResults: [
              { [dimension.bizName]: '选项1', sales: 3500 },
              { [dimension.bizName]: '选项2', sales: 4200 },
              { [dimension.bizName]: '选项3', sales: 2800 },
              { [dimension.bizName]: '选项4', sales: 5100 },
            ],
            queryColumns: [
              { authorized: true, name: dimension.name, nameEn: dimension.bizName, bizName: dimension.bizName, showType: 'CATEGORY', type: 'CATEGORY', dataFormatType: 'string', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
              { authorized: true, name: '销售额', nameEn: 'sales', bizName: 'sales', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 2, needMultiply100: false } },
            ]
          };
          return Promise.resolve({ code: 200, data: newData });
        }
        return Promise.resolve({ code: 200, data: mockData });
      }
    }
  }
};

// 复合图表示例（多种指标在一个图表中）
export const CompositeChart: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      queryResults: [
        { date: '2023-01-01', sales: 1000, orders: 120, users: 350 },
        { date: '2023-01-02', sales: 1200, orders: 140, users: 380 },
        { date: '2023-01-03', sales: 900, orders: 100, users: 320 },
        { date: '2023-01-04', sales: 1500, orders: 180, users: 450 },
        { date: '2023-01-05', sales: 2000, orders: 220, users: 510 },
      ],
      queryColumns: [
        { authorized: true, name: '日期', nameEn: 'date', bizName: 'date', showType: 'DATE', type: 'DATE', dataFormatType: 'date', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '销售额', nameEn: 'sales', bizName: 'sales', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 2, needMultiply100: false } },
        { authorized: true, name: '订单数', nameEn: 'orders', bizName: 'orders', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '用户数', nameEn: 'users', bizName: 'users', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
      ],
    },
  },
};

// 自定义日期范围示例
export const CustomDateRange: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      chatContext: {
        ...mockData.chatContext,
        dateInfo: {
          dateMode: 'CUSTOM',
          startDate: '2023-01-01',
          endDate: '2023-01-31',
        },
      },
    },
  },
};

// 大数据量表格示例
export const LargeDataTable: Story = {
  args: {
    ...Default.args,
    forceShowTable: true,
    data: {
      ...mockData,
      queryResults: Array(100).fill(0).map((_, index) => ({
        id: index + 1,
        date: `2023-01-${(index % 30) + 1}`,
        region: ['华北', '华东', '华南', '西北'][index % 4],
        product: ['产品A', '产品B', '产品C'][index % 3],
        sales: Math.floor(Math.random() * 10000),
        orders: Math.floor(Math.random() * 1000),
      })),
      queryColumns: [
        { authorized: true, name: 'ID', nameEn: 'id', bizName: 'id', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '日期', nameEn: 'date', bizName: 'date', showType: 'DATE', type: 'DATE', dataFormatType: 'date', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '地区', nameEn: 'region', bizName: 'region', showType: 'CATEGORY', type: 'CATEGORY', dataFormatType: 'string', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '产品', nameEn: 'product', bizName: 'product', showType: 'CATEGORY', type: 'CATEGORY', dataFormatType: 'string', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '销售额', nameEn: 'sales', bizName: 'sales', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 2, needMultiply100: false } },
        { authorized: true, name: '订单数', nameEn: 'orders', bizName: 'orders', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
      ],
    },
  },
};

// 交叉表示例
export const CrossTable: Story = {
  args: {
    ...Default.args,
    forceShowTable: true,
    data: {
      ...mockData,
      queryMode: 'CROSS_TABLE',
      queryResults: [
        { region: '华北', '产品A': 1200, '产品B': 1500, '产品C': 1800 },
        { region: '华东', '产品A': 2200, '产品B': 2500, '产品C': 2800 },
        { region: '华南', '产品A': 1700, '产品B': 1900, '产品C': 2100 },
        { region: '西北', '产品A': 1000, '产品B': 1300, '产品C': 1600 },
      ],
      queryColumns: [
        { authorized: true, name: '地区', nameEn: 'region', bizName: 'region', showType: 'CATEGORY', type: 'CATEGORY', dataFormatType: 'string', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '产品A', nameEn: 'productA', bizName: '产品A', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '产品B', nameEn: 'productB', bizName: '产品B', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
        { authorized: true, name: '产品C', nameEn: 'productC', bizName: '产品C', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 0, needMultiply100: false } },
      ],
    },
  },
};

// 带格式的指标卡片示例
export const FormattedMetricCard: Story = {
  args: {
    ...Default.args,
    data: {
      ...mockData,
      queryResults: [{ sales: 12345678.9 }],
      queryColumns: [
        { 
          authorized: true, 
          name: '销售额', 
          nameEn: 'sales', 
          bizName: 'sales', 
          showType: 'NUMBER', 
          type: 'NUMBER', 
          dataFormatType: 'currency', 
          dataFormat: { 
            decimalPlaces: 2, 
            needMultiply100: false,
            currencySymbol: '¥'
          } 
        },
      ],
    },
  },
}; 