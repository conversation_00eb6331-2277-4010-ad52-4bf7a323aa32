<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}-indicator`">
      <div style="display: flex; align-items: flex-end">
        <div :class="`${prefixCls}-indicator-value`">
          {{ formattedValue }}
        </div>
        <div v-if="!isNaN(+value) && +value >= 10000" :class="`${prefixCls}-indicator-switch`">
          <SwapOutlined @click="handleNumberClick" />
        </div>
      </div>
      <div :class="`${prefixCls}-bottom-section`">
        <div :class="`${prefixCls}-date`">
          最新数据日期：<span :class="`${prefixCls}-date-value`">{{ date }}</span>
        </div>
        <div v-if="metricInfos?.length > 0" :class="`${prefixCls}-period-compare`">
          <PeriodCompareItem 
            v-for="(val, key) in statistics" 
            :key="key"
            :title="String(key)" 
            :value="val" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { PREFIX_CLS } from '../../../common/constants';
import { formatByDecimalPlaces, formatMetric, formatNumberWithCN } from '../../../utils/utils';
import type { AggregateInfoType, ColumnType } from '../../../common/type';
import PeriodCompareItem from '../MetricCard/PeriodCompareItem.vue';
import { SwapOutlined } from '@ant-design/icons-vue';

const props = defineProps<{
  aggregateInfo: AggregateInfoType;
  currentMetricField: ColumnType;
}>();

const prefixCls = `${PREFIX_CLS}-metric-info`;
const isNumber = ref(false);

// 从传入的数据中提取所需信息
const metricInfos = computed(() => props.aggregateInfo?.metricInfos || []);
const metricInfo = computed(() => metricInfos.value?.[0] || {});
const date = computed(() => metricInfo.value?.date || '');
const value = computed(() => metricInfo.value?.value);
const statistics = computed(() => metricInfo.value?.statistics || {});
const dataFormatType = computed(() => props.currentMetricField?.dataFormatType);
const dataFormat = computed(() => props.currentMetricField?.dataFormat);

// 格式化显示的值
const formattedValue = computed(() => {
  if (dataFormatType.value === 'percent' || dataFormatType.value === 'decimal') {
    return `${formatByDecimalPlaces(
      dataFormat.value?.needMultiply100 ? +value.value * 100 : value.value,
      dataFormat.value?.decimalPlaces || 2
    )}${dataFormatType.value === 'percent' ? '%' : ''}`;
  } else {
    return isNumber.value
      ? formatMetric(value.value)
      : formatNumberWithCN(+value.value);
  }
});

// 切换数字显示格式
const handleNumberClick = () => {
  isNumber.value = !isNumber.value;
};
</script>

<style lang="less" scoped>
@import './style.less';
</style>
