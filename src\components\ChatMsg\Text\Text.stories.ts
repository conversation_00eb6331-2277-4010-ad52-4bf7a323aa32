import { Meta, StoryObj } from '@storybook/vue3';
import Text from './index.vue';

// 更多关于默认导出：https://storybook.js.org/docs/vue/writing-stories/introduction
const meta = {
  title: 'Components/ChatMsg/Text',
  component: Text,
  // 此处定义组件所有可用属性
  argTypes: {
    columns: { control: 'object' },
    referenceColumn: { control: 'object' },
    dataSource: { control: 'object' },
  },
  // 参数的默认值
  args: {
    columns: [{ bizName: 'text' }],
    referenceColumn: { bizName: 'references' },
    dataSource: [{ text: '这是一个基本文本示例' }]
  },
} satisfies Meta<typeof Text>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const 基础示例: Story = {
  args: {
    columns: [{ bizName: 'text' }],
    referenceColumn: undefined,
    dataSource: [{ text: '这是一个简单的文本组件，用于展示聊天内容。' }]
  },
};

// 带参考资料的示例
export const 带参考资料: Story = {
  args: {
    columns: [{ bizName: 'text' }],
    referenceColumn: { bizName: 'references' },
    dataSource: [{ 
      text: '根据参考资料，我们可以了解到更多相关信息。', 
      references: [
        {
          doc_title: '公司简介',
          doc_chunk: '我们是一家专注于人工智能对话系统开发的技术公司，成立于2020年。'
        },
        {
          doc_title: '产品说明',
          doc_chunk: '此对话系统支持多种数据源引用，可以为用户提供基于事实的准确回答。'
        }
      ]
    }]
  },
};

// 多条参考资料示例
export const 多条参考资料: Story = {
  args: {
    columns: [{ bizName: 'text' }],
    referenceColumn: { bizName: 'references' },
    dataSource: [{ 
      text: '以下是关于人工智能多个领域的相关信息。', 
      references: [
        {
          doc_title: '机器学习基础',
          doc_chunk: '机器学习是人工智能的一个分支，主要研究如何使计算机系统通过经验自动改进。'
        },
        {
          doc_title: '深度学习技术',
          doc_chunk: '深度学习是机器学习的子集，使用多层神经网络进行特征学习和模式识别。'
        },
        {
          doc_title: '自然语言处理',
          doc_chunk: '自然语言处理(NLP)是计算机科学和人工智能的一个领域，关注计算机与人类语言之间的交互。'
        },
        {
          doc_title: '计算机视觉',
          doc_chunk: '计算机视觉是一个跨学科领域，研究如何使计算机从数字图像或视频中获取高层次的理解。'
        }
      ]
    }]
  },
};

// 无数据示例
export const 无数据: Story = {
  args: {
    columns: [{ bizName: 'text' }],
    referenceColumn: { bizName: 'references' },
    dataSource: [{ }]
  },
}; 