import { <PERSON>a, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import SwitchEntity from './SwitchEntity.vue';
import { 
  ChatContextType, 
  FilterItemType,
  ModelType
} from '../../common/type';
import { ChatContextTypeQueryTypeEnum } from '../../common/constants';

// 模拟数据
const mockDimensionFilters: FilterItemType[] = [
  {
    elementID: 101,
    name: '歌曲',
    bizName: 'zyqk_song_id',
    operator: '=',
    value: 'song123',
    type: 'STRING'
  },
  {
    elementID: 102,
    name: '歌手',
    bizName: 'singer_id',
    operator: '=',
    value: 'singer456',
    type: 'STRING'
  }
];

const mockModel: ModelType = {
  alias: 'model_alias',
  bizName: 'model_bizname',
  id: 100,
  model: 100,
  name: '模型名称',
  modelNames: ['模型名称'],
  type: 'MODEL',
  useCnt: 10
};

// 创建模拟的ChatContext
const createMockChatContext = (withFilters: boolean): ChatContextType => ({
  id: 1,
  queryId: 123,
  aggType: 'SUM',
  modelId: 100,
  modelName: '音乐模型',
  dataSet: mockModel,
  dateInfo: {
    dateList: [],
    dateMode: 'ABSOLUTE',
    period: 'day',
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    text: '2023年全年',
    unit: 1
  },
  dimensions: [],
  metrics: [],
  entity: { alias: ['音乐'], id: 1 },
  entityInfo: {
    dataSetInfo: {
      bizName: 'music',
      itemId: 1,
      name: '音乐',
      primaryEntityBizName: 'music',
      value: 'music',
      words: ['音乐']
    },
    dimensions: [],
    metrics: [],
    entityId: 1
  },
  elementMatches: [],
  nativeQuery: false,
  queryMode: 'SQL',
  queryType: ChatContextTypeQueryTypeEnum.AGGREGATE,
  dimensionFilters: withFilters ? mockDimensionFilters : [],
  properties: {},
  sqlInfo: {
    parsedS2SQL: '',
    correctedS2SQL: '',
    querySQL: ''
  },
  textInfo: '查询音乐相关数据'
});

const meta = {
  title: 'ChatItem/SwitchEntity',
  component: SwitchEntity,
  tags: ['autodocs'],
  argTypes: {
    entityName: { control: 'text' },
    chatContext: { control: 'object' },
    onSwitchEntity: { action: 'switched' }
  },
  parameters: {
    backgrounds: {
      default: 'light',
    },
  },
} satisfies Meta<typeof SwitchEntity>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Basic: Story = {
  args: {
    entityName: '周杰伦',
    chatContext: createMockChatContext(true),
    onSwitchEntity: fn()
  }
};

// 没有选中实体的例子
export const NoSelectedEntity: Story = {
  args: {
    entityName: '请选择歌曲',
    chatContext: createMockChatContext(false),
    onSwitchEntity: fn()
  }
};

// 长实体名称的例子
export const LongEntityName: Story = {
  args: {
    entityName: '周杰伦的床边故事专辑中的说好不哭',
    chatContext: createMockChatContext(true),
    onSwitchEntity: fn()
  }
};

// 歌手实体例子
export const SingerEntity: Story = {
  args: {
    entityName: '林俊杰',
    chatContext: {
      ...createMockChatContext(true),
      dimensionFilters: [
        {
          elementID: 102,
          name: '歌手',
          bizName: 'singer_id',
          operator: '=',
          value: 'singer789',
          type: 'STRING'
        }
      ]
    },
    onSwitchEntity: fn()
  }
}; 