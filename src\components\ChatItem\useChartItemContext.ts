import { inject, provide } from 'vue';

export interface ChartItemContext {
  register: (key: string, value: any) => void;
}

export const ChartItemContextKey = Symbol('ChartItemContext');

export function useChartItemContext() {
  const context = inject<ChartItemContext>(ChartItemContextKey);
  if (!context) {
    throw new Error('useChartItemContext must be used within a ChartItemProvider');
  }
  return context;
}

export function provideChartItemContext(context: ChartItemContext) {
  provide(ChartItemContextKey, context);
} 