import type {
  IOnCompleted,
  IOnData,
  IOnError,
  IOnFile,
  IOnMessageEnd,
  IOnMessageReplace,
  IOnNodeFinished,
  IOnNodeStarted,
  IOnThought,
  IOnWorkflowFinished,
  IOnWorkflowStarted,
} from "./base";
import { del, get, post, ssePost } from "./base";

export const sendChatMessage = async (
  body: Record<string, any>,
  {
    onData,
    onCompleted,
    onThought,
    onFile,
    onError,
    getAbortController,
    onMessageEnd,
    onMessageReplace,
    onWorkflowStarted,
    onNodeStarted,
    onNodeFinished,
    onWorkflowFinished,
  }: {
    onData: IOnData;
    onCompleted: IOnCompleted;
    onFile: IOnFile;
    onThought: IOnThought;
    onMessageEnd: IOnMessageEnd;
    onMessageReplace: IOnMessageReplace;
    onError: IOnError;
    getAbortController?: (abortController: AbortController) => void;
    onWorkflowStarted: IOnWorkflowStarted;
    onNodeStarted: IOnNodeStarted;
    onNodeFinished: IOnNodeFinished;
    onWorkflowFinished: IOnWorkflowFinished;
  }
) => {
  return ssePost(
    "/knowledge/kbAssistantConfig/chat-messages",
    // "chat-messages",
    {
      body: {
        ...body,
        response_mode: "streaming",
      },
    },
    {
      onData,
      onCompleted,
      onThought,
      onFile,
      onError,
      getAbortController,
      onMessageEnd,
      onMessageReplace,
      onNodeStarted,
      onWorkflowStarted,
      onWorkflowFinished,
      onNodeFinished,
    }
  );
};

export const fetchConversations = async (chartId?: string) => {
  return get("/knowledge/kbAssistantConfig/conversations", {
    params: { limit: 100, first_id: "", chartId },
  });
};

export const fetchChatList = async (
  conversationId: string,
  chartId: string
) => {
  return get("/knowledge/kbAssistantConfig/messages", {
    params: {
      conversationId: conversationId,
      limit: 20,
      last_id: "",
      chartId,
    },
  });
};

// init value. wait for server update
export const fetchAppParams = async (chartId: string) => {
  return get("/knowledge/kbAssistantConfig/getParameters", {
    params: { chartId },
  });
};

export const fetchAppInfo = async () => {
  return get("info");
};

export const updateFeedback = async (params: {
  chartId: string;
  messageId: string;
  rating?: string | null;
  content?: string;
}) => {
  return post(`/knowledge/kbAssistantConfig/feedbacks`, {
    body: params,
  });
};

export const generationConversationName = async (id: string) => {
  return post(`conversations/${id}/name`, { body: { auto_generate: true } });
};

export const deleteConversation = async (params: {
  chartId: string;
  conversationId: string;
}) => {
  return del(
    `/knowledge/kbAssistantConfig/conversations?chartId=${params.chartId}&conversationId=${params.conversationId}`,
    {}
  );
};
