<template>
  <span :class="`${prefixCls}-loading`">
    <span :class="`${prefixCls}-loading-dot`" />
    <span :class="`${prefixCls}-loading-dot`" />
    <span :class="`${prefixCls}-loading-dot`" />
  </span>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { PREFIX_CLS } from "../../common/constants";

export default defineComponent({
  name: "Loading",
  setup() {
    const prefixCls = `${PREFIX_CLS}-item`;

    return {
      prefixCls,
    };
  },
});
</script>

<style lang="less" scoped>
@import "./style.less";
</style>
