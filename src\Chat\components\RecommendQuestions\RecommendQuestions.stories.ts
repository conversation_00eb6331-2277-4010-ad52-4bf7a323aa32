import { fn } from "@storybook/test";
import type { Meta, StoryObj } from "@storybook/vue3";

import RecommendQuestions from "./index.vue";

// 模拟依赖
// 在实际使用时，Storybook应该配置MSW或其他模拟方式
// 这里我们只在组件级别模拟数据

// 默认推荐问题数据
const mockRecommendData = [
  {
    recommendedQuestions: [
      { question: "怎么查看销售数据?" },
      { question: "如何分析用户行为?" },
      { question: "最近一周的转化率是多少?" },
      { question: "如何提升页面访问量?" },
      { question: "用户留存率怎么计算?" }
    ]
  },
  {
    recommendedQuestions: [
      { question: "如何设置数据看板?" },
      { question: "如何导出报表?" },
      { question: "什么是UV和PV?" },
      { question: "如何分析用户增长趋势?" },
      { question: "如何查询特定时间段的数据?" }
    ]
  }
];

// 定义组件的Meta
const meta = {
  title: "Chat/RecommendQuestions",
  component: RecommendQuestions,
  tags: ["autodocs"],
  argTypes: {
    onSelectQuestion: { action: "onSelectQuestion", description: "当用户点击推荐问题时触发" }
  },
  parameters: {
    docs: {
      description: {
        component: "推荐问题组件，展示系统推荐的问题列表"
      }
    }
  },
  decorators: [
    () => ({
      template: '<div style="padding: 20px;"><story /></div>'
    })
  ]
} satisfies Meta<typeof RecommendQuestions>;

export default meta;
type Story = StoryObj<typeof meta>;

// 模拟组件使用的服务和工具
// 在实际的stories中，我们可以使用Storybook的装饰器或MSW来模拟这些依赖
// 但为了简单起见，我们这里直接在组件中改写这些函数

// 默认场景：显示推荐问题列表
export const Default: Story = {
  args: {
    onSelectQuestion: fn()
  },
  render: (args) => ({
    components: { RecommendQuestions },
    setup() {
      const handleSelectQuestion = (question: string) => {
        console.log(`选择了问题: ${question}`);
        // @ts-ignore - Storybook的action函数在实际环境中会被正确处理
        args.onSelectQuestion(question);
      };

      return { 
        onSelectQuestion: handleSelectQuestion 
      };
    },
    template: `
      <RecommendQuestions :onSelectQuestion="onSelectQuestion" />
    `,
  }),
  parameters: {
    mockData: {
      queryRecommendQuestions: () => Promise.resolve({ data: mockRecommendData }),
      isMobile: false
    }
  }
};

// 场景：没有推荐问题
export const WithoutQuestions: Story = {
  args: {
    onSelectQuestion: fn()
  },
  render: (args) => ({
    components: { RecommendQuestions },
    setup() {
      const handleSelectQuestion = (question: string) => {
        // @ts-ignore - Storybook的action函数在实际环境中会被正确处理
        args.onSelectQuestion(question);
      };

      return { 
        onSelectQuestion: handleSelectQuestion 
      };
    },
    template: `
      <RecommendQuestions :onSelectQuestion="onSelectQuestion" />
    `
  }),
  parameters: {
    mockData: {
      queryRecommendQuestions: () => Promise.resolve({ data: [] }),
      isMobile: false
    }
  }
};

// 场景：加载中状态
export const Loading: Story = {
  args: {
    onSelectQuestion: fn()
  },
  render: (args) => ({
    components: { RecommendQuestions },
    setup() {
      const handleSelectQuestion = (question: string) => {
        // @ts-ignore - Storybook的action函数在实际环境中会被正确处理
        args.onSelectQuestion(question);
      };

      return { 
        onSelectQuestion: handleSelectQuestion
      };
    },
    template: `
      <RecommendQuestions :onSelectQuestion="onSelectQuestion" />
    `
  }),
  parameters: {
    mockData: {
      // 创建一个永远不会resolve的Promise来模拟加载状态
      queryRecommendQuestions: () => new Promise(() => {}),
      isMobile: false
    }
  }
};

// 场景：移动端视图
export const MobileView: Story = {
  args: {
    onSelectQuestion: fn()
  },
  render: (args) => ({
    components: { RecommendQuestions },
    setup() {
      const handleSelectQuestion = (question: string) => {
        // @ts-ignore - Storybook的action函数在实际环境中会被正确处理
        args.onSelectQuestion(question);
      };

      return { 
        onSelectQuestion: handleSelectQuestion
      };
    },
    template: `
      <RecommendQuestions :onSelectQuestion="onSelectQuestion" />
    `
  }),
  parameters: {
    mockData: {
      queryRecommendQuestions: () => Promise.resolve({ data: mockRecommendData }),
      isMobile: true
    }
  }
}; 