import { Meta, StoryObj } from '@storybook/vue3';
import Text from './Text.vue';

const meta = {
  title: 'ChatItem/Text',
  component: Text,
  tags: ['autodocs'],
  argTypes: {
    data: { control: 'text' }
  },
  parameters: {
    backgrounds: {
      default: 'light',
    },
  },
} satisfies Meta<typeof Text>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础文本示例
export const BasicText: Story = {
  args: {
    data: '这是一条普通的文本消息'
  }
};

// 长文本示例
export const LongText: Story = {
  args: {
    data: '这是一条很长的文本消息，它包含了很多文字，可能会导致文本气泡变得很宽。在真实场景中，用户可能会发送这样的长消息来表达复杂的想法或描述详细的问题。处理这种长文本是聊天界面设计中的重要考虑因素。'
  }
};

// 包含特殊字符的文本
export const TextWithSpecialChars: Story = {
  args: {
    data: '消息可以包含特殊字符: !@#$%^&*()_+-=[]{}|;:\'",.<>/?~`'
  }
};

// 包含数字和英文的文本
export const TextWithNumbersAndEnglish: Story = {
  args: {
    data: '这条消息包含数字123和英文单词hello world'
  }
};

// 空文本示例
export const EmptyText: Story = {
  args: {
    data: ''
  }
}; 