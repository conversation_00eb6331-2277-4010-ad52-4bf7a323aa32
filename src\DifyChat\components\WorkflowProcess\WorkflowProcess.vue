<template>
  <div
    :class="[
      'workflow-process',
      collapse ? 'workflow-process-collapsed' : 'workflow-process-expanded',
      running && !collapse && 'workflow-process-running',
      succeeded && !collapse && 'workflow-process-succeeded',
      failed && !collapse && 'workflow-process-failed',
      collapse && 'workflow-process-bg',
    ]"
  >
    <div
      :class="[
        'workflow-process-header',
        !collapse && 'workflow-process-header-expanded',
        readonly && 'workflow-process-header-readonly',
      ]"
      @click="!readonly && (collapse = !collapse)"
    >
      <RiLoader2Line
        v-if="running"
        class="workflow-process-icon workflow-process-icon-loading"
      />
      <CheckCircleFilled
        v-if="succeeded"
        class="workflow-process-icon workflow-process-icon-success"
      />
      <ExclamationCircleFilled
        v-if="failed"
        class="workflow-process-icon workflow-process-icon-error"
      />
      <div
        :class="[
          'workflow-process-title',
          !collapse && 'workflow-process-title-expanded',
        ]"
      >
        工作流
      </div>
      <RiArrowRightSLine
        v-if="!readonly"
        :class="[
          'workflow-process-arrow',
          !collapse && 'workflow-process-arrow-expanded',
        ]"
      />
    </div>
    <div v-if="!collapse && !readonly" class="workflow-process-content">
      <TracingPanel
        :list="data.tracing"
        :hideNodeInfo="hideInfo"
        :hideNodeProcessDetail="hideProcessDetail"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";

import { RiArrowRightSLine, RiLoader2Line } from "@remixicon/vue";
import type { ChatItem, WorkflowProcess } from "@/DifyChat/types/app";
import {
  CheckCircleFilled,
  ExclamationCircleFilled,
} from "@ant-design/icons-vue";
import TracingPanel from "./TracingPanel.vue";
import { WorkflowRunningStatus } from "@/DifyChat/types/app";

interface Props {
  data: WorkflowProcess;
  item?: ChatItem;
  expand?: boolean;
  hideInfo?: boolean;
  hideProcessDetail?: boolean;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  expand: false,
  hideInfo: false,
  hideProcessDetail: false,
  readonly: false,
});

const collapse = ref(!props.expand);
const running = computed(
  () => props.data.status === WorkflowRunningStatus.Running
);
const succeeded = computed(
  () => props.data.status === WorkflowRunningStatus.Succeeded
);
const failed = computed(
  () =>
    props.data.status === WorkflowRunningStatus.Failed ||
    props.data.status === WorkflowRunningStatus.Stopped
);

watch(
  () => props.expand,
  newVal => {
    collapse.value = !newVal;
  }
);
</script>

<style scoped>
.workflow-process {
  border-radius: 0.75rem;
  padding: 8px;
}

.workflow-process-collapsed {
  border-left: 0.25px solid #10182814;
  background-image: linear-gradient(
    90deg,
    rgba(200, 206, 218, 0.2),
    rgba(200, 206, 218, 0.04)
  );
}

.workflow-process-expanded {
  border: 0.5px solid #10182814;
}

.workflow-process-running {
  background-color: #f2f4f7;
}

.workflow-process-succeeded {
  background-color: #ecfdf3;
}

.workflow-process-failed {
  background-color: #fef3f2;
}

.workflow-process-bg {
  background-image: linear-gradient(
    90deg,
    rgba(200, 206, 218, 0.2) 0%,
    rgba(200, 206, 218, 0.04) 100%
  );
}

.workflow-process-header {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.workflow-process-header-expanded {
  padding: 0 0.375rem;
}

.workflow-process-header-readonly {
  cursor: default;
}

.workflow-process-icon {
  margin-right: 0.25rem;
  height: 0.875rem;
  width: 0.875rem;
  flex-shrink: 0;
}

.workflow-process-icon-loading {
  animation: spin 1s linear infinite;
  color: #676f83;
}

.workflow-process-icon-success {
  color: #079455;
}

.workflow-process-icon-error {
  color: #d92d20;
}

.workflow-process-title {
  font-size: 0.75rem;
  font-weight: 500;
  color: #354052;
}

.workflow-process-title-expanded {
  flex-grow: 1;
}

.workflow-process-arrow {
  margin-left: 0.25rem;
  height: 1rem;
  width: 1rem;
  color: #676f83;
}

.workflow-process-arrow-expanded {
  transform: rotate(90deg);
}

.workflow-process-content {
  margin-top: 0.375rem;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
