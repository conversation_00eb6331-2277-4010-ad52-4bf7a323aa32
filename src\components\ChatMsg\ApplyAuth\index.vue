<template>
  <div :class="prefixCls">
    暂无权限，
    <template v-if="onApplyAuth !== undefined">
      <span
        :class="`${prefixCls}-apply`"
        @click="handleApplyAuth"
      >
        点击申请
      </span>
    </template>
    <template v-else>
      请联系管理员申请权限
    </template>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { PREFIX_CLS } from '../../../common/constants';

export default defineComponent({
  name: 'ApplyAuth',
  props: {
    model: {
      type: String,
      required: true
    },
    onApplyAuth: {
      type: Function as PropType<(model: string) => void>,
      default: undefined
    }
  },
  setup(props) {
    const prefixCls = `${PREFIX_CLS}-apply-auth`;

    const handleApplyAuth = () => {
      props.onApplyAuth?.(props.model);
    };

    return {
      prefixCls,
      handleApplyAuth
    };
  }
});
</script>

<style lang="less">
@import './style.less';
</style>
