<template>
  <Message position="left" :bubbleClassName="`${prefixCls}-typing-bubble`">
    <div :class="`${prefixCls}-typing`">
      <span class="dot"></span>
      <span class="dot"></span>
      <span class="dot"></span>
    </div>
  </Message>
</template>

<script setup lang="ts">
import Message from '../ChatMsg/Message/index.vue';
import { PREFIX_CLS } from '../../common/constants';

const prefixCls = `${PREFIX_CLS}-item`;
</script>

<style scoped>
.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--chat-blue, #1b4aef);
  margin: 0 2px;
  animation: bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  } 
  40% {
    transform: scale(1);
  }
}
</style> 