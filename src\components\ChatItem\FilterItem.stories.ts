import { <PERSON>a, StoryObj } from '@storybook/vue3';
import FilterItem from './FilterItem.vue';
import { FilterItemType, ChatContextType, ModelType, EntityInfoType, DateInfoType, SqlInfoType } from '../../common/type';
import { ChatContextTypeQueryTypeEnum } from '../../common/constants';

// 模拟数据
const mockChatContext: Partial<ChatContextType> = {
  id: 123,
  aggType: 'SUM',
  modelId: 100,
  modelName: '销售模型',
  dataSet: {
    alias: '销售',
    bizName: 'sales',
    id: 1,
    model: 100,
    name: '销售数据',
    modelNames: ['销售数据'],
    type: 'BASIC',
    useCnt: 1
  } as ModelType,
  dateInfo: {
    dateList: [],
    dateMode: 'ABSOLUTE',
    period: 'day',
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    text: '2023年全年',
    unit: 1
  } as DateInfoType,
  dimensions: [],
  metrics: [],
  entity: { alias: ['商品'], id: 1 },
  entityInfo: {
    dataSetInfo: {
      bizName: 'product',
      itemId: 1,
      name: '商品',
      primaryEntityBizName: 'product',
      value: 'product',
      words: ['商品']
    },
    dimensions: [],
    metrics: [],
    entityId: 1
  } as EntityInfoType,
  elementMatches: [],
  nativeQuery: false,
  queryMode: 'SQL',
  queryType: ChatContextTypeQueryTypeEnum.DETAIL,
  dimensionFilters: [],
  properties: {},
  sqlInfo: {
    parsedS2SQL: '',
    correctedS2SQL: '',
    querySQL: ''
  } as SqlInfoType,
  textInfo: ''
};

// 更多关于如何设置故事：https://storybook.js.org/docs/writing-stories
const meta = {
  title: 'ChatItem/FilterItem',
  component: FilterItem,
  // 此组件将有一个自动生成的文档页面：https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  argTypes: {
    modelId: {
      control: 'number',
      description: '模型ID'
    },
    filters: {
      control: 'object',
      description: '过滤器数组'
    },
    filter: {
      control: 'object',
      description: '当前过滤器项'
    },
    index: {
      control: 'number',
      description: '索引'
    },
    chatContext: {
      control: 'object',
      description: '聊天上下文'
    },
    agentId: {
      control: 'number',
      description: '代理ID'
    },
    disabled: {
      control: 'boolean',
      description: '是否禁用'
    },
    entityAlias: {
      control: 'text',
      description: '实体别名'
    },
    integrateSystem: {
      control: 'text',
      description: '集成系统名称'
    },
    onFiltersChange: {
      action: 'filtersChanged',
      description: '过滤器变更回调'
    },
    onSwitchEntity: {
      action: 'entitySwitched',
      description: '实体切换回调'
    }
  },
  // 参数的默认值
  args: {
    modelId: 100,
    filters: [
      { elementID: 1, name: '价格', bizName: 'price', operator: '>=', value: 1000 }
    ],
    filter: { elementID: 1, name: '价格', bizName: 'price', operator: '>=', value: 1000 },
    index: 0,
    chatContext: mockChatContext as ChatContextType,
    agentId: 1,
    disabled: false,
    onFiltersChange: (filters: FilterItemType[]) => {},
    onSwitchEntity: (entityId: string) => {}
  },
} satisfies Meta<typeof FilterItem>;

export default meta;
type Story = StoryObj<typeof meta>;

// 数值型过滤器
export const 数值型过滤器: Story = {
  args: {
    filter: { elementID: 1, name: '价格', bizName: 'price', operator: '>=', value: 1000 }
  }
};

// 字符串过滤器
export const 字符串过滤器: Story = {
  args: {
    filter: { elementID: 2, name: '地区', bizName: 'region', operator: '=', value: '北京' }
  }
};

// 日期过滤器
export const 日期过滤器: Story = {
  args: {
    filter: { elementID: 3, name: '日期', bizName: 'date', operator: '=', value: '2023-05-01' }
  }
};

// 多选过滤器
export const 多选过滤器: Story = {
  args: {
    filter: { elementID: 4, name: '城市', bizName: 'city', operator: 'IN', value: ['北京', '上海', '广州'] }
  }
};

// 实体ID过滤器
export const 实体ID过滤器: Story = {
  args: {
    filter: { elementID: 5, name: '商品ID', bizName: 'product_id', value: '商品001' },
    entityAlias: '商品'
  }
};

// 禁用状态
export const 禁用状态: Story = {
  args: {
    filter: { elementID: 1, name: '价格', bizName: 'price', operator: '>=', value: 1000 },
    disabled: true
  }
}; 