<template>
  <div v-if="hasFilterSection" :class="prefixCls">
    <div :class="`${prefixCls}-field-label`">筛选条件：</div>
    <div :class="`${prefixCls}-filter-values`">
      <div
        v-for="filterItem in displayItems"
        :key="filterItem.name"
        :class="`${prefixCls}-filter-item`"
        :title="Array.isArray(filterItem.value) ? filterItem.value.join('、') : [filterItem.value].join('、')"
      >
        <span :class="`${prefixCls}-field-name`">{{ filterItem.name }}：</span>
        <span :class="`${prefixCls}-filter-value`">
          {{ Array.isArray(filterItem.value) ? filterItem.value.join('、') : [filterItem.value].join('、') }}
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { PREFIX_CLS } from '../../../common/constants';
import type { ChatContextType, EntityInfoType } from '../../../common/type';

const props = defineProps<{
  chatContext?: ChatContextType;
  entityInfo?: EntityInfoType;
}>();

const prefixCls = `${PREFIX_CLS}-filter-section`;

const entityInfoList = computed(() => {
  return props.entityInfo?.dimensions?.filter(dimension => !dimension.bizName.includes('photo')) || [];
});

const hasFilterSection = computed(() => {
  return props.chatContext?.dimensionFilters && props.chatContext.dimensionFilters.length > 0;
});

const displayItems = computed(() => {
  if (!hasFilterSection.value) return [];
  return (entityInfoList.value.length > 0 ? entityInfoList.value : props.chatContext?.dimensionFilters || []);
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
