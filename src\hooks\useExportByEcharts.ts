import { message } from "ant-design-vue";
import type { ECharts } from "echarts";
import { Ref } from "vue";

export interface ExportByEchartsProps {
  instanceRef: Ref<ECharts | null | undefined>;
  question: string;
  options?: Parameters<ECharts["getConnectedDataURL"]>[0];
}

export const useExportByEcharts = ({
  instanceRef,
  question,
  options,
}: ExportByEchartsProps) => {
  // @ts-ignore
  const handleSaveAsImage = () => {
    if (instanceRef.value) {
      return instanceRef.value.getConnectedDataURL({
        type: "png",
        pixelRatio: 2,
        backgroundColor: "#fff",
        excludeComponents: ["toolbox"],
        ...options,
      });
    }
  };

  const downloadImage = (url: string) => {
    const a = document.createElement("a");
    a.href = url;
    a.download = `${question}.png`;
    a.click();
  };

  const downloadChartAsImage = () => {
    const url = handleSaveAsImage();
    if (url) {
      downloadImage(url);
      message.success("导出图片成功");
    } else {
      message.error("该条消息暂不支持导出图片");
    }
  };

  return { downloadChartAsImage };
};
