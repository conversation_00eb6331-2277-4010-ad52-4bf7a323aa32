<template>
  <div :class="['node-container', className]">
    <div class="node-panel">
      <div
        :class="[
          'node-header',
          hideInfo ? 'node-header-compact' : '',
          !collapseState &&
            (hideInfo
              ? 'node-header-expanded-compact'
              : 'node-header-expanded'),
        ]"
        @click="handleCollapseToggle"
      >
        <!-- <RiArrowRightSLine
          v-if="!hideProcessDetail"
          :class="['node-arrow', !collapseState && 'node-arrow-rotated']"
        /> -->

        <!-- <BlockIcon
          :size="inMessage ? 'xs' : 'sm'"
          :class="['node-icon', inMessage && 'node-icon-in-message']"
          :type="nodeInfo.node_type"
          :tool-icon="nodeInfo.extras?.icon || nodeInfo.extras"
        /> -->
        <BlockIcon :node_type="nodeInfo.node_type" class="node-icon" />

        <div
          :class="['node-title', hideInfo && 'node-title-small']"
          :title="nodeInfo.title"
        >
          {{ nodeInfo.title }}
        </div>
        <div
          v-if="nodeInfo.status !== 'running' && !hideInfo"
          class="node-time"
        >
          {{
            nodeInfo.execution_metadata?.total_tokens
              ? `${getTokenCount(nodeInfo.execution_metadata?.total_tokens || 0)} tokens · `
              : ""
          }}{{ getTime(nodeInfo.elapsed_time || 0) }}
        </div>
        <RiCheckboxCircleFill
          v-if="nodeInfo.status === 'succeeded'"
          class="node-status-success"
        />
        <RiErrorWarningLine
          v-if="nodeInfo.status === 'failed'"
          class="node-status-warning"
        />
        <RiAlertFill
          v-if="nodeInfo.status === 'stopped'"
          :class="[
            'node-status-alert',
            inMessage && 'node-status-alert-in-message',
          ]"
        />
        <RiAlertFill
          v-if="nodeInfo.status === 'exception'"
          :class="[
            'node-status-alert',
            inMessage && 'node-status-alert-in-message',
          ]"
        />
        <div v-if="nodeInfo.status === 'running'" class="node-status-running">
          <span class="node-status-running-text">Running</span>
          <RiLoader2Line class="node-status-running-icon" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, watch } from "vue";
import {
  RiAlertFill,
  RiCheckboxCircleFill,
  RiErrorWarningLine,
  RiLoader2Line,
} from "@remixicon/vue";
import BlockIcon from "./block-icon.vue";
import type {
  AgentLogItemWithChildren,
  IterationDurationMap,
  LoopDurationMap,
  LoopVariableMap,
  NodeTracing,
} from "@/DifyChat/types/app";

// Props 定义
const props = defineProps({
  className: {
    type: String,
    default: "",
  },
  nodeInfo: {
    type: Object as PropType<NodeTracing>,
    required: true,
  },
  inMessage: {
    type: Boolean,
    default: false,
  },
  hideInfo: {
    type: Boolean,
    default: false,
  },
  hideProcessDetail: {
    type: Boolean,
    default: false,
  },
  onShowIterationDetail: {
    type: Function as PropType<
      (detail: NodeTracing[][], iterDurationMap: IterationDurationMap) => void
    >,
    default: undefined,
  },
  onShowLoopDetail: {
    type: Function as PropType<
      (
        detail: NodeTracing[][],
        loopDurationMap: LoopDurationMap,
        loopVariableMap: LoopVariableMap
      ) => void
    >,
    default: undefined,
  },
  onShowRetryDetail: {
    type: Function as PropType<(detail: NodeTracing[]) => void>,
    default: undefined,
  },
  onShowAgentOrToolLog: {
    type: Function as PropType<(detail?: AgentLogItemWithChildren) => void>,
    default: undefined,
  },
  notShowIterationNav: {
    type: Boolean,
    default: false,
  },
  notShowLoopNav: {
    type: Boolean,
    default: false,
  },
});

const collapseState = ref(true);

const handleCollapseToggle = () => {
  if (props.hideProcessDetail) return;
  collapseState.value = !collapseState.value;
};

const getTime = (time: number): string => {
  if (time < 1) return `${(time * 1000).toFixed(3)} ms`;
  if (time > 60)
    return `${Number.parseInt(Math.round(time / 60).toString())} m ${(time % 60).toFixed(3)} s`;
  return `${time.toFixed(3)} s`;
};

const getTokenCount = (tokens: number): string | number => {
  if (tokens < 1000) return tokens;
  if (tokens >= 1000 && tokens < 1000000)
    return `${Number.parseFloat((tokens / 1000).toFixed(3))}K`;
  if (tokens >= 1000000)
    return `${Number.parseFloat((tokens / 1000000).toFixed(3))}M`;
  return tokens;
};

watch(
  () => props.nodeInfo.expand,
  newVal => {
    if (!props.hideProcessDetail) {
      collapseState.value = !newVal;
    }
  }
);
</script>

<style scoped>
.node-container {
  padding: 4px 16px;
}

.node-panel {
  border-radius: 10px;
  border: 1px solid #10182814;
  background-color: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}

.node-panel:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.node-header {
  display: flex;
  cursor: pointer;
  align-items: center;
  padding-left: 6px;
  padding-right: 12px;
  padding-top: 6px;
  padding-bottom: 6px;
}

.node-header-compact {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 8px;
}

.node-header-expanded {
  padding-bottom: 6px !important;
}

.node-header-expanded-compact {
  padding-bottom: 4px !important;
}

.node-arrow {
  margin-right: 4px;
  height: 16px;
  width: 16px;
  flex-shrink: 0;
  color: #1018284d;
  transition: all 0.2s;
}

.node-panel:hover .node-arrow {
  color: #676f83;
}

.node-arrow-rotated {
  transform: rotate(90deg);
}

.node-icon {
  margin-right: 8px;
  flex-shrink: 0;
}

.node-icon-in-message {
  margin-right: 4px !important;
}

.node-title {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #354052;
}

.node-title-small {
  font-size: 10px !important;
}

.node-time {
  font-size: 12px;
  flex-shrink: 0;
  color: #676f83;
}

.node-status-success {
  margin-left: 8px;
  height: 14px;
  width: 14px;
  flex-shrink: 0;
  color: #079455;
}

.node-status-warning {
  margin-left: 8px;
  height: 14px;
  width: 14px;
  flex-shrink: 0;
  color: #dc6803;
}

.node-status-alert {
  margin-left: 8px;
  height: 16px;
  width: 16px;
  flex-shrink: 0;
  color: #f79009;
}

.node-status-alert-in-message {
  height: 14px;
  width: 14px;
}

.node-status-running {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  font-size: 13px;
  font-weight: 500;
  line-height: 16px;
  color: #155aef;
}

.node-status-running-text {
  margin-right: 8px;
  font-size: 12px;
  font-weight: normal;
}

.node-status-running-icon {
  height: 14px;
  width: 14px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
