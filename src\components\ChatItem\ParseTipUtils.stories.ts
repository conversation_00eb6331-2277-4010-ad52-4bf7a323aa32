import type { Meta, StoryObj } from '@storybook/vue3';
import { ChatContextTypeQueryTypeEnum } from '../../common/constants';
import ParseTipUtils from './ParseTipUtils.vue';
import { MAX_OPTION_VALUES_COUNT } from './constants';

// 假设AGG_TYPE_MAP包含以下映射
const AGG_TYPE_MAP_MOCK = {
  SUM: '总和',
  AVG: '平均值',
  MAX: '最大值',
  MIN: '最小值'
};

/**
 * `ParseTipUtils` 组件用于展示查询解析的提示信息，帮助用户理解当前查询的上下文。
 * 组件会根据传入的解析信息，展示数据集、查询模式、指标、维度等关键信息。
 */
const meta: Meta<typeof ParseTipUtils> = {
  title: 'ChatItem/ParseTipUtils',
  component: ParseTipUtils,
  tags: ['autodocs'],
  argTypes: {
    parseInfo: {
      description: '解析信息对象，包含数据集、查询类型、维度、指标等信息',
      control: 'object',
      table: {
        type: { summary: 'Object' },
        category: '主要参数'
      }
    },
    dimensionFilters: {
      description: '维度过滤器数组，用于实体查询时的过滤条件',
      control: 'object',
      table: {
        type: { summary: 'Array' },
        category: '过滤参数'
      }
    },
    entityInfo: {
      description: '实体信息对象，包含与实体相关的维度信息',
      control: 'object',
      table: {
        type: { summary: 'Object' },
        category: '实体参数'
      }
    }
  },
  parameters: {
    docs: {
      description: {
        component: `
# 解析提示组件

该组件用于展示数据查询解析的结果提示，帮助用户了解系统如何理解和处理他们的查询请求。
组件会根据不同类型的查询场景，显示相应的关键信息。

## 使用场景

- 数据集查询：显示涉及的数据集名称
- 聚合查询：显示聚合模式和使用的指标
- 实体查询：显示查询的具体实体
- 维度筛选：显示应用的维度筛选条件
- 工具解答：显示将用于解答的工具类型

## 使用示例

### 基本用法

\`\`\`vue
<template>
  <ParseTipUtils 
    :parseInfo="parseInfo"
    :dimensionFilters="dimensionFilters"
    :entityInfo="entityInfo"
  />
</template>

<script setup>
import { ref } from 'vue';
import ParseTipUtils from './ParseTipUtils.vue';

const parseInfo = ref({
  dataSet: { name: '销售数据集' },
  queryType: 'DETAIL',
  dimensions: [
    { type: 'DIMENSION', name: '日期', bizName: 'date' },
    { type: 'DIMENSION', name: '产品', bizName: 'product' }
  ]
});

const dimensionFilters = ref([]);
const entityInfo = ref({});
</script>
\`\`\`

### 聚合查询场景

\`\`\`vue
<template>
  <ParseTipUtils :parseInfo="parseInfo" />
</template>

<script setup>
import { ref } from 'vue';
import ParseTipUtils from './ParseTipUtils.vue';
import { ChatContextTypeQueryTypeEnum } from '../../common/constants';

const parseInfo = ref({
  dataSet: { name: '销售数据集' },
  queryType: ChatContextTypeQueryTypeEnum.AGGREGATE,
  queryMode: 'METRIC_GROUPBY',
  metrics: [{ name: '销售额', bizName: 'sales' }],
  aggType: 'SUM'
});
</script>
\`\`\`

## 注意事项

- 当指标或字段数量超过显示上限(${MAX_OPTION_VALUES_COUNT})时，会自动省略显示
- 根据查询模式的不同，组件会有不同的展示逻辑
        `
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof ParseTipUtils>;

// 基础示例 - 数据集视图
export const 基础数据集视图: Story = {
  args: {
    parseInfo: {
      dataSet: {
        name: '销售数据集'
      },
      queryType: 'DETAIL',
      queryMode: 'TAG_DETAIL',
      dimensions: [
        { type: 'DIMENSION', name: '日期', bizName: 'date' },
        { type: 'DIMENSION', name: '产品', bizName: 'product' }
      ]
    }
  },
  parameters: {
    docs: {
      description: {
        story: '最基础的数据集查询视图，展示数据集名称和查询字段'
      }
    }
  }
};

// 聚合模式示例
export const 聚合模式: Story = {
  args: {
    parseInfo: {
      dataSet: {
        name: '销售数据集'
      },
      queryType: ChatContextTypeQueryTypeEnum.AGGREGATE,
      queryMode: 'METRIC_GROUPBY',
      dimensions: [
        { type: 'DIMENSION', name: '日期', bizName: 'date' },
        { type: 'DIMENSION', name: '产品', bizName: 'product' }
      ],
      metrics: [
        { name: '销售额', bizName: 'sales' },
        { name: '利润率', bizName: 'profit_rate' }
      ]
    }
  },
  parameters: {
    docs: {
      description: {
        story: '聚合模式查询，显示查询模式为"聚合模式"，并展示所使用的指标'
      }
    }
  }
};

// 实体查询模式
export const 实体查询: Story = {
  args: {
    parseInfo: {
      queryMode: 'ENTITY_DETAIL',
      entity: {
        alias: ['产品.手机']
      },
      elementMatches: [
        {
          element: {
            type: 'ID',
            name: 'iPhone 13'
          }
        }
      ]
    },
    dimensionFilters: [
      {
        value: 'product_123'
      }
    ]
  },
  parameters: {
    docs: {
      description: {
        story: '实体查询模式，显示实体的类型和名称，而不是数据集名称'
      }
    }
  }
};

// 带有维度过滤的示例
export const 带维度过滤: Story = {
  args: {
    parseInfo: {
      dataSet: {
        name: '用户数据集'
      },
      queryType: 'DETAIL',
      queryMode: 'TAG_DETAIL',
      dimensions: [
        { type: 'DIMENSION', name: '年龄', bizName: 'age' },
        { type: 'DIMENSION', name: '性别', bizName: 'gender' }
      ]
    },
    entityInfo: {
      dimensions: [
        { name: '地区', value: '北京', itemId: 'region_1' },
        { name: '渠道', value: '线上', itemId: 'channel_1' }
      ]
    }
  },
  parameters: {
    docs: {
      description: {
        story: '带有维度过滤条件的查询，显示应用的维度过滤值'
      }
    }
  }
};

// 聚合方式示例
export const 聚合方式展示: Story = {
  args: {
    parseInfo: {
      dataSet: {
        name: '财务数据集'
      },
      queryType: ChatContextTypeQueryTypeEnum.AGGREGATE,
      queryMode: 'METRIC_ORDERBY',
      dimensions: [
        { type: 'DIMENSION', name: '季度', bizName: 'quarter' }
      ],
      metrics: [
        { name: '收入', bizName: 'revenue' }
      ],
      aggType: 'SUM'
    }
  },
  parameters: {
    docs: {
      description: {
        story: '显示聚合方式（总和、平均值等）的查询提示'
      }
    }
  }
};

// 插件工具解答
export const 插件工具解答: Story = {
  args: {
    parseInfo: {
      properties: {
        type: 'plugin',
        name: 'AI分析助手'
      }
    }
  },
  parameters: {
    docs: {
      description: {
        story: '由插件工具解答的场景，显示将由哪个插件来解答查询'
      }
    }
  }
};

// 内置工具解答
export const 内置工具解答: Story = {
  args: {
    parseInfo: {
      properties: {
        type: 'builtin',
        name: '数据洞察'
      }
    }
  },
  parameters: {
    docs: {
      description: {
        story: '由内置工具解答的场景，显示将由哪个内置工具来解答查询'
      }
    }
  }
};

// 多维度指标示例
export const 多维度指标: Story = {
  args: {
    parseInfo: {
      dataSet: {
        name: '营销数据集'
      },
      queryType: ChatContextTypeQueryTypeEnum.AGGREGATE,
      queryMode: 'METRIC_FILTER',
      dimensions: [
        { type: 'DIMENSION', name: '广告系列', bizName: 'campaign' },
        { type: 'DIMENSION', name: '渠道', bizName: 'channel' },
        { type: 'DIMENSION', name: '时间', bizName: 'time' }
      ],
      metrics: [
        { name: '点击率', bizName: 'ctr' },
        { name: '转化率', bizName: 'cvr' },
        { name: '花费', bizName: 'cost' },
        { name: '收入', bizName: 'revenue' }
      ]
    }
  },
  parameters: {
    docs: {
      description: {
        story: '当指标数量超过显示上限时，会自动省略显示'
      }
    }
  }
};

// LLM模式
export const LLM查询模式: Story = {
  args: {
    parseInfo: {
      dataSet: {
        name: '客户反馈数据集'
      },
      queryMode: 'LLM_S2SQL',
      dimensions: [
        { type: 'DIMENSION', name: '主题', bizName: 'topic' },
        { type: 'DIMENSION', name: '情感', bizName: 'sentiment' }
      ],
      metrics: [
        { name: '评分', bizName: 'rating' }
      ]
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'LLM自然语言到SQL的查询模式展示'
      }
    }
  }
};

// 空状态
export const 空数据状态: Story = {
  args: {
    parseInfo: {
      dataSet: {
        name: '空数据集'
      }
    }
  },
  parameters: {
    docs: {
      description: {
        story: '仅有数据集信息，没有其他查询信息的最简状态'
      }
    }
  }
}; 