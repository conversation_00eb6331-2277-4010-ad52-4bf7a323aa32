<template>
  <div :id="id" :class="messageContainerClass">
    <div class="messageList">
      <div
        v-for="(msgItem, index) in messageList"
        :key="msgItem.id"
        :id="`${msgItem.id}`"
        class="messageItem"
      >
        <Text
          v-if="msgItem.type === MessageTypeEnum.TEXT"
          position="left"
          :data="msgItem.msg || ''"
        />
        <AgentTip
          v-if="msgItem.type === MessageTypeEnum.AGENT_LIST"
          :currentAgent="currentAgent"
          :onSendMsg="onSendMsg"
        />
        <template v-if="msgItem.type === MessageTypeEnum.QUESTION">
          <Text position="right" :data="msgItem.msg || ''" />
          <Text
            v-if="msgItem.identityMsg"
            position="left"
            :data="msgItem.identityMsg"
          />
          <ChatItem
            :questionId="msgItem.questionId"
            :currentAgent="currentAgent"
            :isSimpleMode="isSimpleMode"
            :isDebugMode="isDebugMode"
            :msg="msgItem.msgValue || msgItem.msg || ''"
            :parseInfos="msgItem.parseInfos"
            :parseTimeCostValue="msgItem.parseTimeCost"
            :msgData="msgItem.msgData"
            :conversationId="chatId"
            :modelId="msgItem.modelId"
            :agentId="msgItem.agentId"
            :score="msgItem.score"
            :filter="msgItem.filters"
            :triggerResize="triggerResize"
            :isDeveloper="isDeveloper"
            :integrateSystem="integrateSystem"
            :isLastMessage="index === messageList.length - 1"
            :onMsgDataLoaded="
              (data, valid, isRefresh) =>
                onMsgDataLoaded(
                  data,
                  msgItem.id,
                  msgItem.msgValue || msgItem.msg || '',
                  valid,
                  isRefresh
                )
            "
            :onUpdateMessageScroll="updateMessageContainerScroll"
            :onSendMsg="onSendMsg"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  onMounted,
  onUnmounted,
  watch,
} from "vue";
import Text from "../components/Text/index.vue";
import AgentTip from "../components/AgentTip/index.vue";
import ChatItem from "../../components/ChatItem/index.vue";
import { MessageTypeEnum, type MessageItem, type AgentType } from "../type";
import { isMobile, updateMessageContainerScroll } from "../../utils/utils";
import type { MsgDataType } from "../../common/type";
import type { PropType } from "vue";

export default defineComponent({
  name: "MessageContainer",
  components: {
    Text,
    AgentTip,
    ChatItem,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
    chatId: {
      type: Number,
      required: true,
    },
    messageList: {
      type: Array as PropType<MessageItem[]>,
      required: true,
    },
    historyVisible: {
      type: Boolean,
      required: true,
    },
    currentAgent: {
      type: Object as PropType<AgentType>,
      default: undefined,
    },
    chatVisible: {
      type: Boolean,
      default: undefined,
    },
    isDeveloper: {
      type: Boolean,
      default: undefined,
    },
    integrateSystem: {
      type: String,
      default: undefined,
    },
    isSimpleMode: {
      type: Boolean,
      default: undefined,
    },
    isDebugMode: {
      type: Boolean,
      default: undefined,
    },
    onMsgDataLoaded: {
      type: Function as PropType<
        (
          data: MsgDataType,
          questionId: string | number,
          question: string,
          valid: boolean,
          isRefresh?: boolean
        ) => void
      >,
      required: true,
    },
    onSendMsg: {
      type: Function as PropType<(value: string) => void>,
      required: true,
    },
  },
  setup(props) {
    const triggerResize = ref(false);

    const onResize = () => {
      triggerResize.value = true;
      setTimeout(() => {
        triggerResize.value = false;
      }, 0);
    };

    onMounted(() => {
      window.addEventListener("resize", onResize);
    });

    onUnmounted(() => {
      window.removeEventListener("resize", onResize);
    });

    watch(
      () => [props.historyVisible, props.chatVisible],
      () => {
        onResize();
      }
    );

    const messageContainerClass = computed(() => {
      return {
        messageContainer: true,
        mobile: isMobile,
      };
    });

    return {
      triggerResize,
      messageContainerClass,
      MessageTypeEnum,
      updateMessageContainerScroll,
    };
  },
});
</script>

<style lang="less" scoped>
.messageContainer {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 0;
  overflow-x: hidden;
  overflow-y: scroll;

  .messageList {
    display: flex;
    flex-direction: column;
    padding: 70px 20px 60px 14px;
    row-gap: 16px;

    .messageItem {
      display: flex;
      flex-direction: column;
      row-gap: 20px;

      :deep(.ant-table-small) {
        .ant-table-tbody {
          .ant-table-cell {
            padding: 6px 0 !important;
          }
        }
        .ss-chat-table-formatted-value {
          font-size: 15px !important;
        }
      }

      :deep(.ant-table-row) {
        background-color: #fff;
      }

      :deep(.ant-table-tbody > tr > td) {
        border-bottom: 1px solid #f0f0f0;
        transition:
          background 0.2s,
          border-color 0.2s;
      }

      :deep(.ss-chat-table-even-row) {
        background-color: #fbfbfb;
      }
    }
  }

  &.mobile {
    .messageList {
      padding: 20px 10px 30px;
    }
  }
}
</style>
