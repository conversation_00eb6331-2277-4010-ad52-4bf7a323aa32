<template>
  <div class="agentTip" v-if="currentAgent">
    <LeftAvatar v-if="!isMobile" />
    <Message position="left" bubbleClassName="agentTipMsg">
      <div class="title">
        您好，智能助理【{{ currentAgent.name }}】将与您对话，试着问：
      </div>
      <div class="content">
        <div class="examples">
          <template v-if="currentAgent.examples?.length > 0">
            <div
              v-for="example in currentAgent.examples"
              :key="example"
              class="example"
              @click="handleSendMsg(example)"
            >
              "{{ example }}"
            </div>
          </template>
          <template v-else>
            <div class="example">{{ currentAgent.description }}</div>
          </template>
        </div>
      </div>
    </Message>
  </div>
</template>

<script setup lang="ts">
import LeftAvatar from '../CopilotAvatar/index.vue';
import Message from '../Message/index.vue';
import { AgentType } from '../../type';
import { isMobile } from '../../../utils/utils';

const props = defineProps<{
  currentAgent?: AgentType;
  onSendMsg: (value: string) => void;
}>();

const handleSendMsg = (example: string) => {
  props.onSendMsg(example);
};
</script>

<style lang="less" scoped>
.agentTip {
  display: flex;

  :deep(.agentTipMsg) {
    padding: 12px 20px 20px !important;
  }

  .title {
    margin-bottom: 12px;
    font-size: 14px;
  }

  .content {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    margin-top: 10px;
    column-gap: 14px;

    .topBar {
      .tip {
        margin-top: 2px;
        font-size: 13px;
      }
    }

    .examples {
      display: flex;
      flex-direction: column;
      font-size: 13px;
      row-gap: 8px;

      .example {
        color: var(--chat-blue);
        cursor: pointer;
      }
    }

    &.fullscreen {
      flex: none;
      width: 280px;
    }
  }
}
</style>
