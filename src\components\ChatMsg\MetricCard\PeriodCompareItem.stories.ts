import { Meta, StoryObj } from '@storybook/vue3';
import PeriodCompareItem from './PeriodCompareItem.vue';

const meta: Meta<typeof PeriodCompareItem> = {
  component: PeriodCompareItem,
  title: 'Components/ChatMsg/PeriodCompareItem',
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: '周期比较项标题'
    },
    value: {
      control: 'text',
      description: '周期比较项数值'
    }
  }
};

export default meta;
type Story = StoryObj<typeof PeriodCompareItem>;

export const UpTrend: Story = {
  args: {
    title: '环比',
    value: '+20.5%'
  }
};

export const DownTrend: Story = {
  args: {
    title: '同比',
    value: '-15.3%'
  }
};

export const NoTrend: Story = {
  args: {
    title: '增长率',
    value: '0%'
  }
}; 