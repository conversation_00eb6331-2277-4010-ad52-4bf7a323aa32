// import { fn } from "@storybook/test";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";

import Conversation from "./index.vue";
import type { ConversationDetailType, AgentType } from "../type";
import { Dropdown, Input, Menu } from "ant-design-vue";
import {
  CloseOutlined,
  DeleteOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";

// 模拟数据
const mockConversations: ConversationDetailType[] = [
  {
    chatId: 1,
    chatName: "关于销售数据的讨论",
    lastQuestion: "上个月的销售额是多少？",
    lastTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5分钟前
  },
  {
    chatId: 2,
    chatName: "用户增长分析",
    lastQuestion: "我们的用户留存率如何？",
    lastTime: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1小时前
  },
  {
    chatId: 3,
    chatName: "产品讨论",
    lastQuestion: "新功能的开发进度如何？",
    lastTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1天前
  },
  {
    chatId: 4,
    chatName: "市场调研",
    lastQuestion: "竞争对手的市场份额是多少？",
    lastTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1周前
  },
];

const mockAgent: AgentType = {
  id: 1,
  name: "销售助手",
  description: "帮助分析销售数据的智能助手",
  examples: ["分析销售趋势", "预测下个季度的销售额"],
  status: 1,
  createdBy: "admin",
  updatedBy: "admin",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  enableSearch: 1,
  enableFeedback: 1,
  toolConfig: "",
  modelConfig: {
    provider: "openai",
    baseUrl: "https://api.openai.com",
    apiKey: "sk-xxxx",
    modelName: "gpt-3.5-turbo",
    temperature: 0.7,
    timeOut: 60000,
  },
  multiTurnConfig: {
    enableMultiTurn: true,
  },
  dataSetIds: [1, 2, 3],
};

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta = {
  title: "Chat/Conversation",
  component: Conversation,
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  argTypes: {
    historyVisible: {
      control: "boolean",
      description: "是否显示历史对话",
    },
    currentAgent: {
      control: { type: "object" },
      description: "当前选中的代理",
    },
    currentConversation: {
      control: { type: "object" },
      description: "当前选中的对话",
    },
    onSelectConversation: {
      action: "选择对话",
      description: "选择对话的回调函数",
    },
    onCloseConversation: {
      action: "关闭对话",
      description: "关闭对话的回调函数",
    },
  },
  parameters: {
    docs: {
      description: {
        component: "会话历史组件，用于显示和管理历史对话。",
      },
    },
  },
} satisfies Meta<typeof Conversation>;

export default meta;
type Story = StoryObj<typeof meta>;

// 创建模拟API响应
const mockGetAllConversations = async () => {
  return { data: mockConversations };
};

// 默认状态 - 隐藏历史对话
export const 默认状态: Story = {
  args: {
    historyVisible: true,
    currentAgent: mockAgent,
    currentConversation: mockConversations[0],
    onSelectConversation: (conversation: ConversationDetailType) =>
      console.log("选择对话:", conversation),
    onCloseConversation: () => console.log("关闭对话"),
  },
  render: args => ({
    components: {
      Conversation,
      AInput: Input,
      ADropdown: Dropdown,
      AMenu: Menu,
      AMenuItem: Menu.Item,
      CloseOutlined,
      DeleteOutlined,
      SearchOutlined,
    },
    setup() {
      // 模拟API调用
      //   const originalGetAllConversations = ChatService.getAllConversations;
      // @ts-ignore - 在故事环境中覆盖服务方法
      //   ChatService.getAllConversations = mockGetAllConversations;

      return { args };
    },
    template: '<Conversation v-bind="args" />',
  }),
};

// 显示历史对话
export const 显示历史对话: Story = {
  args: {
    historyVisible: true,
    currentAgent: mockAgent,
    currentConversation: mockConversations[0],
    onSelectConversation: (conversation: ConversationDetailType) =>
      console.log("选择对话:", conversation),
    onCloseConversation: () => console.log("关闭对话"),
  },
  render: args => ({
    components: {
      Conversation,
      AInput: Input,
      ADropdown: Dropdown,
      AMenu: Menu,
      AMenuItem: Menu.Item,
      CloseOutlined,
      DeleteOutlined,
      SearchOutlined,
    },
    setup() {
      // 模拟API调用
      //   const originalGetAllConversations = ChatService.getAllConversations;
      // @ts-ignore - 在故事环境中覆盖服务方法
      //   ChatService.getAllConversations = mockGetAllConversations;

      return { args };
    },
    template: '<Conversation v-bind="args" />',
  }),
};

// 右键菜单测试
export const 右键菜单: Story = {
  args: {
    historyVisible: true,
    currentAgent: mockAgent,
    currentConversation: mockConversations[0],
    onSelectConversation: (conversation: ConversationDetailType) =>
      console.log("选择对话:", conversation),
    onCloseConversation: () => console.log("关闭对话"),
  },
  render: args => ({
    components: {
      Conversation,
      AInput: Input,
      ADropdown: Dropdown,
      AMenu: Menu,
      AMenuItem: Menu.Item,
      CloseOutlined,
      DeleteOutlined,
      SearchOutlined,
    },
    setup() {
      return { args };
    },
    template: `
      <div>
        <p>请在下面的对话项上点击右键测试上下文菜单</p>
        <Conversation v-bind="args" />
      </div>
    `,
  }),
  play: async ({ canvasElement }) => {
    // 等待组件渲染完成
    setTimeout(() => {
      // 查找第一个对话项
      const conversationItem = canvasElement.querySelector(".conversationItem");
      if (conversationItem) {
        console.log("找到对话项，准备模拟右键点击");
        
        // 创建并触发contextmenu事件
        const contextMenuEvent = new MouseEvent('contextmenu', {
          bubbles: true,
          cancelable: true,
          view: window,
          button: 2,
          buttons: 2,
        });
        
        // 直接调用preventDefault以确保事件被正确处理
        contextMenuEvent.preventDefault = () => {
          console.log("preventDefault被调用");
          return true;
        };
        
        // 触发事件
        conversationItem.dispatchEvent(contextMenuEvent);
        console.log("已触发contextmenu事件");
      } else {
        console.error("未找到对话项元素，无法模拟右键点击");
      }
    }, 500);
  },
};

// 无选中对话
export const 无选中对话: Story = {
  args: {
    historyVisible: true,
    currentAgent: mockAgent,
    currentConversation: undefined,
    onSelectConversation: (conversation: ConversationDetailType) =>
      console.log("选择对话:", conversation),
    onCloseConversation: () => console.log("关闭对话"),
  },
  render: args => ({
    components: {
      Conversation,
      AInput: Input,
      ADropdown: Dropdown,
      AMenu: Menu,
      AMenuItem: Menu.Item,
      CloseOutlined,
      DeleteOutlined,
      SearchOutlined,
    },
    setup() {
      // 模拟API调用
      //   const originalGetAllConversations = ChatService.getAllConversations;
      // @ts-ignore - 在故事环境中覆盖服务方法
      //   ChatService.getAllConversations = mockGetAllConversations;

      return { args };
    },
    template: '<Conversation v-bind="args" />',
  }),
};

// 不同代理
export const 不同代理: Story = {
  args: {
    historyVisible: true,
    currentAgent: {
      ...mockAgent,
      id: 2,
      name: "客服助手",
      description: "处理客户问题的智能助手",
    },
    currentConversation: mockConversations[2],
    onSelectConversation: (conversation: ConversationDetailType) =>
      console.log("选择对话:", conversation),
    onCloseConversation: () => console.log("关闭对话"),
  },
  render: args => ({
    components: {
      Conversation,
      AInput: Input,
      ADropdown: Dropdown,
      AMenu: Menu,
      AMenuItem: Menu.Item,
      CloseOutlined,
      DeleteOutlined,
      SearchOutlined,
    },
    setup() {
      // 模拟API调用
      //   const originalGetAllConversations = ChatService.getAllConversations;
      // @ts-ignore - 在故事环境中覆盖服务方法
      //   ChatService.getAllConversations = mockGetAllConversations;

      return { args };
    },
    template: '<Conversation v-bind="args" />',
  }),
};

// 搜索状态
export const 搜索对话: Story = {
  args: {
    historyVisible: true,
    currentAgent: mockAgent,
    currentConversation: mockConversations[0],
    onSelectConversation: (conversation: ConversationDetailType) =>
      console.log("选择对话:", conversation),
    onCloseConversation: () => console.log("关闭对话"),
  },
  render: args => ({
    components: {
      Conversation,
      AInput: Input,
      ADropdown: Dropdown,
      AMenu: Menu,
      AMenuItem: Menu.Item,
      CloseOutlined,
      DeleteOutlined,
      SearchOutlined,
    },
    setup() {
      // 模拟API调用
      //   const originalGetAllConversations = ChatService.getAllConversations;
      // @ts-ignore - 在故事环境中覆盖服务方法
      //   ChatService.getAllConversations = mockGetAllConversations;

      return { args };
    },
    template: '<Conversation v-bind="args" />',
  }),
  play: async ({ canvasElement }) => {
    // 模拟用户输入搜索词
    setTimeout(() => {
      const searchInput = canvasElement.querySelector(".searchTask input");
      if (searchInput) {
        (searchInput as HTMLInputElement).value = "学";
        (searchInput as HTMLInputElement).dispatchEvent(new Event("input"));
      }
    }, 300);
  },
};
