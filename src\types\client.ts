/* eslint-disable @typescript-eslint/no-explicit-any */
type FilterItemType = {
  elementID: number;
  name: string;
  bizName: string;
  operator?: string;
  type?: string;
  value: any;
  entityName?: string;
};

type SendMsgParamsType = {
  msg: string;
  agentId: number;
  modelId: number;
  filters?: FilterItemType[];
};

type DifyAppParams = {
  opening_statement?: string; //开场白
  suggested_questions?: string[]; //相关问题
  file_upload?: {
    image: {
      detail: string;
      enabled: boolean;
      number_limits: number;
      transfer_methods: string[];
    };
    enabled: boolean;
    allowed_file_types: any[];
    allowed_file_extensions: string[];
    allowed_file_upload_methods: string[];
    number_limits: number;
  };
  system_parameters?: {
    image_file_size_limit: number;
    video_file_size_limit: number;
    audio_file_size_limit: number;
    file_size_limit: number;
    workflow_file_upload_limit: number;
  };
  sensitive_word_avoidance?: {
    enabled: boolean;
    type: string;
    configs: any[];
  };
  user_input_form?: any[];
  more_like_this?: {
    enabled: boolean;
  };
};

type SupersonicbiAppParams = {
  examples: string[];
  status: 0 | 1;
  initialSendMsgParams?: SendMsgParamsType;
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
  enableSearch: number;
  enableFeedback: number;
  toolConfig: string;
  modelConfig: {
    provider: string;
    baseUrl: string;
    apiKey: string;
    modelName: string;
    temperature: number;
    timeOut: number;
  };
  multiTurnConfig: {
    enableMultiTurn: boolean;
  };
  dataSetIds: number[];
};

export type ChartType = "chat" | "advanced-chat" | "chatBI-chat";

export type ClientAgentType = {
  chartId?: string;
  chartType: ChartType; // chat advanced-chat   //chatBI-chat:supersonicbi agent
  chartIcon?: string;
  chartName?: string;
  description?: string;
  difyAppParams?: DifyAppParams;
  chatbiConfig?: SupersonicbiAppParams;
  usePermits?: string;
};

export type DifyConversationItem = {
  id: string;
  name: string;
  inputs: Record<string, any> | null;
  introduction: string;
  created_at: number;
  updated_at: number;
};
