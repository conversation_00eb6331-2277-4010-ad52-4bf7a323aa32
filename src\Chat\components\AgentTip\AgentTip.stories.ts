import { fn } from "@storybook/test";
import type { Meta, StoryObj } from "@storybook/vue3";

import AgentTip from "./index.vue";

const meta = {
  title: "Chat/AgentTip",
  component: AgentTip,
  tags: ["autodocs"],
  argTypes: {
    currentAgent: {
      control: "object",
      description: "当前智能助理的信息",
    },
    onSendMsg: {
      description: "发送消息的回调函数",
    },
  },
  args: {
    currentAgent: {
      id: 1,
      name: "数据分析助手",
      description: "我可以帮你分析数据，提供数据洞察",
      examples: [
        "分析一下最近一个月的用户增长趋势",
        "查看用户活跃度最高的时间段",
        "统计不同地区的用户分布情况"
      ],
      status: 1,
      createdBy: "admin",
      updatedBy: "admin",
      createdAt: "2024-03-20",
      updatedAt: "2024-03-20",
      enableSearch: 1,
      enableFeedback: 1,
      toolConfig: "{}",
      modelConfig: {}
    },
    onSendMsg: fn(),
  },
} satisfies Meta<typeof AgentTip>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    currentAgent: {
      id: 1,
      name: "数据分析助手",
      description: "我可以帮你分析数据，提供数据洞察",
      examples: [
        "分析一下最近一个月的用户增长趋势",
        "查看用户活跃度最高的时间段",
        "统计不同地区的用户分布情况"
      ],
      status: 1,
      createdBy: "admin",
      updatedBy: "admin",
      createdAt: "2024-03-20",
      updatedAt: "2024-03-20",
      enableSearch: 1,
      enableFeedback: 1,
      toolConfig: "{}",
      modelConfig: {}
    },
    onSendMsg: fn(),
  },
  render: args => ({
    components: { AgentTip },
    setup() {
      return { args };
    },
    template: `<AgentTip v-bind="args" />`,
  }),
};

export const WithoutExamples: Story = {
  args: {
    currentAgent: {
      id: 2,
      name: "通用助手",
      description: "我可以回答你的各种问题，提供帮助和建议",
      examples: [],
      status: 1,
      createdBy: "admin",
      updatedBy: "admin",
      createdAt: "2024-03-20",
      updatedAt: "2024-03-20",
      enableSearch: 1,
      enableFeedback: 1,
      toolConfig: "{}",
      modelConfig: {}
    },
    onSendMsg: fn(),
  },
  render: Default.render,
};