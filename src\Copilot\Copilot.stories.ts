import type { Meta, StoryObj } from '@storybook/vue3';
import Copilot from './index.vue';

const meta = {
  title: 'Components/Copilot',
  component: Copilot,
  tags: ['autodocs'],
  argTypes: {
    token: {
      control: 'text',
      description: '认证token',
    },
    agentIds: {
      control: 'array',
      description: '代理ID列表',
    },
    noInput: {
      control: 'boolean',
      description: '是否禁用输入',
    },
    isDeveloper: {
      control: 'boolean',
      description: '是否为开发者模式',
    },
    integrateSystem: {
      control: 'select',
      options: ['', 'c2'],
      description: '集成系统类型',
    },
    apiUrl: {
      control: 'text',
      description: 'API地址',
    },
    onReportMsgEvent: {
      description: '消息上报事件回调',
    },
    onOpenChatPage: {
      description: '打开聊天页面回调',
    },
  },
} satisfies Meta<typeof Copilot>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础展示
export const Default: Story = {
  args: {
    token: 'eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P7Ml8kt_BRB1uTAyj8aWC6MAqdw-J8vs-qfdOY-JkAzOe8BUAKOkCcnRHysR59FTHc7dYXcHeS7uXjVey2NlGg',
    agentIds: [1, 2, 3],
    noInput: false,
    isDeveloper: false,
    integrateSystem: '',
    apiUrl: 'https://api.example.com',
    onReportMsgEvent: (msg: string, valid: boolean) => {
      console.log('Report message:', msg, valid);
    },
    onOpenChatPage: (agentId?: number) => {
      console.log('Open chat page:', agentId);
    },
  },
};

// 禁用输入模式
export const NoInput: Story = {
  args: {
    ...Default.args,
    noInput: true,
  },
};

// 开发者模式
export const DeveloperMode: Story = {
  args: {
    ...Default.args,
    isDeveloper: true,
  },
};

// C2系统集成
export const C2System: Story = {
  args: {
    ...Default.args,
    integrateSystem: 'c2',
  },
};

// 无代理模式
export const NoAgents: Story = {
  args: {
    ...Default.args,
    agentIds: [],
  },
}; 