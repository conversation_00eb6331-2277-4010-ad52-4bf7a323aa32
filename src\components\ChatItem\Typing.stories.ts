import { Meta, StoryObj } from '@storybook/vue3';
import Typing from './Typing.vue';

const meta = {
  title: 'ChatItem/Typing',
  component: Typing,
  tags: ['autodocs'],
  parameters: {
    backgrounds: {
      default: 'light',
    },
    docs: {
      description: {
        component: '用于显示对方正在输入的加载指示器'
      }
    }
  },
} satisfies Meta<typeof Typing>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Basic: Story = {};

// 自定义背景色示例
export const WithCustomBackground: Story = {
  decorators: [
    () => ({
      template: '<div style="background-color: #f5f5f5; padding: 20px;"><story /></div>'
    })
  ]
}; 