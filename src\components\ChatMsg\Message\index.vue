<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}-content`">
      <div :class="`${prefixCls}-body`">
        <div
          :class="`${prefixCls}-bubble${bubbleClassName ? ` ${bubbleClassName}` : ''}`"
          :style="{ width, height, maxWidth }"
          @click.stop
        >
          <!-- METRIC_ID 或 TAG_DETAIL 模式的实体信息栏 -->
          <div v-if="(queryMode === 'METRIC_ID' || queryMode === 'TAG_DETAIL') && entityInfoList.length > 0" :class="`${prefixCls}-info-bar`">
            <div :class="`${prefixCls}-main-entity-info`">
              <div 
                v-for="dimension in entityInfoList.slice(0, 4)" 
                :key="dimension.bizName"
                :class="`${prefixCls}-info-item`"
              >
                <div :class="`${prefixCls}-info-name`">{{ dimension.name }}：</div>
                <img 
                  v-if="dimension.bizName.includes('photo')" 
                  width="40" 
                  height="40" 
                  :src="dimension.value" 
                  alt="" 
                />
                <div v-else :class="`${prefixCls}-info-value`">{{ dimension.value }}</div>
              </div>
            </div>
          </div>
          
          <!-- TAG_LIST_FILTER 模式的信息栏 -->
          <div v-if="queryMode === 'TAG_LIST_FILTER'" :class="`${prefixCls}-info-bar`">
            <div :class="`${prefixCls}-main-entity-info`">
              <div :class="`${prefixCls}-info-item`">
                <div :class="`${prefixCls}-info-name`">数据模型：</div>
                <div :class="`${prefixCls}-info-value`">{{ modelName }}</div>
              </div>
              <div :class="`${prefixCls}-info-item`">
                <div :class="`${prefixCls}-info-name`">时间：</div>
                <div :class="`${prefixCls}-info-value`">
                  {{ startDate === endDate ? startDate : `${startDate} ~ ${endDate}` }}
                </div>
              </div>
              <div v-if="dimensionFilters && dimensionFilters.length > 0" :class="`${prefixCls}-info-item`">
                <div :class="`${prefixCls}-info-name`">筛选条件：</div>
                <div 
                  v-for="(filter, index) in dimensionFilters" 
                  :key="filter.name"
                  :class="`${prefixCls}-info-value`"
                >
                  <span>{{ filter.name }}：</span>
                  <span>{{ Array.isArray(filter.value) ? filter.value.join('、') : filter.value }}</span>
                  <span v-if="index !== dimensionFilters.length - 1">、</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 主要内容区域 -->
          <div :class="`${prefixCls}-children`">
            <slot></slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { PREFIX_CLS } from '../../../common/constants';
import type { EntityInfoType, ChatContextType } from '../../../common/type';

const props = defineProps<{
  position?: 'left' | 'right';
  width?: number | string;
  maxWidth?: number | string;
  height?: number | string;
  title?: string;
  followQuestions?: string[];
  bubbleClassName?: string;
  chatContext?: ChatContextType;
  entityInfo?: EntityInfoType;
  isMobileMode?: boolean;
  queryMode?: string;
}>();

const prefixCls = `${PREFIX_CLS}-message`;

// 从 chatContext 中提取数据
const modelName = computed(() => props.chatContext?.modelName);
const dateInfo = computed(() => props.chatContext?.dateInfo || { startDate: '', endDate: '' });
const startDate = computed(() => dateInfo.value.startDate);
const endDate = computed(() => dateInfo.value.endDate);
const dimensionFilters = computed(() => props.chatContext?.dimensionFilters);

// 过滤实体信息列表
const entityInfoList = computed(() => 
  props.entityInfo?.dimensions?.filter(dimension => !dimension.bizName.includes('photo')) || []
);
</script>

<style lang="less" scoped>
@import './style.less';
</style>
