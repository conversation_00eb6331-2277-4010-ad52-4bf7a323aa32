import type { StorybookConfig } from "@storybook/vue3-vite";

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [
    "@storybook/addon-essentials",
    "@storybook/addon-onboarding",
    "@chromatic-com/storybook",
    "@storybook/experimental-addon-test",
  ],
  framework: {
    name: "@storybook/vue3-vite",
    options: {},
  },
  viteFinal: async config => {
    // 添加代理配置
    if (config.server) {
      config.server.proxy = {
        // 代理所有API请求到后端服务
        // "/api": {
        //   target: "http://127.0.0.1:19080",
        //   changeOrigin: true,
        //   // rewrite: (path) => path.replace(/^\/api/, '/api')
        // },
        "/openapi": {
          target: "http://127.0.0.1:19080",
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/openapi/, '')
        },
        "/api": {
          target: "http://192.168.3.150:8888",
          // target: "http://192.168.3.20/v1",
          changeOrigin: true,
          rewrite: path => {
            // console.log("path", path.replace(/^\/api/, ""));
            // return path.replace(/^\/api/, "");
            return path;
          },
        },
      };
    }
    return config;
  },
};
export default config;
