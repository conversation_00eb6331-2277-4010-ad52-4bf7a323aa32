<script setup lang="ts">
import { computed } from 'vue';
import { CLS_PREFIX } from '../../common/constants';
import { FieldType } from '../../common/type';
import { isMobile } from '../../utils/utils';

interface Props {
  metrics: FieldType[];
  defaultMetric?: FieldType;
  currentMetric?: FieldType;
  isMetricCard?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'selectMetric', metric?: FieldType): void;
}>();

const DEFAULT_DIMENSION_COUNT = isMobile ? 2 : 5;
const prefixCls = `${CLS_PREFIX}-metric-options`;

const defaultMetrics = computed(() => 
  props.metrics
    .filter(metric => metric.id !== props.defaultMetric?.id)
    .slice(0, DEFAULT_DIMENSION_COUNT)
);

const sectionClass = computed(() => ({
  [`${prefixCls}-section`]: true,
  [`${prefixCls}-metric-card`]: props.isMetricCard,
}));

const handleMetricClick = (metric: FieldType) => {
  emit('selectMetric', props.currentMetric?.id === metric.id ? props.defaultMetric : metric);
};

const handleCancelSelect = () => {
  emit('selectMetric', props.defaultMetric);
};
</script>

<template>
  <div v-if="defaultMetrics.length" :class="prefixCls">
    <div :class="sectionClass">
      <div :class="`${prefixCls}-title`">推荐相关指标：</div>
      <div :class="`${prefixCls}-content`">
        <div v-for="(metric, index) in defaultMetrics" :key="metric.id">
          <span
            :class="[
              `${prefixCls}-content-item-name`,
              {
                [`${prefixCls}-content-item-active`]: currentMetric?.id === metric.id,
              },
            ]"
            @click="handleMetricClick(metric)"
          >
            {{ metric.name }}
          </span>
          <span v-if="index !== defaultMetrics.length - 1">、</span>
        </div>
      </div>
      <div
        v-if="currentMetric?.id !== defaultMetric?.id"
        :class="`${prefixCls}-cancel-select`"
        @click="handleCancelSelect"
      >
        取消
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import '../../styles/index.less';

@metric-options-prefix-cls: ~'@{supersonic-chat-prefix}-metric-options';

.@{metric-options-prefix-cls} {
  display: flex;
  flex-direction: column;

  &-section {
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    column-gap: 6px;
    row-gap: 4px;
  }

  &-metric-card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.14), 0 0 2px rgba(0, 0, 0, 0.12);
    border-radius: 8px;
    background-color: #fff;
    width: fit-content;
    padding: 6px 12px;
    font-size: 12px;
  }

  &-title {
    color: var(--text-color-third);
  }
  
  &-content {
    display: flex;
    align-items: center;
  }

  &-content-item-name {
    color: var(--chat-blue);
    font-weight: 500;
    border-bottom: 1px solid var(--chat-blue);
    padding: 1px;
    cursor: pointer;
  }

  &-content-item-active {
    color: #fff;
    border-bottom: none;
    background-color: var(--chat-blue);
    border-radius: 2px;
  }

  &-menu-item-active {
    color: var(--chat-blue);
  }

  &-cancel-select {
    margin-left: 12px;
    color: var(--text-color-third);
    cursor: pointer;
    padding: 0 4px;
    border: 1px solid var(--text-color-third);
    border-radius: 4px;
    font-size: 12px;

    &:hover {
      color: var(--chat-blue);
      border-color: var(--chat-blue);
    }
  }
}
</style> 