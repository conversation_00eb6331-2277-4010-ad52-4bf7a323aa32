// import { fn } from "@storybook/test";
import type { Meta, StoryObj } from "@storybook/vue3";

import CopilotAvatar from "./index.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta = {
  title: "Chat/CopilotAvatar",
  component: CopilotAvatar,
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  argTypes: {},
  decorators: [
    story => ({
      components: { story },
      template: `<div style="padding: 20px; display: flex;"><story /></div>`,
    }),
  ],
} satisfies Meta<typeof CopilotAvatar>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基本展示
export const Default: Story = {
  render: () => ({
    components: {
      CopilotAvatar,
    },
    template: `
      <div>
        <CopilotAvatar />
      </div>
    `,
  }),
};

// 不同背景色展示
export const DifferentBackgrounds: Story = {
  render: () => ({
    components: {
      CopilotAvatar,
    },
    template: `
      <div style="display: flex; align-items: center; gap: 20px;">
        <div style="padding: 10px; background-color: #ffffff;">
          <p style="margin-bottom: 10px; font-size: 12px;">白色背景</p>
          <CopilotAvatar />
        </div>
        <div style="padding: 10px; background-color: #f0f2f5;">
          <p style="margin-bottom: 10px; font-size: 12px;">浅灰背景</p>
          <CopilotAvatar />
        </div>
        <div style="padding: 10px; background-color: #001529;">
          <p style="margin-bottom: 10px; font-size: 12px; color: white;">深色背景</p>
          <CopilotAvatar />
        </div>
      </div>
    `,
  }),
};
