<template>
  <div>
    <component :is="getIconComponent" />
  </div>
</template>

<script setup lang="ts">
import { BlockEnum } from "@/DifyChat/types/app";
import type { PropType } from "vue";
import {
  Agent,
  Answer,
  Assigner,
  Code,
  DocsExtractor,
  End,
  Home,
  Http,
  IfElse,
  Iteration,
  IterationStart,
  KnowledgeRetrieval,
  ListFilter,
  Llm,
  Loop,
  LoopEnd,
  ParameterExtractor,
  QuestionClassifier,
  TemplatingTransform,
  VariableX,
} from "./NodeIcons/index";
import { computed } from "vue";

const { node_type } = defineProps({
  node_type: {
    type: String as PropType<BlockEnum>,
    required: true,
  },
});

const getIconComponent = computed(() => {
  const iconMap = {
    [BlockEnum.Start]: Home,
    [BlockEnum.LLM]: Llm,
    [BlockEnum.Code]: Code,
    [BlockEnum.End]: End,
    [BlockEnum.IfElse]: IfElse,
    [BlockEnum.HttpRequest]: Http,
    [BlockEnum.Answer]: Answer,
    [BlockEnum.KnowledgeRetrieval]: KnowledgeRetrieval,
    [BlockEnum.QuestionClassifier]: QuestionClassifier,
    [BlockEnum.TemplateTransform]: TemplatingTransform,
    [BlockEnum.VariableAssigner]: VariableX,
    [BlockEnum.VariableAggregator]: VariableX,
    [BlockEnum.Assigner]: Assigner,
    [BlockEnum.Tool]: VariableX,
    [BlockEnum.IterationStart]: IterationStart,
    [BlockEnum.Iteration]: Iteration,
    [BlockEnum.LoopStart]: Loop,
    [BlockEnum.Loop]: Loop,
    [BlockEnum.LoopEnd]: LoopEnd,
    [BlockEnum.ParameterExtractor]: ParameterExtractor,
    [BlockEnum.DocExtractor]: DocsExtractor,
    [BlockEnum.ListFilter]: ListFilter,
    [BlockEnum.Agent]: Agent,
  };

  return iconMap[node_type] || Home;
});
</script>

<style scoped></style>
