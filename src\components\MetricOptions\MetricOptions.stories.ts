import type { Meta, StoryObj } from '@storybook/vue3';
import MetricOptions from './index.vue';

const meta = {
  title: 'Components/MetricOptions',
  component: MetricOptions,
  tags: ['autodocs'],
  argTypes: {
    metrics: {
      control: 'array',
      description: '指标列表',
    },
    defaultMetric: {
      control: 'object',
      description: '默认选中的指标',
    },
    currentMetric: {
      control: 'object',
      description: '当前选中的指标',
    },
    isMetricCard: {
      control: 'boolean',
      description: '是否为指标卡片样式',
    },
    onSelectMetric: {
      description: '选择指标时的回调函数',
    },
  },
} satisfies Meta<typeof MetricOptions>;

export default meta;
type Story = StoryObj<typeof meta>;

// 模拟数据
const mockMetrics = [
  { id: '1', name: '总销售额' },
  { id: '2', name: '订单量' },
  { id: '3', name: '客单价' },
  { id: '4', name: '转化率' },
  { id: '5', name: '退款率' },
];

export const Default: Story = {
  args: {
    metrics: mockMetrics,
    defaultMetric: mockMetrics[0],
    currentMetric: mockMetrics[0],
    isMetricCard: false,
  },
};

export const WithSelectedMetric: Story = {
  args: {
    metrics: mockMetrics,
    defaultMetric: mockMetrics[0],
    currentMetric: mockMetrics[1],
    isMetricCard: false,
  },
};

export const AsMetricCard: Story = {
  args: {
    metrics: mockMetrics,
    defaultMetric: mockMetrics[0],
    currentMetric: mockMetrics[0],
    isMetricCard: true,
  },
};

export const MobileView: Story = {
  args: {
    metrics: mockMetrics,
    defaultMetric: mockMetrics[0],
    currentMetric: mockMetrics[0],
    isMetricCard: false,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
}; 