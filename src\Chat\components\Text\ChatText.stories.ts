// import { fn } from "@storybook/test";
import type { Meta, StoryObj } from "@storybook/vue3";

import Text from "./index.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta = {
  title: "Chat/Text",
  component: Text,
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  argTypes: {
    position: {
      control: "select",
      options: ["left", "right"],
      description: "消息气泡的位置",
    },
    data: {
      control: "text",
      description: "消息内容",
    },
    quote: {
      control: "text",
      description: "引用内容，仅在position为right时显示",
    },
    anonymousUser: {
      control: "boolean",
      description: "是否为匿名用户",
    },
  },
  args: {
    position: "left",
    data: "这是一条测试消息",
    quote: "",
    anonymousUser: false,
  },
} satisfies Meta<typeof Text>;

export default meta;
type Story = StoryObj<typeof meta>;
/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */

export const Default: Story = {
  args: {
    position: "left",
    data: "这是一条来自左侧的测试消息",
  },
//   render: args => ({
//     components: { Text },
//     setup() {
//       return { args };
//     },
//     template: `
//         <Text v-bind="args">
//           <div>这是消息内容</div>
//         </Text>
//       `,
//   }),
};

export const Right: Story = {
  args: {
    position: "right",
    data: "这是一条来自右侧的测试消息",
  },
//   render: args => ({
//     components: { Text },
//     setup() {
//       return { args };
//     },
//     template: `
//           <Text v-bind="args">
//             <div>这是消息内容</div>
//           </Text>
//         `,
//   }),
};

export const WithQuote: Story = {
  args: {
    position: "right",
    data: "这是一条带引用的消息",
    quote: "这是引用的内容",
  },
};

export const AnonymousUser: Story = {
  args: {
    position: "right",
    data: "这是一条匿名用户的消息",
    anonymousUser: true,
  },
};
