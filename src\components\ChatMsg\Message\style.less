@import '../../../styles/index.less';

@msg-prefix-cls: ~'@{supersonic-chat-prefix}-message';

.@{msg-prefix-cls} {
  &-title-bar {
    display: flex;
    align-items: baseline;
    column-gap: 10px;
    margin-bottom: 6px;
  }

  &-model-name {
    color: var(--text-color);
    margin-left: 4px;
    font-weight: 500;
    font-size: 15px;
  }

  &-top-bar {
    position: relative;
    max-width: 80%;
    color: var(--text-color-third);
    font-size: 13px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &-content {
    display: flex;
    align-items: flex-start;
  }

  &-body {
    width: 100%;
  }

  &-bubble {
    position: relative;
    box-sizing: border-box;
    min-width: 1px;
    max-width: 100%;
    padding: 8px 16px 10px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid transparent;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.14), 0 0 2px rgba(0, 0, 0, 0.12);
  }

  &-filter-section {
    display: flex;
    align-items: center;
    color: var(--text-color-secondary);
    font-weight: normal;
    font-size: 13px;
  }

  &-filter-values {
    display: flex;
    align-items: center;
    column-gap: 6px;
  }

  &-filter-item {
    padding: 2px 12px;
    color: var(--text-color-secondary);
    background-color: #edf2f2;
    border-radius: 13px;
    max-width: 200px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  &-tip {
    margin-left: 6px;
    color: var(--text-color-third);
  }

  &-info-bar {
    display: flex;
    align-items: center;
    row-gap: 12px;
    flex-wrap: wrap;
    margin-top: 2px;
    margin-bottom: 12px;
    column-gap: 20px;
    color: var(--text-color-secondary);
    background: rgba(133, 156, 241, 0.1);
    padding: 4px 12px;
    width: fit-content;
    border-radius: 8px;
  }

  &-main-entity-info {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    column-gap: 20px;
    row-gap: 10px;
  }

  &-info-item {
    display: flex;
    align-items: center;
  }

  &-info-name {
    color: var(--text-color-secondary);
  }

  &-info-value {
    color: var(--text-color);
    font-weight: 500;
  }
}


