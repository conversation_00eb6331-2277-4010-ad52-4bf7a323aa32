import { Meta, StoryObj } from '@storybook/vue3';
import Table from './index.vue';
import { MsgDataType } from '../../../common/type';

const meta: Meta<typeof Table> = {
  component: Table,
  title: 'Components/ChatMsg/Table',
  tags: ['autodocs'],
  argTypes: {
    question: {
      control: 'text',
      description: '表格问题'
    },
    loading: {
      control: 'boolean',
      description: '加载状态'
    },
    size: {
      control: 'select',
      options: ['small', 'middle', 'large'],
      description: '表格大小'
    },
    onApplyAuth: {
      action: 'onApplyAuth',
      description: '申请权限回调函数'
    }
  },
  parameters: {
    docs: {
      description: {
        component: '表格组件，用于展示结构化数据'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof Table>;

// 基础表格样例
export const Basic: Story = {
  args: {
    question: '2023年各月份销售数据',
    loading: false,
    size: 'middle',
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          name: '月份',
          bizName: 'month',
          showType: 'STRING',
          authorized: true
        },
        {
          name: '销售额',
          bizName: 'sales',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: true
        },
        {
          name: '增长率',
          bizName: 'growth_rate',
          showType: 'NUMBER',
          dataFormatType: 'percent',
          dataFormat: {
            decimalPlaces: 2,
            needMultiply100: true
          },
          authorized: true
        }
      ],
      queryResults: [
        { month: '1月', sales: '15689234.56', growth_rate: '0.125' },
        { month: '2月', sales: '16892345.67', growth_rate: '0.083' },
        { month: '3月', sales: '18923456.78', growth_rate: '0.156' }
      ],
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    }
  }
};

// 带图片的表格
export const WithPhotos: Story = {
  args: {
    question: '产品列表',
    loading: false,
    size: 'middle',
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          name: '产品图片',
          bizName: 'product_photo',
          showType: 'STRING',
          authorized: true
        },
        {
          name: '产品名称',
          bizName: 'product_name',
          showType: 'STRING',
          authorized: true
        },
        {
          name: '价格',
          bizName: 'price',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: true
        }
      ],
      queryResults: [
        { 
          product_photo: 'https://picsum.photos/40/40',
          product_name: '产品A',
          price: '199.99'
        },
        { 
          product_photo: 'https://picsum.photos/40/40',
          product_name: '产品B',
          price: '299.99'
        }
      ],
      entityInfo: {
        dataSetInfo: {
          name: '产品数据'
        }
      }
    }
  }
};

// 带日期排序的表格
export const WithDateSort: Story = {
  args: {
    question: '销售趋势',
    loading: false,
    size: 'middle',
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          name: '日期',
          bizName: 'date',
          type: 'DATE',
          showType: 'STRING',
          authorized: true
        },
        {
          name: '销售额',
          bizName: 'sales',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: true
        }
      ],
      queryResults: [
        { date: '2023-03-01', sales: '15689234.56' },
        { date: '2023-03-02', sales: '16892345.67' },
        { date: '2023-03-03', sales: '18923456.78' }
      ],
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    }
  }
};

// 加载中状态
export const Loading: Story = {
  args: {
    question: '销售数据',
    loading: true,
    size: 'middle',
    data: {
      queryMode: 'SQL',
      queryColumns: [],
      queryResults: [],
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    }
  }
};

// 需要权限认证
export const NeedAuthorization: Story = {
  args: {
    question: '销售数据',
    loading: false,
    size: 'middle',
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          name: '销售额',
          bizName: 'sales',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: false
        }
      ],
      queryResults: [],
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    },
    onApplyAuth: (model: string) => console.log('申请权限:', model)
  }
}; 