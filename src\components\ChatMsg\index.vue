<template>
  <div :class="chartMsgClass" :style="style">
    <div v-if="dataSource?.length === 0">暂无数据</div>
    <div v-else>
      <component
        :is="getMsgContent()"
      />
      <div
        v-if="(isMultipleMetric || existDrillDownDimension) && !isSimpleMode"
        :class="`${prefixCls}-bottom-tools ${
          getMsgContentType() === MsgContentTypeEnum.METRIC_CARD
            ? `${prefixCls}-metric-card-tools`
            : ''
        } ${isMobile ? 'mobile' : ''}`"
      >
        <MetricOptions
          v-if="isMultipleMetric"
          :metrics="chatContext?.metrics"
          :default-metric="defaultMetricField"
          :current-metric="activeMetricField"
          @select-metric="onSwitchMetric"
        />
        <DrillDownDimensions
          v-if="existDrillDownDimension"
          :drill-down-dimensions="data?.recommendedDimensions || []"
          :drill-down-dimension="drillDownDimension"
          :second-drill-down-dimension="secondDrillDownDimension"
          :origin-dimensions="chatContext?.dimensions"
          :dimension-filters="chatContext?.dimensionFilters"
          @select-dimension="onSelectDimension"
          @select-second-dimension="onSelectSecondDimension"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, h as vueRender } from 'vue';
import Bar from './Bar/index.vue';
import MetricCard from './MetricCard/index.vue';
import MetricTrend from './MetricTrend/index.vue';
import MarkDown from './MarkDown/index.vue';
import Table from './Table/index.vue';
import Text from './Text/index.vue';
import DrillDownDimensions from '../DrillDownDimensions/index.vue';
import MetricOptions from '../MetricOptions/index.vue';
import { queryData } from '../../service';
import { isMobile } from '../../utils/utils';
import { PREFIX_CLS, MsgContentTypeEnum } from '../../common/constants';
import type { ColumnType, DrillDownDimensionType, FieldType, MsgDataType } from '../../common/type';

// Props 定义
const props = defineProps<{
  queryId?: number;
  question: string;
  data: MsgDataType;
  chartIndex: number;
  triggerResize?: boolean;
  forceShowTable?: boolean;
  isSimpleMode?: boolean;
}>();

// 事件定义
const emit = defineEmits<{
  (e: 'msgContentTypeChange', type: MsgContentTypeEnum): void;
}>();

// 数据提取
const { queryColumns, queryResults, chatContext, queryMode } = props.data || {};
const { dimensionFilters, elementMatches } = chatContext || {};

// 状态声明
const columns = ref<ColumnType[]>([]);
const referenceColumn = ref<ColumnType | undefined>();
const dataSource = ref<any[]>(queryResults || []);
const drillDownDimension = ref<DrillDownDimensionType | undefined>();
const secondDrillDownDimension = ref<DrillDownDimensionType | undefined>();
const loading = ref(false);
const defaultMetricField = ref<FieldType | undefined>();
const activeMetricField = ref<FieldType | undefined>();
const dateModeValue = ref<any>();
const currentDateOption = ref<number | undefined>();

// 定义前缀类名
const prefixCls = `${PREFIX_CLS}-chat-msg`;

// 更新列信息
const updateColumns = (queryColumnsValue: ColumnType[]) => {
  referenceColumn.value = queryColumnsValue.find(item => item.showType === 'more');
  columns.value = queryColumnsValue.filter(item => item.showType !== 'more');
};

// 监听props数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    updateColumns(newData.queryColumns || []);
    dataSource.value = newData.queryResults || [];
    defaultMetricField.value = newData.chatContext?.metrics?.[0];
    activeMetricField.value = newData.chatContext?.metrics?.[0];
    dateModeValue.value = newData.chatContext?.dateInfo?.dateMode;
    currentDateOption.value = newData.chatContext?.dateInfo?.unit;
    drillDownDimension.value = undefined;
    secondDrillDownDimension.value = undefined;
  }
}, { immediate: true });

// 计算指标字段
const metricFields = computed(() => columns.value.filter(item => item.showType === 'NUMBER'));

// 获取消息内容类型
const getMsgContentType = () => {
  const singleData = dataSource.value.length === 1;
  const dateField = columns.value.find(item => item.showType === 'DATE' || item.type === 'DATE');
  const categoryField = columns.value.filter(item => item.showType === 'CATEGORY');
  const metricFieldsValue = columns.value.filter(item => item.showType === 'NUMBER');
  
  if (!columns.value.length) {
    return undefined;
  }
  
  if (props.isSimpleMode) {
    return MsgContentTypeEnum.MARKDOWN;
  }
  
  if (props.forceShowTable) {
    return MsgContentTypeEnum.TABLE;
  }
  
  const isDslMetricCard =
    queryMode === 'LLM_S2SQL' && singleData && metricFieldsValue.length === 1 && columns.value.length === 1;
  const isMetricCard = (queryMode.includes('METRIC') || isDslMetricCard) && singleData;
  const isText = !queryColumns?.length;

  if (isText) {
    return MsgContentTypeEnum.TEXT;
  }

  if (isMetricCard) {
    return MsgContentTypeEnum.METRIC_CARD;
  }

  const isTable =
    !isText &&
    !isMetricCard &&
    (categoryField.length > 1 ||
      queryMode === 'TAG_DETAIL' ||
      queryMode === 'ENTITY_DIMENSION' ||
      dataSource.value?.length === 1 ||
      (categoryField.length === 1 && metricFieldsValue.length === 0));

  if (isTable) {
    return MsgContentTypeEnum.TABLE;
  }
  
  const isMetricTrend =
    dateField &&
    metricFieldsValue.length > 0 &&
    categoryField.length <= 1 &&
    !(metricFieldsValue.length > 1 && categoryField.length > 0) &&
    !dataSource.value.every(item => item[dateField.bizName] === dataSource.value[0][dateField.bizName]);

  if (isMetricTrend) {
    return MsgContentTypeEnum.METRIC_TREND;
  }

  const isMetricBar =
    categoryField?.length > 0 &&
    metricFieldsValue?.length === 1 &&
    (isMobile ? dataSource.value?.length <= 5 : dataSource.value?.length <= 50);

  if (isMetricBar) {
    return MsgContentTypeEnum.METRIC_BAR;
  }
  
  return MsgContentTypeEnum.TABLE;
};

// 获取消息样式
const getMsgStyle = (type: MsgContentTypeEnum) => {
  if (isMobile) {
    return { maxWidth: 'calc(100vw - 20px)' };
  }
  
  if (!queryResults?.length || !queryColumns?.length) {
    return {};
  }
  
  if (type === MsgContentTypeEnum.METRIC_BAR) {
    return {
      [queryResults.length > 5 ? 'width' : 'minWidth']: `${queryResults.length * 150}px`,
    };
  }
  
  if (type === MsgContentTypeEnum.TABLE) {
    return {
      [queryColumns.length > 5 ? 'width' : 'minWidth']: `${queryColumns.length * 150}px`,
    };
  }
  
  if (type === MsgContentTypeEnum.METRIC_TREND) {
    return { width: 'calc(100vw - 410px)' };
  }
  
  return {};
};

// 监听内容类型变化，发送事件
watch(getMsgContentType, (type) => {
  if (type) {
    emit('msgContentTypeChange', type);
  }
}, { immediate: true });

// 获取消息内容
const getMsgContent = () => {
  const contentType = getMsgContentType();
  
  switch (contentType) {
    case MsgContentTypeEnum.TEXT:
      return () => vueRender(Text, {
        columns: columns.value,
        referenceColumn: referenceColumn.value,
        dataSource: dataSource.value
      });
    
    case MsgContentTypeEnum.METRIC_CARD:
      return () => vueRender(MetricCard, {
        data: { 
          ...props.data, 
          queryColumns: columns.value, 
          queryResults: dataSource.value 
        },
        question: props.question,
        loading: loading.value
      });
    
    case MsgContentTypeEnum.TABLE:
      return () => vueRender(Table, {
        question: props.question,
        data: { 
          ...props.data, 
          queryColumns: columns.value, 
          queryResults: dataSource.value 
        },
        loading: loading.value
      });
    
    case MsgContentTypeEnum.METRIC_TREND:
      return () => vueRender(MetricTrend, {
        data: {
          ...props.data,
          queryColumns: columns.value,
          queryResults: dataSource.value,
        },
        question: props.question,
        loading: loading.value,
        chartIndex: props.chartIndex,
        triggerResize: props.triggerResize,
        activeMetricField: activeMetricField.value,
        drillDownDimension: drillDownDimension.value,
        currentDateOption: currentDateOption.value,
        onSelectDateOption: selectDateOption
      });
    
    case MsgContentTypeEnum.METRIC_BAR:
      return () => vueRender(Bar, {
        data: { 
          ...props.data, 
          queryColumns: columns.value, 
          queryResults: dataSource.value 
        },
        question: props.question,
        triggerResize: props.triggerResize,
        loading: loading.value,
        metricField: metricFields.value[0]
      });
    
    case MsgContentTypeEnum.MARKDOWN:
      return () => vueRender('div', { style: { maxHeight: '800px' } }, [
        vueRender(MarkDown, {
          markdown: props.data.textResult,
          loading: loading.value
        })
      ]);
    
    default:
      return () => vueRender(Table, {
        question: props.question,
        data: { 
          ...props.data, 
          queryColumns: columns.value, 
          queryResults: dataSource.value 
        },
        loading: loading.value
      });
  }
};

// 加载数据
const onLoadData = async (value: any) => {
  loading.value = true;
  try {
    const res: any = await queryData({
      ...chatContext,
      ...value,
      queryId: props.queryId,
      parseId: chatContext?.id,
    });
    
    if (res.code === 200) {
      updateColumns(res.data?.queryColumns || []);
      dataSource.value = res.data?.queryResults || [];
    }
  } finally {
    loading.value = false;
  }
};

// 选择维度
const onSelectDimension = async (dimension?: DrillDownDimensionType) => {
  loading.value = true;
  drillDownDimension.value = dimension;
  
  await onLoadData({
    dateInfo: {
      ...chatContext?.dateInfo,
      dateMode: dateModeValue.value,
      unit: currentDateOption.value || chatContext?.dateInfo?.unit,
    },
    dimensions: dimension
      ? [...(chatContext?.dimensions || []), dimension]
      : chatContext?.dimensions,
    metrics: [activeMetricField.value || defaultMetricField.value],
  });
};

// 选择第二个维度
const onSelectSecondDimension = (dimension?: DrillDownDimensionType) => {
  secondDrillDownDimension.value = dimension;
  
  onLoadData({
    dateInfo: {
      ...chatContext?.dateInfo,
      dateMode: dateModeValue.value,
      unit: currentDateOption.value || chatContext?.dateInfo?.unit,
    },
    dimensions: [
      ...(chatContext?.dimensions || []),
      ...(drillDownDimension.value ? [drillDownDimension.value] : []),
      ...(dimension ? [dimension] : []),
    ],
    metrics: [activeMetricField.value || defaultMetricField.value],
  });
};

// 切换指标
const onSwitchMetric = (metricField?: FieldType) => {
  activeMetricField.value = metricField;
  
  onLoadData({
    dateInfo: {
      ...chatContext?.dateInfo,
      dateMode: dateModeValue.value,
      unit: currentDateOption.value || chatContext?.dateInfo?.unit,
    },
    dimensions: drillDownDimension.value
      ? [...(chatContext?.dimensions || []), drillDownDimension.value]
      : chatContext?.dimensions,
    metrics: [metricField || defaultMetricField.value],
  });
};

// 选择日期选项
const selectDateOption = (dateOption: number) => {
  currentDateOption.value = dateOption;
  dateModeValue.value = 'RECENT';
  
  onLoadData({
    metrics: [activeMetricField.value || defaultMetricField.value],
    dimensions: drillDownDimension.value
      ? [...(chatContext?.dimensions || []), drillDownDimension.value]
      : chatContext?.dimensions,
    dateInfo: {
      ...chatContext?.dateInfo,
      dateMode: 'RECENT',
      unit: dateOption,
    },
  });
};

// 计算样式类名
const chartMsgClass = computed(() => {
  const type = getMsgContentType();
  const notTableOrMarkdown = 
    type !== undefined && 
    ![MsgContentTypeEnum.TABLE, MsgContentTypeEnum.MARKDOWN].includes(type);
  
  return {
    [prefixCls]: notTableOrMarkdown
  };
});

// 计算实体相关属性
const entityId = computed(() => 
  dimensionFilters?.length > 0 ? dimensionFilters[0].value : undefined
);

const entityName = computed(() => 
  elementMatches?.find((item: any) => item.element?.type === 'ID')?.element?.name
);

const isEntityMode = computed(() => 
  (queryMode === 'TAG_LIST_FILTER' || queryMode === 'METRIC_TAG') &&
  typeof entityId.value === 'string' &&
  entityName.value !== undefined
);

const existDrillDownDimension = computed(() => 
  queryMode?.includes('METRIC') &&
  getMsgContentType() !== MsgContentTypeEnum.TEXT &&
  !isEntityMode.value
);

const recommendMetrics = computed(() => 
  chatContext?.metrics?.filter(metric =>
    queryColumns?.every(queryColumn => queryColumn.bizName !== metric.bizName)
  )
);

const isMultipleMetric = computed(() => 
  (queryMode?.includes('METRIC') || queryMode === 'LLM_S2SQL') &&
  recommendMetrics.value?.length > 0 &&
  queryColumns?.filter(column => column.showType === 'NUMBER').length === 1
);

// 计算样式
const style = computed(() => {
  const type = getMsgContentType();
  return type ? getMsgStyle(type) : undefined;
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
