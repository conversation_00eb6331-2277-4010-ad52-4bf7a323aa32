import { <PERSON>a, StoryObj } from '@storybook/vue3';
import ExpandParseTip from './ExpandParseTip.vue';
import { ChatContextType, FilterItemType, ModelType, EntityInfoType, DateInfoType, SqlInfoType } from '../../common/type';
import { ChatContextTypeQueryTypeEnum } from '../../common/constants';

// 模拟数据
const createMockParseInfo = (id: number, textInfo: string, queryMode: string = 'LLM_S2SQL', properties?: any): Partial<ChatContextType> => {
  return {
    id,
    aggType: 'SUM',
    modelId: 100,
    modelName: '销售模型',
    dataSet: {
      alias: '销售',
      bizName: 'sales',
      id: 1,
      model: 100,
      name: '销售数据',
      modelNames: ['销售数据'],
      type: 'BASIC',
      useCnt: 1
    } as ModelType,
    dateInfo: {
      dateList: [],
      dateMode: 'ABSOLUTE',
      period: 'day',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      text: '2023年全年',
      unit: 1
    } as DateInfoType,
    dimensions: [
      { bizName: 'region', itemId: 1, id: 1, name: '地区', status: 1, model: 100, type: 'DIMENSION', value: '地区' },
      { bizName: 'category', itemId: 2, id: 2, name: '品类', status: 1, model: 100, type: 'DIMENSION', value: '品类' }
    ],
    metrics: [
      { bizName: 'sales', itemId: 3, id: 3, name: '销售额', status: 1, model: 100, type: 'METRIC', value: '销售额' },
      { bizName: 'profit', itemId: 4, id: 4, name: '利润', status: 1, model: 100, type: 'METRIC', value: '利润' }
    ],
    entity: { alias: ['商品'], id: 1 },
    entityInfo: {
      dataSetInfo: {
        bizName: 'product',
        itemId: 1,
        name: '商品',
        primaryEntityBizName: 'product',
        value: 'product',
        words: ['商品']
      },
      dimensions: [
        { bizName: 'brand', itemId: 5, id: 5, name: '品牌', status: 1, model: 100, type: 'DIMENSION', value: '苹果' },
        { bizName: 'size', itemId: 6, id: 6, name: '尺寸', status: 1, model: 100, type: 'DIMENSION', value: '大' }
      ],
      metrics: [],
      entityId: 1
    } as unknown as EntityInfoType,
    elementMatches: [
      {
        element: {
          type: 'ID',
          name: 'iPhone 13'
        }
      }
    ],
    nativeQuery: false,
    queryMode,
    queryType: ChatContextTypeQueryTypeEnum.AGGREGATE,
    dimensionFilters: [
      { elementID: 1, name: '价格', bizName: 'price', operator: '>=', value: 1000 },
      { elementID: 2, name: '城市', bizName: 'city', operator: 'IN', value: ['北京', '上海', '广州'] }
    ],
    properties: properties || {},
    sqlInfo: {
      parsedS2SQL: '',
      correctedS2SQL: '',
      querySQL: ''
    } as SqlInfoType,
    textInfo
  };
};

// 更多关于如何设置故事：https://storybook.js.org/docs/writing-stories
const meta = {
  title: 'ChatItem/ExpandParseTip',
  component: ExpandParseTip,
  // 此组件将有一个自动生成的文档页面：https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  argTypes: {
    isSimpleMode: {
      control: 'boolean',
      description: '简单模式',
      table: {
        category: '外观',
        defaultValue: { summary: false }
      }
    },
    parseInfoOptions: {
      control: 'object',
      description: '解析信息选项',
      table: {
        category: '数据',
        type: { summary: 'ChatContextType[]' }
      }
    },
    agentId: {
      control: 'number',
      description: '代理ID',
      table: {
        category: '配置',
        defaultValue: { summary: undefined }
      }
    },
    integrateSystem: {
      control: 'text',
      description: '集成系统',
      table: {
        category: '配置',
        defaultValue: { summary: undefined }
      }
    },
    parseTimeCost: {
      control: 'number',
      description: '解析耗时(ms)',
      table: {
        category: '开发',
        defaultValue: { summary: undefined }
      }
    },
    isDeveloper: {
      control: 'boolean',
      description: '是否为开发者',
      table: {
        category: '开发',
        defaultValue: { summary: false }
      }
    },
    onSelectParseInfo: { 
      action: 'parseInfoSelected',
      description: '选择解析信息回调'
    },
    onSwitchEntity: { 
      action: 'entitySwitched',
      description: '切换实体回调'
    },
    onFiltersChange: { 
      action: 'filtersChanged',
      description: '过滤器变更回调'
    },
    onDateInfoChange: { 
      action: 'dateInfoChanged',
      description: '日期信息变更回调'
    },
    onRefresh: { 
      action: 'refreshTriggered',
      description: '刷新回调'
    },
    handlePresetClick: { 
      action: 'presetClicked',
      description: '预设点击回调'
    }
  },
  parameters: {
    docs: {
      description: {
        component: `
# ExpandParseTip 组件

ExpandParseTip 组件用于展示查询意图解析的结果，让用户能够选择最合适的解析结果进行后续查询。

## 功能

- 展示多个可能的解析结果
- 允许用户选择合适的解析意图
- 显示解析耗时（仅开发者模式）
- 支持简单模式和完整模式
- 展示数据集、过滤条件等详细信息

## 使用场景

当用户提出一个可能有歧义的查询时，系统可能会产生多个解析结果。此时可以使用该组件展示所有的解析结果，让用户选择最合适的一个继续查询。
`
      }
    }
  },
  args: {
    isSimpleMode: false,
    parseInfoOptions: [
      createMockParseInfo(1, '查询全年各地区销售额和利润'),
      createMockParseInfo(2, '查询各品类的销售额和利润', 'METRIC_GROUPBY')
    ] as ChatContextType[],
    agentId: 1,
    integrateSystem: 'system1',
    parseTimeCost: 230,
    isDeveloper: true,
    onSelectParseInfo: (parseInfo: ChatContextType) => console.log('选择了解析结果', parseInfo),
    onSwitchEntity: (entityId: string) => console.log('切换实体', entityId),
    onFiltersChange: (filters: FilterItemType[]) => console.log('过滤器变更', filters),
    onDateInfoChange: (dateRange: any) => console.log('日期变更', dateRange),
    onRefresh: (parseInfo: ChatContextType) => console.log('刷新', parseInfo),
    handlePresetClick: (range: any) => console.log('预设点击', range)
  },
} satisfies Meta<typeof ExpandParseTip>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基本用例 - 多选择
export const 多解析选择: Story = {
  args: {
    parseInfoOptions: [
      createMockParseInfo(1, '查询全年各地区销售额和利润', 'METRIC_GROUPBY'),
      createMockParseInfo(2, '查询各品类的销售额和利润', 'TAG_DETAIL')
    ] as ChatContextType[]
  }
};

// 简单模式
export const 简单模式: Story = {
  args: {
    isSimpleMode: true,
    parseInfoOptions: [
      createMockParseInfo(1, '查询全年各地区销售额和利润'),
      createMockParseInfo(2, '查询各品类的销售额和利润')
    ] as ChatContextType[]
  }
};

// 带插件/工具
export const 工具解析: Story = {
  args: {
    parseInfoOptions: [
      createMockParseInfo(1, '使用ChatGPT解答这个问题', 'LLM_S2SQL', { 
        type: 'plugin', 
        name: 'ChatGPT'
      })
    ] as ChatContextType[]
  }
};

// 实体查询
export const 实体查询: Story = {
  args: {
    parseInfoOptions: [
      createMockParseInfo(1, '查询iPhone 13的销售情况', 'ENTITY_DETAIL')
    ] as ChatContextType[]
  }
};

// 单一选择
export const 单一解析结果: Story = {
  args: {
    parseInfoOptions: [
      createMockParseInfo(1, '查询全年销售总额')
    ] as ChatContextType[]
  }
};

// 开发者模式关闭
export const 普通用户模式: Story = {
  args: {
    isDeveloper: false,
    parseTimeCost: 230,
    parseInfoOptions: [
      createMockParseInfo(1, '查询全年各地区销售额和利润')
    ] as ChatContextType[]
  }
}; 