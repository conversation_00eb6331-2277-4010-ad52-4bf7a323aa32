<template>
  <a-popover
    placement="bottomLeft"
    trigger="click"
    :open="recommendOptionsOpen"
    @openChange="handleOpenChange"
  >
    <template #content>
      <RecommendOptions
        :entityId="entityId"
        :modelId="chatContext.modelId"
        :modelName="chatContext.modelName"
        @select="switchEntity"
      />
    </template>
    <div :class="`${prefixCls}-tip-item-value ${prefixCls}-switch-entity`">
      {{ entityName }}
      <DownOutlined :class="`${prefixCls}-down-icon`" />
    </div>
  </a-popover>
</template>

<script lang="ts">
import { defineComponent, ref, computed, PropType } from 'vue';
import { Popover as APopover } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';
import RecommendOptions from '../RecommendOptions/index.vue';
import { PREFIX_CLS } from '../../common/constants';
import type { ChatContextType } from '../../common/type';

/**
 * 实体切换组件
 * 用于在不同实体间进行切换
 */
export default defineComponent({
  name: 'SwitchEntity',
  components: {
    APopover,
    DownOutlined,
    RecommendOptions
  },
  props: {
    /** 当前实体名称 */
    entityName: {
      type: String,
      required: true
    },
    /** 聊天上下文 */
    chatContext: {
      type: Object as PropType<ChatContextType>,
      required: true
    },
    /** 切换实体回调函数 */
    onSwitchEntity: {
      type: Function as PropType<(entityId: string) => void>,
      required: true
    }
  },
  setup(props) {
    // 前缀类名
    const prefixCls = `${PREFIX_CLS}-item`;
    // 推荐选项弹窗是否打开
    const recommendOptionsOpen = ref(false);
    
    /**
     * 计算当前实体ID
     * 从维度过滤器中查找实体ID
     */
    const entityId = computed(() => {
      return props.chatContext.dimensionFilters?.find(
        filter => filter?.bizName === 'zyqk_song_id' || filter?.bizName === 'singer_id'
      )?.value;
    });

    /**
     * 处理弹窗打开状态变化
     * @param open 弹窗是否打开
     */
    const handleOpenChange = (open: boolean) => {
      recommendOptionsOpen.value = open;
    };

    /**
     * 切换实体处理函数
     * @param option 选中的实体ID
     */
    const switchEntity = (option: string) => {
      recommendOptionsOpen.value = false;
      props.onSwitchEntity(option);
    };
    
    return {
      prefixCls,
      recommendOptionsOpen,
      entityId,
      switchEntity,
      handleOpenChange
    };
  }
});
</script> 