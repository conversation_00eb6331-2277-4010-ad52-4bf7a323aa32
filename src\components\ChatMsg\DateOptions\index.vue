<template>
  <div :class="prefixCls">
    <template v-for="(dateOption, index) in dateOptions" :key="dateOption.value">
      <div
        :class="[
          `${prefixCls}-item`,
          {
            [`${prefixCls}-date-active`]: dateOption.value === currentDateOption,
            [`${prefixCls}-date-mobile`]: isMobile,
          },
        ]"
        @click="handleSelectDateOption(dateOption.value)"
      >
        {{ dateOption.label }}
        <div v-if="dateOption.value === currentDateOption" :class="`${prefixCls}-active-identifier`" />
      </div>
      <div v-if="index !== dateOptions.length - 1" :class="`${prefixCls}-item-divider`" />
    </template>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { CLS_PREFIX, DATE_TYPES } from '../../../common/constants';
import { isMobile } from '../../../utils/utils';
import type { ChatContextType } from '../../../common/type';

interface DateOption {
  label: string;
  value: number;
}

const props = withDefaults(defineProps<{
  chatContext?: ChatContextType;
  currentDateOption?: number;
}>(), {
  chatContext: () => ({} as ChatContextType),
  currentDateOption: 7 // 默认选择近7天
});

const emit = defineEmits<{
  (e: 'selectDateOption', value: number): void;
}>();

const prefixCls = `${CLS_PREFIX}-date-options`;

const dateOptions = computed<DateOption[]>(() => {
  if (!props.chatContext?.dateInfo?.period) {
    return DATE_TYPES.DAY;
  }
  return DATE_TYPES[props.chatContext.dateInfo.period as keyof typeof DATE_TYPES] || DATE_TYPES.DAY;
});

const handleSelectDateOption = (value: number) => {
  emit('selectDateOption', value);
};
</script>

<style lang="less" scoped>
@import './style.less';
</style>
