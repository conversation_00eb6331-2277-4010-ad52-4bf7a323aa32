import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import RecommendOptions from './index.vue';
import { CLS_PREFIX } from '../../common/constants';

// 模拟服务
import { queryEntities } from '../../service';

// 模拟数据
const mockArtistData = [
  {
    id: '1001',
    name: '周杰伦',
    url: 'https://placehold.co/100x100?text=周杰伦',
    onlineSongCnt: '523',
    tme3platJsPlayCnt: '1523000'
  },
  {
    id: '1002',
    name: '林俊杰',
    url: 'https://placehold.co/100x100?text=林俊杰',
    onlineSongCnt: '312',
    tme3platJsPlayCnt: '982300'
  },
  {
    id: '1003',
    name: '华晨宇',
    url: 'https://placehold.co/100x100?text=华晨宇',
    onlineSongCnt: '156',
    tme3platJsPlayCnt: '503450'
  }
];

const mockSongData = [
  {
    id: '2001',
    name: '稻香',
    singerName: '周杰伦',
    ver: '完整版',
    url: 'https://placehold.co/100x100?text=稻香',
    publishTime: '2008-10-15',
    tme3platAvgLogYyPlayCnt: '56230'
  },
  {
    id: '2002',
    name: '告白气球',
    singerName: '周杰伦',
    ver: '完整版',
    url: 'https://placehold.co/100x100?text=告白气球',
    publishTime: '2016-06-24',
    tme3platAvgLogYyPlayCnt: '82450'
  },
  {
    id: '2003',
    name: '七里香',
    singerName: '周杰伦',
    ver: 'DJ版',
    url: 'https://placehold.co/100x100?text=七里香',
    publishTime: '2004-08-03',
    tme3platAvgLogYyPlayCnt: '43267'
  }
];

// 模拟queryEntities服务
// @ts-ignore 覆盖原始方法，仅在Storybook中使用
queryEntities.mockImplementation = (entityId: string | number, modelId: number) => {
  return Promise.resolve({
    data: modelId === 1 ? mockArtistData : mockSongData
  });
};

const meta = {
  title: 'Components/RecommendOptions',
  component: RecommendOptions,
  tags: ['autodocs'],
  argTypes: {
    entityId: {
      control: { type: 'text' },
      description: '实体ID',
    },
    modelId: {
      control: { type: 'number' },
      options: [1, 2],
      description: '模型ID：1表示艺人模型，2表示歌曲模型',
    },
    modelName: {
      control: { type: 'select' },
      options: ['艺人库', '歌曲库'],
      description: '模型名称',
    },
    onSelect: {
      action: 'selected',
      description: '选择项的回调函数',
    },
  },
  parameters: {
    docs: {
      description: {
        component: '推荐选项组件，用于展示艺人或歌曲的推荐列表，支持桌面端和移动端自适应。',
      },
    },
    mockData: {
      artists: mockArtistData,
      songs: mockSongData,
    },
  },
  decorators: [
    (story) => ({
      components: { story },
      template: `<div style="width: 600px; margin: 0 auto;"><story /></div>`,
    }),
  ],
} satisfies Meta<typeof RecommendOptions>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础故事：艺人列表
export const ArtistList: Story = {
  args: {
    entityId: '1001',
    modelId: 1,
    modelName: '艺人库',
    onSelect: fn(),
  },
};

// 歌曲列表故事
export const SongList: Story = {
  args: {
    entityId: '2001',
    modelId: 2,
    modelName: '歌曲库',
    onSelect: fn(),
  },
};

// 移动端显示故事
export const MobileView: Story = {
  args: {
    entityId: '1001',
    modelId: 1,
    modelName: '艺人库',
    onSelect: fn(),
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile2',
    },
  },
  decorators: [
    (story) => ({
      components: { story },
      template: `<div style="width: 320px; margin: 0 auto;"><story /></div>`,
    }),
  ],
};

// 加载状态故事
export const LoadingState: Story = {
  args: {
    entityId: '',
    modelId: 1,
    modelName: '艺人库',
    onSelect: fn(),
  },
  play: async ({ canvasElement }) => {
    // 在这里可以编写自动化测试或展示交互
  },
}; 