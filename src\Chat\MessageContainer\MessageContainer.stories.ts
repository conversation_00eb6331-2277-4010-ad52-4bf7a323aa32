import { fn } from "@storybook/test";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";

import MessageContainer from "./index.vue";
import { MessageTypeEnum, type MessageItem, type AgentType } from "../type";
import { MsgDataType } from "../../common/type";

// 模拟消息数据
const mockMessages: MessageItem[] = [
  {
    id: 1,
    type: MessageTypeEnum.TEXT,
    msg: "您好，我是智能助手，有什么可以帮助您的吗？",
  },
  {
    id: 2,
    type: MessageTypeEnum.AGENT_LIST,
  },
  {
    id: 3,
    type: MessageTypeEnum.QUESTION,
    msg: "如何分析销售数据趋势？",
    questionId: 1001,
    parseInfos: [
      {
        id: 101,
        modelId: 201,
        modelName: "销售分析",
        aggType: "COUNT",
        queryType: "METRIC",
        queryMode: "NORMAL",
        dateInfo: {
          startDate: "2024-01-01",
          endDate: "2024-03-31",
          dateMode: "day",
          period: "quarter",
          unit: 1,
          text: "最近一个季度",
          dateList: []
        },
        dimensions: [],
        metrics: [
          {
            id: 301,
            bizName: "sales_amount",
            name: "销售额",
            model: 201,
            status: 1,
            type: "METRIC",
            value: "sales_amount"
          }
        ],
        dimensionFilters: [],
        entity: { id: 1, alias: ["销售"] },
        entityInfo: {
          dimensions: [],
          metrics: [],
          dataSetInfo: {
            bizName: "sales",
            name: "销售",
            itemId: 201,
            primaryEntityBizName: "sales",
            value: "sales",
            words: ["销售"]
          },
          entityId: 1
        },
        elementMatches: [],
        nativeQuery: false,
        properties: {
          type: "metrics"
        },
        sqlInfo: {
          parsedS2SQL: "",
          correctedS2SQL: "",
          querySQL: ""
        },
        textInfo: "查询销售趋势"
      }
    ],
    parseTimeCost: {
      total: 1200,
      modelInvoke: 800,
      parse: 400
    },
    msgData: {
      id: 1001,
      question: "如何分析销售数据趋势？",
      queryId: 1001,
      queryMode: "NORMAL",
      queryState: "COMPLETED",
      queryText: "分析最近一季度的销售数据趋势",
      queryResults: [
        { date: "2024-01", value: 10000 },
        { date: "2024-02", value: 12000 },
        { date: "2024-03", value: 15000 },
      ],
      queryColumns: [
        {
          name: "日期",
          nameEn: "date",
          bizName: "date",
          showType: "DATE",
          type: "DIMENSION",
          authorized: true,
          dataFormatType: "TEXT",
          dataFormat: {
            decimalPlaces: 0,
            needMultiply100: false
          }
        },
        {
          name: "销售额",
          nameEn: "value",
          bizName: "value",
          showType: "NUMBER",
          type: "METRIC",
          authorized: true,
          dataFormatType: "NUMBER",
          dataFormat: {
            decimalPlaces: 0,
            needMultiply100: false
          }
        }
      ],
      aggregateInfo: {
        metricInfos: [
          {
            name: "总销售额",
            value: "37000",
            date: "2024 Q1",
            statistics: {}
          }
        ]
      },
      chatContext: {
        id: 101,
        modelId: 201,
        modelName: "销售分析",
        aggType: "COUNT",
        queryType: "METRIC",
        queryMode: "NORMAL",
        dateInfo: {
          startDate: "2024-01-01",
          endDate: "2024-03-31",
          dateMode: "day",
          period: "quarter",
          unit: 1,
          text: "最近一个季度",
          dateList: []
        },
        dimensions: [],
        metrics: [
          {
            id: 301,
            bizName: "sales_amount",
            name: "销售额",
            model: 201,
            status: 1,
            type: "METRIC",
            value: "sales_amount"
          }
        ],
        dimensionFilters: [],
        entity: { id: 1, alias: ["销售"] },
        entityInfo: {
          dimensions: [],
          metrics: [],
          dataSetInfo: {
            bizName: "sales",
            name: "销售",
            itemId: 201,
            primaryEntityBizName: "sales",
            value: "sales",
            words: ["销售"]
          },
          entityId: 1
        },
        elementMatches: [],
        nativeQuery: false,
        properties: {
          type: "metrics"
        },
        sqlInfo: {
          parsedS2SQL: "",
          correctedS2SQL: "",
          querySQL: ""
        },
        textInfo: "查询销售趋势"
      },
      entityInfo: {
        dimensions: [],
        metrics: [],
        dataSetInfo: {
          bizName: "sales",
          name: "销售",
          itemId: 201,
          primaryEntityBizName: "sales",
          value: "sales",
          words: ["销售"]
        },
        entityId: 1
      },
      parseInfos: [],
      textResult: "在最近一季度(2024年Q1)中，销售额呈现稳步增长趋势，从1月的1万元增长到3月的1.5万元，整体增长了50%。",
      textSummary: "销售额逐月增长，整体呈上升趋势。",
      similarQueries: [],
      recommendedDimensions: [],
      queryAuthorization: {},
      errorMsg: "",
      response: {
        description: "",
        webPage: { url: "", paramOptions: {}, params: {}, valueParams: {} },
        pluginId: 0,
        pluginType: "",
        name: ""
      },
      queryTimeCost: 2000
    },
    modelId: 201,
    agentId: 101,
    score: 5,
    filters: []
  },
  {
    id: 4,
    type: MessageTypeEnum.QUESTION,
    msg: "上个月的最畅销产品是什么？",
    questionId: 1002,
    msgValue: "上个月的最畅销产品是什么？",
    identityMsg: "我将为您分析上个月的产品销售数据",
    modelId: 201,
    agentId: 101
  }
];

// 模拟Agent数据
const mockAgent: AgentType = {
  id: 101,
  name: "数据分析专家",
  description: "我可以帮助您分析各类业务数据，提供数据洞察和趋势分析。",
  examples: [
    "分析过去三个月的销售趋势",
    "哪个地区的客户转化率最高？",
    "上个季度利润最高的产品是什么？"
  ],
  status: 1,
  createdBy: "admin",
  updatedBy: "admin",
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-03-01T00:00:00Z",
  enableSearch: 1,
  enableFeedback: 1,
  toolConfig: "",
  modelConfig: {
    provider: "openai",
    baseUrl: "https://api.openai.com",
    apiKey: "sk-xxxx",
    modelName: "gpt-4",
    temperature: 0.7,
    timeOut: 60000
  },
  multiTurnConfig: {
    enableMultiTurn: true
  },
  dataSetIds: [1, 2, 3]
};

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta = {
  title: "Chat/MessageContainer",
  component: MessageContainer,
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  argTypes: {
    id: {
      control: "text",
      description: "容器ID"
    },
    chatId: {
      control: "number",
      description: "对话ID"
    },
    messageList: {
      control: "object",
      description: "消息列表"
    },
    historyVisible: {
      control: "boolean",
      description: "是否显示历史记录"
    },
    currentAgent: {
      control: "object",
      description: "当前代理信息"
    },
    chatVisible: {
      control: "boolean",
      description: "是否显示聊天窗口"
    },
    isDeveloper: {
      control: "boolean",
      description: "是否为开发者模式"
    },
    integrateSystem: {
      control: "text",
      description: "集成系统名称"
    },
    isSimpleMode: {
      control: "boolean",
      description: "是否为简单模式"
    },
    isDebugMode: {
      control: "boolean",
      description: "是否为调试模式"
    },
    onMsgDataLoaded: {
      action: "消息数据加载完成",
      description: "消息数据加载完成的回调"
    },
    onSendMsg: {
      action: "发送消息",
      description: "发送消息的回调"
    }
  },
  args: {
    id: "message-container",
    chatId: 1,
    messageList: mockMessages,
    historyVisible: false,
    currentAgent: mockAgent,
    chatVisible: true,
    isDeveloper: false,
    isSimpleMode: false,
    isDebugMode: false
  },
  parameters: {
    docs: {
      description: {
        component: "消息容器组件，用于显示聊天消息列表。支持多种消息类型的展示，包括文本消息、问答消息和代理列表。"
      }
    }
  }
} satisfies Meta<typeof MessageContainer>;

export default meta;
type Story = StoryObj<typeof meta>;

// 默认状态展示
export const 默认状态: Story = {
  args: {
    id: "message-container",
    chatId: 1,
    messageList: mockMessages,
    historyVisible: false,
    currentAgent: mockAgent,
    chatVisible: true,
    isDeveloper: false,
    isSimpleMode: false,
    isDebugMode: false,
    onMsgDataLoaded: (data: MsgDataType, questionId: string | number, question: string, valid: boolean, isRefresh?: boolean) => {
      console.log("消息数据加载完成:", { data, questionId, question, valid, isRefresh });
    },
    onSendMsg: (value: string) => {
      console.log("发送消息:", value);
    }
  }
};

// 历史记录可见状态
export const 历史记录可见: Story = {
  args: {
    ...默认状态.args,
    historyVisible: true
  }
};

// 开发者模式
export const 开发者模式: Story = {
  args: {
    ...默认状态.args,
    isDeveloper: true,
    isDebugMode: true
  }
};

// 简单模式
export const 简单模式: Story = {
  args: {
    ...默认状态.args,
    isSimpleMode: true
  }
};

// 仅文本消息
export const 仅文本消息: Story = {
  args: {
    ...默认状态.args,
    messageList: mockMessages.filter(msg => msg.type === MessageTypeEnum.TEXT)
  }
};

// 仅代理列表
export const 代理列表: Story = {
  args: {
    ...默认状态.args,
    messageList: mockMessages.filter(msg => msg.type === MessageTypeEnum.AGENT_LIST)
  }
};

// 仅问答消息
export const 问答消息: Story = {
  args: {
    ...默认状态.args,
    messageList: mockMessages.filter(msg => msg.type === MessageTypeEnum.QUESTION)
  }
};

// 移动端视图
export const 移动端视图: Story = {
  args: {
    ...默认状态.args
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}; 