import { Meta, StoryFn } from '@storybook/vue3';
import FilterSection from './index.vue';
import { PREFIX_CLS } from '../../../common/constants';

export default {
  title: 'Components/ChatMsg/FilterSection',
  component: FilterSection,
  tags: ['autodocs'],
  argTypes: {
    chatContext: {
      control: { type: 'object' },
      description: '聊天上下文，包含筛选条件信息',
    },
    entityInfo: {
      control: { type: 'object' },
      description: '实体信息，包含维度信息',
    },
  },
  parameters: {
    docs: {
      description: {
        component: '筛选条件展示组件，用于显示当前查询的筛选条件',
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#333333' },
      ],
    },
  },
} as Meta<typeof FilterSection>;

const Template: StoryFn<typeof FilterSection> = (args) => ({
  components: { FilterSection },
  setup() {
    return { args, PREFIX_CLS };
  },
  template: `
    <div style="padding: 20px; max-width: 800px; border: 1px solid #eee; border-radius: 8px;">
      <FilterSection v-bind="args" />
    </div>
  `,
});

// 单个筛选条件
export const 单个筛选条件 = Template.bind({});
单个筛选条件.args = {
  chatContext: {
    dimensionFilters: [
      {
        elementID: 1,
        name: '部门',
        bizName: 'department',
        value: '销售部',
      },
    ],
  },
};

// 多个筛选条件
export const 多个筛选条件 = Template.bind({});
多个筛选条件.args = {
  chatContext: {
    dimensionFilters: [
      {
        elementID: 1,
        name: '部门',
        bizName: 'department',
        value: '销售部',
      },
      {
        elementID: 2,
        name: '城市',
        bizName: 'city',
        value: '北京',
      },
      {
        elementID: 3,
        name: '年份',
        bizName: 'year',
        value: '2023',
      },
    ],
  },
};

// 使用entityInfo展示
export const 使用维度信息展示 = Template.bind({});
使用维度信息展示.args = {
  chatContext: {
    dimensionFilters: [
      {
        elementID: 1,
        name: '部门',
        bizName: 'department',
        value: '销售部',
      },
      {
        elementID: 2,
        name: '城市',
        bizName: 'city',
        value: '北京',
      },
    ],
  },
  entityInfo: {
    dimensions: [
      {
        bizName: 'department',
        name: '部门名称',
        value: '销售部',
        itemId: 1,
        id: 1,
        status: 1,
        model: 1,
        type: 'STRING',
      },
      {
        bizName: 'city',
        name: '所在城市',
        value: '北京',
        itemId: 2,
        id: 2,
        status: 1,
        model: 1,
        type: 'STRING',
      },
    ],
  },
};

// 数组类型的筛选值
export const 数组筛选值 = Template.bind({});
数组筛选值.args = {
  chatContext: {
    dimensionFilters: [
      {
        elementID: 1,
        name: '部门',
        bizName: 'department',
        value: ['销售部', '市场部', '研发部'],
      },
      {
        elementID: 2,
        name: '季度',
        bizName: 'quarter',
        value: ['Q1', 'Q2'],
      },
    ],
  },
};

// 无筛选条件
export const 无筛选条件 = Template.bind({});
无筛选条件.args = {
  chatContext: {
    dimensionFilters: [],
  },
};

// 长文本筛选条件
export const 长文本筛选条件 = Template.bind({});
长文本筛选条件.args = {
  chatContext: {
    dimensionFilters: [
      {
        elementID: 1,
        name: '产品描述',
        bizName: 'product_description',
        value: '这是一段非常长的产品描述文本，用于测试筛选条件组件在显示长文本时的效果，包括文本截断和提示功能',
      },
      {
        elementID: 2,
        name: '标签',
        bizName: 'tags',
        value: ['高端', '奢华', '品质', '专业', '高效', '创新', '时尚', '经典', '实用'],
      },
    ],
  },
};

// 深色背景
export const 深色背景 = Template.bind({});
深色背景.args = {
  ...多个筛选条件.args,
};
深色背景.parameters = {
  backgrounds: { default: 'dark' },
};
深色背景.decorators = [
  () => ({ 
    template: '<div style="padding: 20px; color: white; background-color: #333; border-radius: 8px;"><story /></div>' 
  }),
]; 