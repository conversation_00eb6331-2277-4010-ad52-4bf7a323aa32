<template>
  <div class="recommendQuestions">
    <LeftAvatar v-if="!isMobile" />
    <template v-if="loading">
      <!-- 加载中不显示内容 -->
    </template>
    <Message
      v-else-if="questions.length > 0"
      position="left"
      :bubbleClassName="'recommendQuestionsMsg'"
    >
      <div class="title">推荐问题：</div>
      <div class="content">
        <div
          v-for="(question, index) in questions"
          :key="`${question}_${index}`"
          class="question"
          @click="onSelectQuestion(question)"
        >
          {{ question }}
        </div>
      </div>
    </Message>
    <Message v-else position="left">您好，请问有什么我可以帮您吗？</Message>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, PropType } from "vue";
import LeftAvatar from "../CopilotAvatar/index.vue";
import Message from "../Message/index.vue";
import { queryRecommendQuestions } from "../../service";
import { isMobile } from "../../../utils/utils";

export default defineComponent({
  name: "RecommendQuestions",
  components: {
    LeftAvatar,
    Message,
  },
  props: {
    onSelectQuestion: {
      type: Function as PropType<(value: string) => void>,
      required: true,
    },
  },
  setup(props) {
    const questions = ref<string[]>([]);
    const loading = ref<boolean>(false);

    const initData = async () => {
      loading.value = true;
      const res = await queryRecommendQuestions();
      // const res = {
      //   data: [
      //     {
      //       recommendedQuestions: [
      //         { question: "怎么查看销售数据?" },
      //         { question: "如何分析用户行为?" },
      //         { question: "最近一周的转化率是多少?" },
      //         { question: "如何提升页面访问量?" },
      //         { question: "用户留存率怎么计算?" },
      //       ],
      //     },
      //     {
      //       recommendedQuestions: [
      //         { question: "如何设置数据看板?" },
      //         { question: "如何导出报表?" },
      //         { question: "什么是UV和PV?" },
      //         { question: "如何分析用户增长趋势?" },
      //         { question: "如何查询特定时间段的数据?" },
      //       ],
      //     },
      //   ],
      // };
      loading.value = false;
      questions.value =
        res.data?.reduce((result: any[], item: any) => {
          result = [
            ...result,
            ...item.recommendedQuestions
              .slice(0, 20)
              .map((item: any) => item.question),
          ];
          return result;
        }, []) || [];
    };

    onMounted(() => {
      initData();
    });

    return {
      questions,
      loading,
      isMobile,
      onSelectQuestion: props.onSelectQuestion,
    };
  },
});
</script>

<style lang="less" scoped>
.recommendQuestions {
  display: flex;

  :deep(.recommendQuestionsMsg) {
    padding: 12px 20px 20px !important;

    .title {
      margin-bottom: 12px;
      font-weight: 500;
      font-size: 14px;
    }

    .content {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      column-gap: 16px;
      row-gap: 20px;

      .question {
        height: 22px;
        padding: 0 6px;
        color: var(--text-color);
        font-size: 12px;
        line-height: 22px;
        background-color: #f4f4f4;
        border-radius: 11px;
        cursor: pointer;

        &:hover {
          color: var(--chat-blue);
        }
      }
    }
  }
}
</style>
