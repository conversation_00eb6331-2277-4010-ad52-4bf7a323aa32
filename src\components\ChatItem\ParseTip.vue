<template>
  <div v-if="parseLoading" :class="`${prefixCls}-parse-tip`">
    <div :class="`${prefixCls}-title-bar`">
      <check-circle-filled :class="`${prefixCls}-step-icon`" />
      <div :class="`${prefixCls}-step-title`">
        意图解析中
        <Loading v-if="true" />
      </div>
    </div>
  </div>

  <div v-else-if="parseTip" :class="`${prefixCls}-parse-tip`">
    <div :class="`${prefixCls}-title-bar`">
      <close-circle-filled :class="`${prefixCls}-step-error-icon`" />
      <div :class="`${prefixCls}-step-title`">
        意图解析失败
        <span v-if="!!parseTimeCost && isDeveloper" :class="`${prefixCls}-title-tip`">(耗时: {{ parseTimeCost }}ms)</span>
      </div>
    </div>
    <div
      :class="[
        `${prefixCls}-content-container`,
        `${prefixCls}-content-container-failed`
      ]"
    >
      {{ parseTip }}
    </div>
  </div>

  <div v-else-if="!isSimpleMode && parseInfoOptions.length > 0" :class="`${prefixCls}-parse-tip`">
    <div :class="`${prefixCls}-title-bar`">
      <check-circle-filled :class="`${prefixCls}-step-icon`" />
      <div :class="`${prefixCls}-step-title`">
        <div :class="`${prefixCls}-title-bar`">
          <div>
            意图解析
            <span v-if="!!parseTimeCost && isDeveloper" :class="`${prefixCls}-title-tip`">(耗时: {{ parseTimeCost }}ms)</span>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="currentParseInfo && currentParseInfo.queryMode !== 'PLAIN_TEXT'" :class="`${prefixCls}-content-container`">
      <div :class="`${prefixCls}-tip`">
        <parse-tip-utils 
          :parse-info="currentParseInfo" 
          :dimension-filters="dimensionFilters" 
          :entity-info="entityInfo" 
        />
        <template v-if="!(!!currentParseInfo.properties?.type && currentParseInfo.queryMode !== 'LLM_S2SQL')">
          <div v-if="!!dateInfo || !!dimensionFilters?.length" :class="`${prefixCls}-tip-item`">
            <div :class="`${prefixCls}-tip-item-name`">筛选条件：</div>
            <div :class="`${prefixCls}-tip-item-content`">
              <div :class="`${prefixCls}-tip-item-filter-content`">
                <div v-if="!!dateInfo" :class="tipItemOptionClass">
                  <span :class="`${prefixCls}-tip-item-filter-name`">数据时间：</span>
                  <span v-if="currentParseInfo.nativeQuery" :class="itemValueClass">
                    {{ dateInfo.startDate === dateInfo.endDate ? dateInfo.startDate : `${dateInfo.startDate} ~ ${dateInfo.endDate}` }}
                  </span>
                  <a-range-picker
                    v-else
                    :value="[dayjs(dateInfo.startDate), dayjs(dateInfo.endDate)]"
                    @change="onDateInfoChange"
                    format="YYYY-MM-DD"
                  >
                    <template #renderExtraFooter>
                      <a-row :gutter="[28, 28]">
                        <a-col v-for="(_, key) in ranges" :key="key">
                          <a-button size="small" @click="() => handlePresetClick(ranges[key])">
                            {{ key }}
                          </a-button>
                        </a-col>
                      </a-row>
                    </template>
                  </a-range-picker>
                </div>
                <filter-item
                  v-for="(filter, index) in dimensionFilters"
                  :key="`${filter.name}_${index}`"
                  :model-id="currentParseInfo.modelId"
                  :filters="dimensionFilters"
                  :filter="filter"
                  :index="index"
                  :chat-context="currentParseInfo"
                  :entity-alias="entityAlias"
                  :agent-id="agentId"
                  :integrate-system="integrateSystem"
                  :on-filters-change="onFiltersChange"
                  :on-switch-entity="onSwitchEntity"
                />
              </div>
            </div>
          </div>
          <a-button :class="`${prefixCls}-reload`" size="small" @click="onRefresh">
            <reload-outlined />
            重新查询
          </a-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, PropType } from 'vue';
import { Button, DatePicker, Row, Col } from 'ant-design-vue';
import { CheckCircleFilled, CloseCircleFilled, ReloadOutlined } from '@ant-design/icons-vue';
import Loading from './Loading.vue';
import FilterItem from './FilterItem.vue';
import MarkDown from '../ChatMsg/MarkDown/index.vue';
import ParseTipUtils from './ParseTipUtils.vue';
import classNames from 'classnames';
import { isMobile } from '../../utils/utils';
import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import { ChatContextType, DateInfoType, EntityInfoType, FilterItemType } from '../../common/type';
import { PREFIX_CLS } from '../../common/constants';

import 'dayjs/locale/zh-cn';

dayjs.extend(quarterOfYear);
dayjs.locale('zh-cn');

const prefixCls = `${PREFIX_CLS}-item`;

type RangeValue = [dayjs.Dayjs, dayjs.Dayjs];
type RangeKeys = '近7日' | '近14日' | '近30日' | '本周' | '本月' | '上月' | '本季度' | '本年';

export default defineComponent({
  name: 'ParseTip',
  components: {
    Loading,
    FilterItem,
    MarkDown,
    ParseTipUtils,
    CheckCircleFilled,
    CloseCircleFilled,
    ReloadOutlined,
    AButton: Button,
    ARangePicker: DatePicker.RangePicker,
    ARow: Row,
    ACol: Col
  },
  props: {
    parseLoading: {
      type: Boolean,
      default: false
    },
    parseInfoOptions: {
      type: Array as PropType<ChatContextType[]>,
      required: true
    },
    parseTip: {
      type: String,
      default: ''
    },
    currentParseInfo: {
      type: Object as PropType<ChatContextType>,
      default: undefined
    },
    agentId: {
      type: Number,
      default: undefined
    },
    dimensionFilters: {
      type: Array as PropType<FilterItemType[]>,
      required: true
    },
    dateInfo: {
      type: Object as PropType<DateInfoType>,
      required: true
    },
    entityInfo: {
      type: Object as PropType<EntityInfoType>,
      required: true
    },
    integrateSystem: {
      type: String,
      default: undefined
    },
    parseTimeCost: {
      type: Number,
      default: undefined
    },
    isDeveloper: {
      type: Boolean,
      default: false
    },
    isSimpleMode: {
      type: Boolean,
      default: false
    },
    onSelectParseInfo: {
      type: Function as PropType<(parseInfo: ChatContextType) => void>,
      required: true
    },
    onSwitchEntity: {
      type: Function as PropType<(entityId: string) => void>,
      required: true
    },
    onFiltersChange: {
      type: Function as PropType<(filters: FilterItemType[]) => void>,
      required: true
    },
    onDateInfoChange: {
      type: Function as PropType<(dateRange: any) => void>,
      required: true
    },
    onRefresh: {
      type: Function as PropType<() => void>,
      required: true
    },
    handlePresetClick: {
      type: Function,
      required: true
    }
  },
  setup(props) {
    const ranges: Record<RangeKeys, RangeValue> = {
      '近7日': [dayjs().subtract(7, 'day'), dayjs()],
      '近14日': [dayjs().subtract(14, 'day'), dayjs()],
      '近30日': [dayjs().subtract(30, 'day'), dayjs()],
      '本周': [dayjs().startOf('week'), dayjs().endOf('week')],
      '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
      '上月': [
        dayjs().subtract(1, 'month').startOf('month'),
        dayjs().subtract(1, 'month').endOf('month'),
      ],
      '本季度': [dayjs().startOf('quarter'), dayjs().endOf('quarter')],
      '本年': [dayjs().startOf('year'), dayjs().endOf('year')],
    };

    const entityAlias = computed(() => {
      return props.currentParseInfo?.entity?.alias?.[0]?.split('.')?.[0];
    });

    const itemValueClass = `${prefixCls}-tip-item-value`;

    const tipItemOptionClass = computed(() => {
      return classNames(`${prefixCls}-tip-item-option`, {
        [`${prefixCls}-mobile-tip-item-option`]: isMobile,
      });
    });

    return {
      prefixCls,
      ranges,
      entityAlias,
      itemValueClass,
      tipItemOptionClass,
      dayjs
    };
  }
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
