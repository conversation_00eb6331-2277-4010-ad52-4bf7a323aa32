<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}-charts`">
      <div :class="`${prefixCls}-top-bar`">
        <div :class="`${prefixCls}-metric-fields ${prefixCls}-metric-field-single`">
          {{ question }}
        </div>
      </div>
      <a-spin :spinning="loading">
        <div :class="`${prefixCls}-content`">
          <MetricInfo 
            v-if="!isMobile && aggregateInfo?.metricInfos?.length > 0 && drillDownDimension === undefined && currentMetricField"
            :aggregate-info="aggregateInfo" 
            :current-metric-field="currentMetricField" 
          />
          <div :class="`${prefixCls}-select-options`">
            <DateOptions
              :chat-context="chatContext"
              :current-date-option="currentDateOption"
              @select-date-option="onSelectDateOption"
            />
            <div>
              <a-select
                v-model:value="chartType"
                :bordered="false"
                :options="metricChartSelectOptions"
                @change="onChartTypeChange"
              />
            </div>
          </div>
          <Table 
            v-if="queryResults?.length === 1 || chartIndex % 2 === 1"
            :data="{ ...data, queryResults }" 
            @apply-auth="onApplyAuth" 
          />
          <MultiMetricsTrendChart
            v-else-if="metricFields.length > 1"
            :date-column-name="dateColumnName"
            :metric-fields="metricFields"
            :question="question"
            :result-list="queryResults"
            :trigger-resize="triggerResize"
            :chart-type="chartType"
          />
          <MetricTrendChart
            v-else-if="currentMetricField"
            :model="entityInfo?.dataSetInfo.name"
            :date-column-name="dateColumnName"
            :category-column-name="categoryColumnName"
            :metric-field="currentMetricField"
            :result-list="queryResults"
            :trigger-resize="triggerResize"
            :chart-type="chartType"
            @apply-auth="onApplyAuth"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { CLS_PREFIX } from '../../../common/constants';
import { isMobile } from '../../../utils/utils';
import type { DrillDownDimensionType, FieldType, MsgDataType } from '../../../common/type';
import MetricTrendChart from './MetricTrendChart.vue';
import Table from '../Table/index.vue';
import MetricInfo from './MetricInfo.vue';
import DateOptions from '../DateOptions/index.vue';
import MultiMetricsTrendChart from './MultiMetricsTrendChart.vue';
import { Spin as ASpin, Select as ASelect } from 'ant-design-vue'
import type { SelectValue } from 'ant-design-vue/es/select';

const metricChartSelectOptions = [
  {
    value: 'line',
    label: '折线图',
  },
  {
    value: 'bar',
    label: '柱状图',
  },
];

const props = defineProps<{
  data: MsgDataType;
  question: string;
  chartIndex: number;
  triggerResize?: boolean;
  loading: boolean;
  activeMetricField?: FieldType;
  drillDownDimension?: DrillDownDimensionType;
  currentDateOption?: number;
  onApplyAuth?: (model: string) => void;
}>();

const emit = defineEmits<{
  (e: 'selectDateOption', value: number): void;
  (e: 'applyAuth', model: string): void;
}>();

const prefixCls = `${CLS_PREFIX}-metric-trend`;
const chartType = ref('line');

// 从 data 中提取数据
const { queryColumns, queryResults, aggregateInfo, entityInfo, chatContext } = props.data;

// 查找日期字段
const dateField = computed(() => 
  queryColumns?.find((column: any) => column.showType === 'DATE' || column.type === 'DATE')
);
const dateColumnName = computed(() => dateField.value?.bizName || '');

// 查找分类字段
const categoryColumnName = computed(() => 
  queryColumns?.find((column: any) => column.showType === 'CATEGORY')?.bizName || ''
);

// 查找度量字段
const metricFields = computed(() => 
  queryColumns?.filter((column: any) => column.showType === 'NUMBER')
);

// 当前度量字段
const currentMetricField = computed(() => 
  queryColumns?.find((column: any) => column.showType === 'NUMBER')
);

// 处理事件
const onSelectDateOption = (value: number) => {
  emit('selectDateOption', value);
};

const onChartTypeChange = (value: SelectValue) => {
  if (typeof value === 'string') {
    chartType.value = value;
  }
};

const onApplyAuth = (model: string) => {
  if (props.onApplyAuth) {
    props.onApplyAuth(model);
  } else {
    emit('applyAuth', model);
  }
};
</script>

<style lang="less" scoped>
@import './style.less';
</style>
