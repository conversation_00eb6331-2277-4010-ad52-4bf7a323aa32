import { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import ChatItem from './index.vue';
import { 
  ChatContextType, 
  DateInfoType, 
  EntityInfoType, 
  FieldType,
  FilterItemType,
  ModelType,
  MsgDataType,
  ParseStateEnum,
  ParseTimeCostType
} from '../../common/type';
import { ChatContextTypeQueryTypeEnum } from '../../common/constants';
import dayjs from 'dayjs';

// 模拟日期信息
const mockDateInfo: DateInfoType = {
  dateList: [],
  dateMode: 'ABSOLUTE',
  period: 'day',
  startDate: '2023-01-01',
  endDate: '2023-12-31',
  text: '2023年全年',
  unit: 1
};

// 模拟实体信息
const mockEntityInfo: EntityInfoType = {
  dataSetInfo: {
    bizName: 'product',
    itemId: 1,
    name: '商品',
    primaryEntityBizName: 'product',
    value: 'product',
    words: ['商品']
  },
  dimensions: [],
  metrics: [],
  entityId: 1
};

// 模拟维度筛选条件
const mockDimensionFilters: FilterItemType[] = [
  {
    elementID: 101,
    name: '城市',
    bizName: 'city',
    operator: '=',
    value: '北京',
    type: 'STRING'
  },
  {
    elementID: 102,
    name: '销售额',
    bizName: 'sales_amount',
    operator: '>=',
    value: 1000,
    type: 'NUMBER'
  }
];

// 模拟维度字段
const mockDimensions: FieldType[] = [
  { 
    id: 101, 
    name: '城市', 
    bizName: 'city', 
    type: 'DIMENSION',
    itemId: 1001,
    status: 1,
    model: 100,
    value: 'city'
  },
  { 
    id: 102, 
    name: '商品类别', 
    bizName: 'category', 
    type: 'DIMENSION',
    itemId: 1002,
    status: 1,
    model: 100,
    value: 'category'
  }
];

// 模拟指标字段
const mockMetrics: FieldType[] = [
  { 
    id: 201, 
    name: '销售额', 
    bizName: 'sales_amount', 
    type: 'METRIC',
    itemId: 2001,
    status: 1,
    model: 100,
    value: 'sales_amount'
  },
  { 
    id: 202, 
    name: '订单数', 
    bizName: 'order_count', 
    type: 'METRIC',
    itemId: 2002,
    status: 1,
    model: 100,
    value: 'order_count'
  }
];

// 模拟解析结果
const mockParseInfo: ChatContextType = {
  id: 1001,
  queryId: 1001,
  aggType: 'SUM',
  modelId: 100,
  modelName: '销售数据',
  dataSet: {
    alias: '销售',
    bizName: 'sales_data',
    id: 1,
    model: 100,
    name: '销售数据',
    modelNames: ['销售数据'],
    type: 'BASIC',
    useCnt: 1
  } as ModelType,
  dateInfo: mockDateInfo,
  dimensions: mockDimensions,
  metrics: mockMetrics,
  entity: { alias: ['商品'], id: 1 },
  entityInfo: mockEntityInfo,
  elementMatches: [],
  nativeQuery: false,
  queryMode: 'SQL',
  queryType: ChatContextTypeQueryTypeEnum.DETAIL,
  dimensionFilters: mockDimensionFilters,
  properties: {
    CONTEXT: {
      llmReq: '请问2023年北京地区的销售额是多少?',
      llmResp: '根据分析，2023年北京地区的销售额为35,678元。'
    }
  },
  sqlInfo: {
    parsedS2SQL: 'SELECT SUM(sales_amount) FROM sales_data WHERE city = "北京" AND date BETWEEN "2023-01-01" AND "2023-12-31"',
    correctedS2SQL: 'SELECT SUM(sales_amount) FROM sales_data WHERE city = "北京" AND date BETWEEN "2023-01-01" AND "2023-12-31"',
    querySQL: 'SELECT SUM(sales_amount) as sales_amount FROM sales_data WHERE city = "北京" AND date BETWEEN "2023-01-01" AND "2023-12-31"'
  },
  textInfo: ''
};

// 模拟查询结果
const mockQueryResults = [
  {
    city: '北京',
    sales_amount: 35678
  }
];

// 模拟消息数据
const mockMsgData: MsgDataType = {
  id: 1001,
  question: '2023年北京地区的销售额是多少?',
  aggregateInfo: { metricInfos: [] },
  chatContext: mockParseInfo,
  entityInfo: mockEntityInfo,
  queryAuthorization: null,
  queryColumns: [
    {
      bizName: 'city',
      name: '城市',
      nameEn: 'city',
      showType: 'CATEGORY',
      type: 'STRING',
      authorized: true,
      dataFormatType: 'STRING',
      dataFormat: {
        decimalPlaces: 0,
        needMultiply100: false
      }
    },
    {
      bizName: 'sales_amount',
      name: '销售额',
      nameEn: 'sales_amount',
      showType: 'NUMBER',
      type: 'NUMBER',
      authorized: true,
      dataFormatType: 'NUMBER',
      dataFormat: {
        decimalPlaces: 0,
        needMultiply100: false
      }
    }
  ],
  queryResults: mockQueryResults,
  queryId: 1001,
  queryMode: 'SQL',
  queryState: 'SUCCESS',
  queryText: '2023年北京地区的销售额是多少?',
  response: {
    description: '查询成功',
    webPage: { url: '', paramOptions: null, params: null, valueParams: null },
    pluginId: 0,
    pluginType: '',
    name: ''
  },
  parseInfos: [mockParseInfo],
  queryTimeCost: 350,
  similarQueries: [
    {
      queryId: 2001,
      parseId: 2001,
      queryText: '2023年上海地区的销售额是多少?'
    },
    {
      queryId: 2002,
      parseId: 2002,
      queryText: '2023年北京地区的订单数是多少?'
    }
  ],
  recommendedDimensions: [],
  textResult: '',
  errorMsg: '',
  textSummary: ''
};

// 模拟解析时间成本
const mockParseTimeCost: ParseTimeCostType = {
  parseStartTime: dayjs().subtract(1, 'minute').valueOf(),
  parseTime: 233,
  sqlTime: 158
};

// 定义组件的Meta
const meta = {
  title: 'ChatItem/ChatItem',
  component: ChatItem,
  tags: ['autodocs'],
  argTypes: {
    msg: { 
      control: 'text',
      description: '聊天消息内容'
    },
    conversationId: { 
      control: 'number',
      description: '会话ID'
    },
    questionId: { 
      control: 'number',
      description: '问题ID'
    },
    modelId: { 
      control: 'number',
      description: '模型ID'
    },
    agentId: { 
      control: 'number',
      description: '代理ID'
    },
    score: { 
      control: 'number',
      description: '得分'
    },
    filter: { 
      control: 'object',
      description: '过滤条件'
    },
    parseInfos: { 
      control: 'object',
      description: '解析信息数组'
    },
    parseTimeCostValue: { 
      control: 'object',
      description: '解析时间成本'
    },
    msgData: { 
      control: 'object',
      description: '消息数据'
    },
    triggerResize: { 
      control: 'boolean',
      description: '触发调整大小'
    },
    isDeveloper: { 
      control: 'boolean',
      description: '是否为开发者'
    },
    integrateSystem: { 
      control: 'text',
      description: '集成系统'
    },
    isSimpleMode: { 
      control: 'boolean',
      description: '是否为简单模式'
    },
    isDebugMode: { 
      control: 'boolean',
      description: '是否为调试模式'
    },
    isLastMessage: { 
      control: 'boolean',
      description: '是否为最后一条消息'
    },
    onMsgDataLoaded: { 
      action: 'onMsgDataLoaded',
      description: '消息数据加载完成回调'
    },
    onUpdateMessageScroll: { 
      action: 'onUpdateMessageScroll',
      description: '更新消息滚动回调'
    },
    onSendMsg: { 
      action: 'onSendMsg',
      description: '发送消息回调'
    }
  },
  parameters: {
    docs: {
      description: {
        component: 'ChatItem组件用于展示聊天消息，包括查询解析、执行结果展示等功能。它是智能对话系统的核心组件之一。'
      }
    }
  },
  decorators: [
    () => ({
      template: '<div style="padding: 20px; background-color: #f5f5f5;"><story /></div>'
    })
  ]
} satisfies Meta<typeof ChatItem>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例 - 加载状态
export const Loading: Story = {
  args: {
    msg: '2023年北京地区的销售额是多少?',
    conversationId: 1,
    modelId: 100,
    agentId: 1,
    isDeveloper: false,
    isSimpleMode: false,
    isDebugMode: false,
    isLastMessage: true,
    onMsgDataLoaded: fn(),
    onUpdateMessageScroll: fn(),
    onSendMsg: fn()
  }
};

// 已完成解析的示例
export const ParsedResult: Story = {
  args: {
    msg: '2023年北京地区的销售额是多少?',
    conversationId: 1,
    modelId: 100,
    agentId: 1,
    parseInfos: [mockParseInfo],
    parseTimeCostValue: mockParseTimeCost,
    isDeveloper: false,
    isSimpleMode: false,
    isDebugMode: false,
    isLastMessage: true,
    onMsgDataLoaded: fn(),
    onUpdateMessageScroll: fn(),
    onSendMsg: fn()
  }
};

// 已加载数据的示例
export const WithData: Story = {
  args: {
    msg: '2023年北京地区的销售额是多少?',
    conversationId: 1,
    modelId: 100,
    agentId: 1,
    parseInfos: [mockParseInfo],
    parseTimeCostValue: mockParseTimeCost,
    msgData: mockMsgData,
    isDeveloper: false,
    isSimpleMode: false,
    isDebugMode: false,
    isLastMessage: true,
    onMsgDataLoaded: fn(),
    onUpdateMessageScroll: fn(),
    onSendMsg: fn()
  }
};

// 开发者模式示例
export const DeveloperMode: Story = {
  args: {
    msg: '2023年北京地区的销售额是多少?',
    conversationId: 1,
    modelId: 100,
    agentId: 1,
    parseInfos: [mockParseInfo],
    parseTimeCostValue: mockParseTimeCost,
    msgData: mockMsgData,
    isDeveloper: true,
    isSimpleMode: false,
    isDebugMode: true,
    isLastMessage: true,
    onMsgDataLoaded: fn(),
    onUpdateMessageScroll: fn(),
    onSendMsg: fn()
  }
};

// 简单模式示例
export const SimpleMode: Story = {
  args: {
    msg: '2023年北京地区的销售额是多少?',
    conversationId: 1,
    modelId: 100,
    agentId: 1,
    parseInfos: [mockParseInfo],
    parseTimeCostValue: mockParseTimeCost,
    msgData: mockMsgData,
    isDeveloper: false,
    isSimpleMode: true,
    isDebugMode: false,
    isLastMessage: true,
    onMsgDataLoaded: fn(),
    onUpdateMessageScroll: fn(),
    onSendMsg: fn()
  }
};

// 移动端示例
export const MobileView: Story = {
  args: {
    msg: '2023年北京地区的销售额是多少?',
    conversationId: 1,
    modelId: 100,
    agentId: 1,
    parseInfos: [mockParseInfo],
    parseTimeCostValue: mockParseTimeCost,
    msgData: mockMsgData,
    isDeveloper: false,
    isSimpleMode: false,
    isDebugMode: false,
    isLastMessage: true,
    onMsgDataLoaded: fn(),
    onUpdateMessageScroll: fn(),
    onSendMsg: fn()
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
};

// 错误状态示例
export const ErrorState: Story = {
  args: {
    msg: '这是一个无法解析的问题语句',
    conversationId: 1,
    modelId: 100,
    agentId: 1,
    isDeveloper: false,
    isSimpleMode: false,
    isDebugMode: false,
    isLastMessage: true,
    onMsgDataLoaded: fn(),
    onUpdateMessageScroll: fn(),
    onSendMsg: fn()
  }
}; 