declare module "@kangc/v-md-editor/lib/preview" {
  import { App } from "vue";

  interface VMdPreviewInstance {
    props: {
      text: string;
      [key: string]: any;
    };
  }

  const VMdPreview: {
    install: (app: App) => void;
    use: (theme: any, options?: any) => void;
  } & VMdPreviewInstance;

  export default VMdPreview;
}

declare module "@kangc/v-md-editor/lib/theme/vuepress.js" {
  const vuepressTheme: any;
  export default vuepressTheme;
}
