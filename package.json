{"name": "hr-chat-sdk", "version": "0.0.9", "module": "./dist/hr-chat-sdk.js", "type": "module", "exports": {".": {"import": "./dist/hr-chat-sdk.js"}, "./dist/": {"import": "./dist/"}}, "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite --host", "build": "rimraf build && tsc && vite build && dts-bundle-generator --config ./dts-bundle-generator.config.ts && copyfiles ./package.json build", "build1": "rimraf build && tsc && vite build && copyfiles ./package.json build", "build2": "rimraf build && vite build && copyfiles ./package.json build", "test": "vitest", "test:coverage": "vitest --coverage", "lint:scripts": "eslint . --ext .ts", "lint:styles": "stylelint ./**/*.{css,scss}", "format:scripts": "prettier . --write", "format:styles": "stylelint ./**/*.{css,scss} --fix", "format": "npm run format:scripts && npm run format:styles", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@chromatic-com/storybook": "3", "@eslint/js": "^9.23.0", "@storybook/addon-essentials": "8.6.7", "@storybook/addon-onboarding": "8.6.7", "@storybook/blocks": "8.6.7", "@storybook/experimental-addon-test": "8.6.7", "@storybook/test": "8.6.7", "@storybook/vue3": "8.6.7", "@storybook/vue3-vite": "8.6.7", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.16", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.5", "@vitejs/plugin-vue": "^5.2.2", "@vitest/browser": "3.0.8", "@vitest/coverage-v8": "^3.0.7", "@vue/runtime-core": "^3.5.13", "copyfiles": "^2.4.1", "dts-bundle-generator": "^9.5.1", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-storybook": "^0.11.6", "globals": "^16.0.0", "jiti": "^2.4.2", "less": "^4.2.2", "lint-staged": "^15.4.3", "patch-package": "^8.0.0", "playwright": "^1.51.1", "postcss": "^8.5.3", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "rimraf": "^6.0.1", "rollup-plugin-analyzer": "^4.0.0", "storybook": "8.6.7", "stylelint": "^16.14.1", "stylelint-config-recommended": "^15.0.0", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-order": "^6.0.4", "stylelint-prettier": "^5.0.3", "terser": "^5.39.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "typescript-eslint": "^8.25.0", "vite": "^6.2.0", "vitest": "^3.0.7", "vue": "^3.5.13", "vue-tsc": "3.0.0-alpha.2"}, "peerDependencies": {"vue": "^3.5.13", "vue-types": "^6.0.0"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@remixicon/vue": "^4.6.0", "ant-design-vue": "^4.2.6", "ant-design-x-vue": "^1.0.6", "axios": "^1.8.3", "classnames": "^2.5.1", "cropperjs": "1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "github-markdown-css": "^5.5.1", "highlight.js": "^11.11.1", "js-base64": "^3.7.7", "katex": "^0.16.22", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "md-editor-v3": "^5.4.5", "mermaid": "^11.6.0", "mime": "^4.0.7", "screenfull": "^6.0.2", "sql-formatter": "^15.5.1", "vue3-spinners": "^1.2.2"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}