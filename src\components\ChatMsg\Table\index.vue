<template>
  <div :class="prefixCls">
    <div v-if="question" :class="`${prefixCls}-top-bar`">
      <div :class="`${prefixCls}-indicator-name`">{{ question }}</div>
    </div>

    <a-table
      :pagination="tablePagination"
      :columns="tableColumns"
      :data-source="dataSource"
      :style="{ width: '100%', overflowX: 'auto', overflowY: 'hidden' }"
      :row-class-name="getRowClassName"
      :size="size"
      :loading="loading"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, h as vueRender } from "vue";
import {
  formatByDecimalPlaces,
  formatByThousandSeperator,
} from "../../../utils/utils";
import type { MsgDataType } from "../../../common/type";
import { CLS_PREFIX } from "../../../common/constants";
import ApplyAuth from "../ApplyAuth/index.vue";
import dayjs from "dayjs";
import { Table as ATable } from "ant-design-vue";

// 定义 SizeType 类型
type SizeType = "small" | "middle" | "large" | undefined;

const props = defineProps<{
  data: MsgDataType;
  size?: SizeType;
  question?: string;
  loading?: boolean;
  onApplyAuth?: (model: string) => void;
}>();

const emit = defineEmits<{
  (e: "applyAuth", model: string): void;
}>();

const prefixCls = `${CLS_PREFIX}-table`;
const { entityInfo, queryColumns, queryResults } = props.data;

// 转发 ApplyAuth 事件
const handleApplyAuth = (model: string) => {
  if (props.onApplyAuth) {
    props.onApplyAuth(model);
  } else {
    emit("applyAuth", model);
  }
};

// 构建表格列
const tableColumns = computed(() =>
  queryColumns.map(
    ({ name, bizName, showType, dataFormatType, dataFormat, authorized }) => {
      return {
        dataIndex: bizName,
        key: bizName,
        title: name || bizName,
        defaultSortOrder: "descend",
        sorter:
          showType === "NUMBER"
            ? (a: any, b: any) => a[bizName] - b[bizName]
            : undefined,
        customRender: ({ text: value }: { text: string | number }) => {
          if (!authorized) {
            return vueRender(ApplyAuth, {
              model: entityInfo?.dataSetInfo.name || "",
              onApplyAuth: handleApplyAuth,
            });
          }
          if (dataFormatType === "percent") {
            return vueRender(
              "div",
              {
                class: `${prefixCls}-formatted-value`,
              },
              `${
                value
                  ? formatByDecimalPlaces(
                      dataFormat?.needMultiply100 ? +value * 100 : value,
                      dataFormat?.decimalPlaces || 2
                    )
                  : 0
              }%`
            );
          }
          if (showType === "NUMBER") {
            return vueRender(
              "div",
              {
                class: `${prefixCls}-formatted-value`,
              },
              formatByThousandSeperator(value)
            );
          }
          if (bizName.includes("photo")) {
            return vueRender(
              "div",
              {
                class: `${prefixCls}-photo`,
              },
              [
                vueRender("img", {
                  width: 40,
                  height: 40,
                  src: value as string,
                  alt: "",
                }),
              ]
            );
          }
          return value;
        },
      };
    }
  )
);

// 表格行样式
const getRowClassName = (_: any, index: number) => {
  return index % 2 !== 0 ? `${prefixCls}-even-row` : "";
};

// 分页配置
const tablePagination = computed(() => {
  return queryResults.length <= 10
    ? false
    : { defaultPageSize: 10, position: ["bottomCenter"] };
});

// 数据源处理
const dateColumn = computed(() =>
  queryColumns.find(column => column.type === "DATE")
);
const dataSource = computed(() => {
  if (dateColumn.value) {
    return [...queryResults].sort((a, b) =>
      dayjs(a[dateColumn.value!.bizName]).diff(
        dayjs(b[dateColumn.value!.bizName])
      )
    );
  }
  return queryResults;
});
</script>

<style lang="less" scoped>
@import "./style.less";
</style>
