<template>
  <!-- :class="[!feedbackDisabled && 'px-3.5', 'h-full']" -->
  <div class="chat-container">
    <div class="chatHeader">
      <a-row style="width: 100%">
        <a-col flex="1 1 200px">
          <a-space v-if="!isDebugMode">
            <div class="chatHeaderTitle">{{ currentAgent.chartName }}</div>
            <div class="chatHeaderTip">
              {{ currentAgent.description }}
            </div>
          </a-space>

          <div class="debug-preview-header" v-if="isDebugMode">
            <div class="chatHeaderTitle">调式与预览</div>
            <a-tooltip>
              <template #title>重新开始</template>
              <ReloadOutlined
                class="reload-icon"
                @click="startNewConversationFromIcon"
              />
            </a-tooltip>
          </div>
        </a-col>
      </a-row>
    </div>
    <!-- Chat List -->
    <div
      class="chat-list-wrapper"
      id="messageContainer"
      :style="{
        height: attachmentsOpen ? 'calc(100% - 270px)' : 'calc(100% - 120px)',
      }"
    >
      <InputForm
        ref="inputFormRef"
        v-if="userInputForm && userInputForm.length > 0"
        :current-agent="currentAgent"
      />

      <template v-for="item in chatList" :key="item.id">
        <Answer
          v-if="item.isAnswer"
          :item="item"
          :feedback-disabled="feedbackDisabled"
          :is-responding="
            isResponding && item.id === chatList[chatList.length - 1].id
          "
          :current-agent="currentAgent"
          @suggestedQuestion="handleSuggestedQuestion"
        />
        <Question
          v-else
          :id="item.id"
          :content="item.content"
          :user-avatar="userAvatar"
          :item="item"
        />
      </template>
    </div>

    <ChatFooter
      ref="chatFooterRef"
      v-model:value="query"
      :loading="isResponding"
      :is-use-upload-file="isFileUploadEnabled"
      :is-debug-mode="isDebugMode"
      @onSubmit="onSubmit"
      @onCancel="handleFooterCancel"
      @addConversation="startNewConversation"
      @toggleHistoryVisible="onToggleHistoryVisible"
      @openAgents="onOpenAgents"
      :isPublic="isPublic"
      :is-file-upload-number-limits="isFileUploadNumberLimits"
      :is-file-upload-allowed-file-types="isFileUploadAllowedFileTypes"
      :system-parameters="systemParameters"
      @updateUploadFile="handleUploadFile"
      @onAttachmentsOpenChange="handleAttachmentsOpenChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, toRefs, computed } from "vue";
import {
  TransferMethod,
  type ChatItem,
  type VisionFile,
  type NodeTracing,
  WorkflowRunningStatus,
} from "./types/app";
import Question from "./components/Question.vue";
import Answer from "./components/Answer.vue";
import ChatFooter from "./components/ChatFooter.vue";
import InputForm from "./components/InputForm/InputForm.vue";
import {
  message as Message,
  Row as ARow,
  Col as ACol,
  Space as ASpace,
  Tooltip as ATooltip,
} from "ant-design-vue";
import { sendChatMessage, fetchChatList } from "./service/index";
import { updateMessageContainerScroll } from "../utils/utils";
import { ReloadOutlined } from "@ant-design/icons-vue";
import type { ClientAgentType } from "@/types/client";
import { getUserInfo as getUserInfoService } from "@/service/user";
import type { UploadFile } from "./types/app";
import { getSupportFileType } from "./utils/file-uploader";

interface Props {
  feedbackDisabled?: boolean;
  isDebugMode?: boolean;
  currentAgent: ClientAgentType;
  conversationId?: string; // 当前选中的会话ID，从父组件接收
  conversationInputs?: Record<string, any>; // 会话的输入参数，从父组件接收
  chartId?: string; //
  isPublic?: boolean; // 是否是公开模式
}

const props = withDefaults(defineProps<Props>(), {
  feedbackDisabled: false,
  isDebugMode: false,
  currentAgent: () => ({}),
  conversationId: undefined,
  conversationInputs: () => ({}),
});

const { chartId } = toRefs(props);

const isFileUploadEnabled = computed(
  () => props.currentAgent.difyAppParams?.file_upload?.enabled
);
const isFileUploadNumberLimits = computed(
  () => props.currentAgent.difyAppParams?.file_upload?.number_limits || 5
);

const isFileUploadAllowedFileTypes = computed(() => {
  return (
    props.currentAgent.difyAppParams?.file_upload?.allowed_file_types || []
  );
});

const systemParameters = computed(() => {
  return (
    props.currentAgent.difyAppParams?.system_parameters || {
      file_size_limit: 20,
      image_file_size_limit: 20,
    }
  );
});

// 定义向父组件发出的事件
const emit = defineEmits([
  "update:conversationId",
  "conversationUpdated",
  "startNewConversation",
  "toggleHistoryVisible",
  "openAgents",
]);

const chatFooterRef = ref<any>(null);
const inputFormRef = ref<any>(null);
const inputFormState = ref<Record<string, any>>({});

// 状态管理
const chatList = ref<ChatItem[]>([]);
const isResponding = ref(false);
const abortController = ref<AbortController | null>(null);
const messageTaskId = ref<string>("");
const isRespondingConCurrCon = ref(true);
const conversationIdChangeBecauseOfNew = ref(false);

const query = ref("");
const files = ref<UploadFile[]>([]);
const attachmentsOpen = ref(false);

const userAvatar = ref<string>("");

// 添加一个新方法用于更新会话和加载历史消息
const updateConversation = async (newConversationId: string) => {
  if (newConversationId) {
    // 加载会话历史消息
    await loadConversationMessages(newConversationId);
  } else {
    // 没有会话，当前助理开始新对话
    startNewConversation();
  }
};

const setIsRespondingConCurrCon = (value: boolean) => {
  isRespondingConCurrCon.value = value;
};

// 加载会话历史消息
const loadConversationMessages = async (conversationId: string) => {
  try {
    const response: any = await fetchChatList(
      conversationId,
      chartId.value as string
    );

    if (!response.ok) return;

    const difyRes = response.data || {};
    console.log("difyRes", difyRes);

    if (difyRes && difyRes.data) {
      // 转换消息格式
      const newChatList: ChatItem[] = [];
      difyRes.data.forEach((item: any) => {
        const uniqueResources = [];
        const fileUrlSet = new Set();

        for (const resource of item.retriever_resources) {
          if (resource.fileUrl && !fileUrlSet.has(resource.fileUrl)) {
            fileUrlSet.add(resource.fileUrl);
            uniqueResources.push(resource);
          }
        }

        newChatList.push({
          id: `question-${item.id}`,
          content: item.query,
          isAnswer: false,
          message_files:
            item.message_files?.filter(
              (file: any) => file.belongs_to === "user"
            ) || [],
        });
        newChatList.push({
          id: item.id,
          content: item.answer,
          agent_thoughts: item.agent_thoughts,
          feedback: item.feedback,
          isAnswer: true,
          message_files:
            item.message_files?.filter(
              (file: any) => file.belongs_to === "assistant"
            ) || [],
          retriever_resources: uniqueResources,
        });
      });

      if (difyRes.data.length > 0) {
        const lastItem = difyRes.data[difyRes.data.length - 1];
        inputFormState.value = lastItem.inputs;
        inputFormRef.value?.setFormData(inputFormState.value);
      }

      chatList.value = newChatList;
    }
  } catch (error) {
    console.error("Failed to load conversation messages:", error);
    Message.error("加载会话历史失败");
  }
};

// 取消当前请求
const cancelCurrentRequest = () => {
  if (abortController.value) {
    try {
      abortController.value.abort();
    } catch (error: any) {
      // 忽略 AbortError，因为这是预期的行为
      if (error.name !== "AbortError") {
        console.error("取消请求时发生错误:", error);
      }
    } finally {
      abortController.value = null;
    }
  }
};

// 开始新对话
const startNewConversation = () => {
  // 重置状态
  chatList.value = [];
  isResponding.value = false;
  cancelCurrentRequest();
  messageTaskId.value = "";
  isRespondingConCurrCon.value = true;
  conversationIdChangeBecauseOfNew.value = false;
  query.value = ""; // 清空输入框
  files.value = []; // 清空文件列表
  inputFormState.value = {};
  chatFooterRef.value && chatFooterRef.value.setAttachmentsOpenState(false);

  chatFooterRef.value && chatFooterRef.value.clearUploadFile();

  if (
    props.currentAgent?.difyAppParams?.opening_statement &&
    chatList.value.length === 0
  ) {
    let suggestedQuestions: string[] = [];
    if (props.currentAgent?.difyAppParams?.suggested_questions?.length) {
      suggestedQuestions =
        props.currentAgent?.difyAppParams?.suggested_questions;
    }

    chatList.value = [
      {
        id: `opening-${Date.now()}`,
        content: props.currentAgent.difyAppParams.opening_statement,
        isAnswer: true,
        message_files: [],
        suggested_questions: suggestedQuestions,
      },
    ];
  }

  // 通知父组件清空会话ID
  emit("update:conversationId", "");
};

const startNewConversationFromIcon = () => {
  // 通知父组件开始新对话
  emit("startNewConversation");
};

// 监听currentAgent变化，添加开场白
watch(
  () => props.currentAgent,
  () => {
    // 切换助手的时候开始新对话
    startNewConversation();
  },
  { immediate: true, deep: true }
);

const updateCurrentQA = ({
  responseItem,
  questionId,
  placeholderAnswerId,
  questionItem,
}: {
  responseItem: ChatItem;
  questionId: string;
  placeholderAnswerId: string;
  questionItem: ChatItem;
}) => {
  // 只更新响应内容，而不是重建整个列表
  const currentList = chatList.value;
  const responseIndex = currentList.findIndex(
    item => item.id === responseItem.id
  );
  const placeholderIndex = currentList.findIndex(
    item => item.id === placeholderAnswerId
  );

  // 如果问题不存在的话，新增问题
  if (!currentList.find(item => item.id === questionId)) {
    currentList.push({ ...questionItem });
  }

  // 如果响应项已存在，只更新内容
  if (responseIndex !== -1) {
    currentList[responseIndex] = { ...responseItem };
  } else {
    // 如果还没添加，先移除占位符再添加响应项
    if (placeholderIndex !== -1) {
      currentList.splice(placeholderIndex, 1, { ...responseItem });
    } else {
      currentList.push({ ...responseItem });
    }
  }

  // 触发更新
  chatList.value = [...currentList];

  updateMessageContainerScroll();
};

/**
 * 发送消息处理函数
 * 处理用户发送的消息，包括普通文本和带有文件的消息
 *
 * @param message 用户输入的消息文本
 * @param files 可选的视觉文件数组，用于多模态对话
 */
const handleSend = async (message: string, files?: UploadFile[]) => {
  // 验证消息不能为空
  if (!message?.trim()) {
    Message.error("消息不能为空");
    return;
  }

  // 防止重复发送，等待上一条消息响应完成
  if (isResponding.value) {
    Message.info("请等待上条信息响应完成");
    return;
  }

  try {
    // 构建请求数据对象
    const data: Record<string, any> = {
      inputs: props.conversationInputs || {},
      query: message,
      conversationId: props.conversationId || null,
      chartId: chartId.value,
    };

    // 如果有表单数据，合并到inputs中
    if (Object.keys(inputFormState.value).length > 0) {
      data.inputs = { ...data.inputs, ...inputFormState.value };
    }

    // 处理视觉文件，如果启用了视觉模式且有文件 TODO fix
    if (
      props.currentAgent.difyAppParams?.file_upload?.enabled &&
      files &&
      files.length > 0
    ) {
      // 筛选出状态为success或done的文件
      const validFiles = files.filter(
        file => file.status === "success" || file.status === "done"
      );

      data.files = validFiles.map(item => {
        const fileType = getSupportFileType(
          item?.response?.name as string,
          item?.response?.mimeType as string
        );
        return {
          transfer_method: TransferMethod.local_file,
          upload_file_id: item?.response?.id,
          url: "",
          type: fileType,
        };
      });
    }

    // 创建用户问题项
    const questionId = `question-${Date.now()}`;
    const questionItem: ChatItem = {
      id: questionId,
      content: message,
      isAnswer: false,
      message_files: files || [],
    };

    // 创建占位答案项，用于显示加载状态
    const placeholderAnswerId = `answer-placeholder-${Date.now()}`;
    const placeholderAnswerItem: ChatItem = {
      id: placeholderAnswerId,
      content: "",
      isAnswer: true,
    };

    // 更新聊天列表，添加问题和占位回答
    const newList = [...chatList.value, questionItem, placeholderAnswerItem];
    chatList.value = newList;

    // 标记是否处于Agent模式
    let isAgentMode = false;

    // 创建AI响应项
    const responseItem: ChatItem = {
      id: placeholderAnswerId,
      content: "",
      agent_thoughts: [], // Agent思考过程
      message_files: [], // 回复包含的文件
      isAnswer: true,
      retriever_resources: [], //引用
    };
    let hasSetResponseId = false; // 是否已设置响应ID

    // 记录当前会话ID
    const prevConversationId = props.conversationId || "";

    // 设置为正在响应状态
    isResponding.value = true;

    // 发送聊天消息请求
    await sendChatMessage(data, {
      // 获取AbortController，用于取消请求
      getAbortController: (controller: AbortController) => {
        abortController.value = controller;
      },
      // 处理流式数据返回
      onData: (
        message: string,
        isFirstMessage: boolean,
        { conversationId: newConversationId, messageId, taskId }: any
      ) => {
        // 根据模式处理返回的消息内容
        if (!isAgentMode) {
          // 普通模式，累加内容
          responseItem.content = responseItem.content + message;
        } else {
          // Agent模式，更新最后一个思考的内容
          const lastThought =
            responseItem.agent_thoughts?.[
              responseItem.agent_thoughts?.length - 1
            ];
          if (lastThought) lastThought.thought = lastThought.thought + message;
        }

        // 设置响应ID（仅设置一次）
        if (messageId && !hasSetResponseId) {
          responseItem.id = messageId;
          hasSetResponseId = true;
        }

        // 处理新会话ID
        if (isFirstMessage && newConversationId) {
          // 如果是新会话，通知父组件
          if (!props.conversationId) {
            conversationIdChangeBecauseOfNew.value = true;
            // 发出新会话ID的事件
            emit("update:conversationId", newConversationId);
          }
        }
        // 设置消息任务ID
        messageTaskId.value = taskId;

        // 检查会话是否已切换
        if (
          prevConversationId !== props.conversationId &&
          !(prevConversationId === "" && conversationIdChangeBecauseOfNew.value)
        ) {
          setIsRespondingConCurrCon(false);
          cancelCurrentRequest();
          return;
        }
        // 更新当前问答对的显示
        updateCurrentQA({
          responseItem,
          questionId,
          placeholderAnswerId,
          questionItem,
        });
        return true;
      },
      // 消息完成回调
      async onCompleted(hasError?: boolean) {
        // console.log("onCompleted", hasError);
        if (hasError) return;

        // 重置状态
        conversationIdChangeBecauseOfNew.value = false;
        isResponding.value = false;
      },
      // 处理文件响应
      onFile(file: VisionFile) {
        // 将文件添加到最后一个思考中
        const lastThought =
          responseItem.agent_thoughts?.[
            responseItem.agent_thoughts?.length - 1
          ];
        if (lastThought) {
          lastThought.message_files = [
            ...(lastThought.message_files || []),
            { ...file },
          ];
        }

        // 更新显示
        updateCurrentQA({
          responseItem,
          questionId,
          placeholderAnswerId,
          questionItem,
        });
      },
      // 处理Agent思考过程
      onThought(thought: any) {
        isAgentMode = true;
        const response = responseItem as any;

        // 设置消息ID
        if (thought.message_id && !hasSetResponseId) {
          response.id = thought.message_id;
          hasSetResponseId = true;
        }

        // 初始化思考数组
        if (!response.agent_thoughts) {
          response.agent_thoughts = [];
        }

        // 添加或更新思考
        if (response.agent_thoughts.length === 0) {
          // 添加第一个思考
          response.agent_thoughts.push(thought);
        } else {
          const lastThought =
            response.agent_thoughts[response.agent_thoughts.length - 1];
          if (lastThought.id === thought.id) {
            // 更新现有思考
            thought.thought = lastThought.thought;
            thought.message_files = lastThought.message_files;
            response.agent_thoughts[response.agent_thoughts.length - 1] =
              thought;
          } else {
            // 添加新思考
            response.agent_thoughts.push(thought);
          }
        }

        // 检查会话是否已切换
        if (
          prevConversationId !== props.conversationId &&
          !(prevConversationId === "" && conversationIdChangeBecauseOfNew.value)
        ) {
          setIsRespondingConCurrCon(false);
          cancelCurrentRequest();
          return false;
        }

        // 更新UI显示
        updateCurrentQA({
          responseItem,
          questionId,
          placeholderAnswerId,
          questionItem,
        });
        return true;
      },
      // 消息结束处理
      onMessageEnd: (messageEnd: any) => {
        // console.log("onMessageEnd", messageEnd);
        // 处理注释回复
        if (messageEnd.metadata?.annotation_reply) {
          responseItem.id = messageEnd.id;
          responseItem.annotation = {
            id: messageEnd.metadata.annotation_reply.id,
            authorName: messageEnd.metadata.annotation_reply.account.name,
          };
        }

        if (messageEnd.metadata?.retriever_resources) {
          //
          // 根据fileUrl字段对retriever_resources进行去重
          if (Array.isArray(messageEnd.metadata.retriever_resources)) {
            const uniqueResources = [];
            const fileUrlSet = new Set();

            for (const resource of messageEnd.metadata.retriever_resources) {
              if (resource.fileUrl && !fileUrlSet.has(resource.fileUrl)) {
                fileUrlSet.add(resource.fileUrl);
                uniqueResources.push(resource);
              }
            }

            responseItem.retriever_resources = uniqueResources;
          }
        }

        // 更新聊天列表，移除占位符，添加最终回答
        const newListWithAnswer = chatList.value
          .filter(
            (item: ChatItem) =>
              item.id !== responseItem.id && item.id !== placeholderAnswerId
          )
          .concat(
            !chatList.value.find((item: ChatItem) => item.id === questionId)
              ? [{ ...questionItem }]
              : []
          )
          .concat([{ ...responseItem }]);
        chatList.value = newListWithAnswer;

        // 重置状态
        conversationIdChangeBecauseOfNew.value = false;
        isResponding.value = false;
      },
      // 消息替换处理
      onMessageReplace: (messageReplace: { id: string; answer: string }) => {
        // 查找并替换消息内容
        const currentList = [...chatList.value];
        const currentIndex = currentList.findIndex(
          (item: ChatItem) => item.id === messageReplace.id
        );
        if (currentIndex !== -1) {
          currentList[currentIndex] = {
            ...currentList[currentIndex],
            content: messageReplace.answer,
          };
        }
        chatList.value = currentList;
      },
      // 错误处理
      onError() {
        // 重置响应状态
        isResponding.value = false;
        cancelCurrentRequest();
        // 移除占位答案项
        const currentList = [...chatList.value];
        const index = currentList.findIndex(
          (item: ChatItem) => item.id === placeholderAnswerId
        );
        if (index !== -1) {
          currentList.splice(index, 1);
        }
        chatList.value = currentList;
      },
      // 工作流开始事件
      onWorkflowStarted: (workflowData: {
        workflow_run_id: string;
        message_id: string;
      }) => {
        const { message_id, workflow_run_id } = workflowData;

        // 设置消息ID
        if (message_id && !hasSetResponseId) {
          responseItem.id = message_id;
          hasSetResponseId = true;
        }
        // 设置工作流信息
        responseItem.workflow_run_id = workflow_run_id;
        responseItem.workflowProcess = {
          status: WorkflowRunningStatus.Running,
          tracing: [],
        };

        // 更新UI显示
        updateCurrentQA({
          responseItem,
          questionId,
          placeholderAnswerId,
          questionItem,
        });
      },
      // 工作流完成事件
      onWorkflowFinished: ({ data }: any) => {
        if (responseItem.workflowProcess) {
          // 创建新的工作流状态对象以确保Vue能检测到变化
          const updatedWorkflowProcess = {
            ...responseItem.workflowProcess,
            status: data.status,
            tracing: [...responseItem.workflowProcess.tracing],
          };

          // 更新工作流状态
          responseItem.workflowProcess = updatedWorkflowProcess;

          // 更新UI显示
          updateCurrentQA({
            responseItem,
            questionId,
            placeholderAnswerId,
            questionItem,
          });
        }
      },
      // 节点开始事件
      onNodeStarted: ({ data }: any) => {
        if (responseItem.workflowProcess) {
          // 添加节点跟踪信息
          responseItem.workflowProcess.tracing.push({
            ...data,
            status: WorkflowRunningStatus.Running,
          });

          updateCurrentQA({
            responseItem,
            questionId,
            placeholderAnswerId,
            questionItem,
          });
        }
      },
      // 节点完成事件
      onNodeFinished: ({ data }: any) => {
        if (responseItem.workflowProcess) {
          // 查找并更新节点状态
          const currentIndex = responseItem.workflowProcess.tracing.findIndex(
            (item: NodeTracing) => item.node_id === data.node_id
          );
          if (currentIndex !== -1) {
            // 更新节点信息
            responseItem.workflowProcess.tracing[currentIndex] = data;

            updateCurrentQA({
              responseItem,
              questionId,
              placeholderAnswerId,
              questionItem,
            });
          }
        }
      },
    });
  } catch (error) {
    // 错误处理
    console.error("发送消息失败:", error);
    Message.error("发送消息失败，请重试");
    isResponding.value = false;
  }
};

const onSubmit = async () => {
  try {
    if (userInputForm.value && userInputForm.value.length > 0) {
      inputFormState.value = await inputFormRef.value?.getFormData();
    }
    handleSend(query.value, files.value);
    query.value = ""; // 发送后清空输入框
    files.value = []; // 发送后清空文件列表
    chatFooterRef.value && chatFooterRef.value.clearUploadFile();
    chatFooterRef.value && chatFooterRef.value.setAttachmentsOpenState(false);
  } catch (error) {
    console.error("onSubmit失败:", error);
  }
};

// 停止响应
const handleFooterCancel = () => {
  cancelCurrentRequest();
  isResponding.value = false;

  // 删除占位回答
  const currentList = [...chatList.value];
  const placeholderIndex = currentList.findIndex((item: ChatItem) =>
    item.id.startsWith("answer-placeholder-")
  );
  if (placeholderIndex !== -1) {
    currentList.splice(placeholderIndex, 1);
    chatList.value = currentList;
  }
};

const onToggleHistoryVisible = () => {
  emit("toggleHistoryVisible");
};

const onOpenAgents = () => {
  emit("openAgents");
};

const getUserInfo = async () => {
  const res: any = await getUserInfoService();
  if (res.ok) {
    userAvatar.value = res.data?.sysUser?.avatar;
  }
};

const handleSuggestedQuestion = (question: string) => {
  // console.log("question", question);
  handleSend(question);
};

const handleUploadFile = (fileList: UploadFile[]) => {
  // console.log("fileList", fileList);
  files.value = fileList;
};

const handleAttachmentsOpenChange = (open: boolean) => {
  attachmentsOpen.value = open;
};

const userInputForm = computed(() => {
  return props.currentAgent.difyAppParams?.user_input_form || [];
});

onMounted(() => {
  getUserInfo();
});

defineExpose({
  startNewConversation,
  updateConversation, // 暴露更新会话方法给父组件
});
</script>

<style lang="less" scoped>
.chat-container {
  height: 100%;
  position: relative;
  box-sizing: border-box;
  // padding-top: 48px;
  background:
    linear-gradient(
      180deg,
      rgba(23, 74, 228, 0) 29.44%,
      rgba(23, 74, 228, 0.06) 100%
    ),
    linear-gradient(
      90deg,
      #f3f3f7 0%,
      #f3f3f7 20%,
      #ebf0f9 60%,
      #f3f3f7 80%,
      #f3f3f7 100%
    );
}
.chatHeader {
  position: absolute;
  top: 0;
  z-index: 9;
  display: flex;
  align-items: baseline;
  width: 100%;
  padding: 14px 16px;
  background: rgba(243, 243, 247, 0.85);
  backdrop-filter: blur(2px);
  box-sizing: border-box;
}
.chatHeaderTitle {
  color: var(--text-color);
  font-weight: bold;
  font-size: 15px;
}
.chatHeaderTip {
  max-width: 600px;
  margin-left: 5px;
  overflow: hidden;
  color: var(--text-color-third);
  font-size: 12px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.debug-preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.reload-icon {
  color: #666;
  font-size: 15px;
  cursor: pointer;
}
.chat-list-wrapper {
  box-sizing: border-box;
  // height: calc(100% - 120px);
  height: calc(100% - 270px);

  overflow-y: auto;
  padding: 54px 14px 14px;
}
</style>
