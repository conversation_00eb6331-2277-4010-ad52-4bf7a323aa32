// import { fn } from "@storybook/test";
import type { Meta, StoryObj } from "@storybook/vue3";

import Message from "./index.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta = {
  title: "Chat/Message",
  component: Message,
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  argTypes: {
    position: {
      control: "select",
      options: ["left", "right"],
      description: "消息气泡的位置",
    },
    width: {
      control: "text",
      description: "消息气泡的宽度",
    },
    height: {
      control: "text",
      description: "消息气泡的高度",
    },
    bubbleClassName: {
      control: "text",
      description: "消息气泡的类名",
    },
  },
  args: {
    position: "left",
  },
} satisfies Meta<typeof Message>;

export default meta;
type Story = StoryObj<typeof meta>;
/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */

export const Default: Story = {
  args: {
    position: "left",
    bubbleClassName: "test-bubble",
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>这是消息内容</div>
        </Message>
      `,
  }),
};

export const Right: Story = {
  args: {
    position: "right",
    bubbleClassName: "test-bubble",
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
          <Message v-bind="args">
            <div>这是消息内容</div>
          </Message>
        `,
  }),
};
