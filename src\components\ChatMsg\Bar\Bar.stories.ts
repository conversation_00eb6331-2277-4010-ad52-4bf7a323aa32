import { Meta, StoryFn } from '@storybook/vue3';
import Bar from './index.vue';
import { PREFIX_CLS } from '../../../common/constants';
import { fn } from '@storybook/test';
import { provide } from 'vue';
import { ChartItemContextKey } from '../../ChatItem/useChartItemContext';

export default {
  title: 'Components/ChatMsg/Bar',
  component: Bar,
  tags: ['autodocs'],
  argTypes: {
    data: {
      control: { type: 'object' },
      description: '图表数据',
    },
    question: {
      control: { type: 'text' },
      description: '查询问题',
    },
    triggerResize: {
      control: { type: 'boolean' },
      description: '是否触发重新计算大小',
    },
    loading: {
      control: { type: 'boolean' },
      description: '加载状态',
    },
    metricField: {
      control: { type: 'object' },
      description: '度量字段信息',
    },
    onApplyAuth: {
      action: 'applyAuth',
      description: '申请权限的回调函数',
    },
  },
  parameters: {
    docs: {
      description: {
        component: '条形图组件，用于展示类别数据和对应的度量值',
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#333333' },
      ],
    },
  },
  // 全局装饰器，为所有故事提供ChartItemContext
  decorators: [
    (story) => ({
      components: { story },
      setup() {
        // 提供模拟的ChartItemContext
        provide(ChartItemContextKey, {
          register: (key: string, value: any): void => {
            console.log('Registered:', key, value);
          },
          call: (method: string, ...args: any[]): void => {
            console.log('Called:', method, args);
          }
        });
        return {};
      },
      template: '<story />'
    })
  ]
} as Meta<typeof Bar>;

const Template: StoryFn<typeof Bar> = (args) => ({
  components: { Bar },
  setup() {
    return { args, PREFIX_CLS };
  },
  template: `
    <div style="padding: 20px; width: 800px; height: 400px; border: 1px solid #eee; border-radius: 8px;">
      <Bar v-bind="args" @applyAuth="args.onApplyAuth" />
    </div>
  `,
});

// 基础数据示例
const baseData = {
  id: 1,
  question: '各部门销售额',
  aggregateInfo: {},
  chatContext: {},
  entityInfo: {
    dataSetInfo: {
      name: 'Sales Data'
    }
  },
  queryAuthorization: {},
  queryColumns: [
    {
      authorized: true,
      name: '部门',
      nameEn: 'Department',
      bizName: 'department',
      showType: 'CATEGORY',
      type: 'STRING',
      dataFormatType: 'string',
      dataFormat: {
        decimalPlaces: 0,
        needMultiply100: false
      }
    },
    {
      authorized: true,
      name: '销售额',
      nameEn: 'Sales',
      bizName: 'sales',
      showType: 'NUMBER',
      type: 'NUMERIC',
      dataFormatType: 'decimal',
      dataFormat: {
        decimalPlaces: 2,
        needMultiply100: false
      }
    }
  ],
  queryResults: [
    { department: '销售部', sales: 1245678 },
    { department: '市场部', sales: 894532 },
    { department: '研发部', sales: 456789 },
    { department: '财务部', sales: 325678 },
    { department: '客服部', sales: 567890 }
  ],
  queryId: 1001,
  queryMode: 'METRIC_BAR',
  queryState: 'SUCCESS',
  queryText: '各部门销售额是多少?',
  response: {},
  similarQueries: [],
  recommendedDimensions: [],
  textResult: '',
  errorMsg: '',
  textSummary: ''
};

const baseMetricField = {
  authorized: true,
  name: '销售额',
  nameEn: 'Sales',
  bizName: 'sales',
  showType: 'NUMBER',
  type: 'NUMERIC',
  dataFormatType: 'decimal',
  dataFormat: {
    decimalPlaces: 2,
    needMultiply100: false
  }
};

export const 基础柱状图 = Template.bind({});
基础柱状图.args = {
  data: baseData,
  question: '各部门销售额',
  loading: false,
  triggerResize: false,
  metricField: baseMetricField,
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};

export const 加载中 = Template.bind({});
加载中.args = {
  ...基础柱状图.args,
  loading: true,
};

export const 百分比数据 = Template.bind({});
百分比数据.args = {
  data: {
    ...baseData,
    queryColumns: [
      {
        authorized: true,
        name: '部门',
        nameEn: 'Department',
        bizName: 'department',
        showType: 'CATEGORY',
        type: 'STRING',
        dataFormatType: 'string',
        dataFormat: {
          decimalPlaces: 0,
          needMultiply100: false
        }
      },
      {
        authorized: true,
        name: '完成率',
        nameEn: 'Completion Rate',
        bizName: 'rate',
        showType: 'NUMBER',
        type: 'NUMERIC',
        dataFormatType: 'percent',
        dataFormat: {
          decimalPlaces: 2,
          needMultiply100: true
        }
      }
    ],
    queryResults: [
      { department: '销售部', rate: 0.95 },
      { department: '市场部', rate: 0.88 },
      { department: '研发部', rate: 0.75 },
      { department: '财务部', rate: 0.92 },
      { department: '客服部', rate: 0.83 }
    ],
  },
  question: '各部门目标完成率',
  loading: false,
  metricField: {
    authorized: true,
    name: '完成率',
    nameEn: 'Completion Rate',
    bizName: 'rate',
    showType: 'NUMBER',
    type: 'NUMERIC',
    dataFormatType: 'percent',
    dataFormat: {
      decimalPlaces: 2,
      needMultiply100: true
    }
  },
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};

export const 无权限状态 = Template.bind({});
无权限状态.args = {
  data: {
    ...baseData,
    queryColumns: [
      {
        authorized: true,
        name: '部门',
        nameEn: 'Department',
        bizName: 'department',
        showType: 'CATEGORY',
        type: 'STRING',
        dataFormatType: 'string',
        dataFormat: {
          decimalPlaces: 0,
          needMultiply100: false
        }
      },
      {
        authorized: false,
        name: '销售额',
        nameEn: 'Sales',
        bizName: 'sales',
        showType: 'NUMBER',
        type: 'NUMERIC',
        dataFormatType: 'decimal',
        dataFormat: {
          decimalPlaces: 2,
          needMultiply100: false
        }
      }
    ]
  },
  question: '各部门销售额',
  loading: false,
  metricField: {
    ...baseMetricField,
    authorized: false
  },
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};

export const 深色背景 = Template.bind({});
深色背景.args = {
  ...基础柱状图.args,
};
深色背景.parameters = {
  backgrounds: { default: 'dark' },
};
深色背景.decorators = [
  () => ({ 
    template: '<div style="padding: 20px; color: white; background-color: #333; border-radius: 8px;"><story /></div>' 
  }),
];
