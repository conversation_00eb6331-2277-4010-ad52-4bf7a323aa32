<template>
  <div :class="prefixCls">
    <icon-font
      v-if="!isMobile"
      type="icon-zhinengsuanfa"
      :class="`${prefixCls}-avatar`"
    />
    <div :class="isMobile ? `${prefixCls}-mobile-msg-card` : ''">
      <div :class="`${prefixCls}-time`">
        {{
          parseTimeCost?.parseStartTime
            ? dayjs(parseTimeCost.parseStartTime).format("M月D日 HH:mm")
            : ""
        }}
      </div>
      <div :class="contentClass">
        <template
          v-if="
            currentAgent?.enableFeedback === 1 &&
            !questionId &&
            showExpandParseTip
          "
        >
          <div style="margin-bottom: 10px">
            <expand-parse-tip
              :is-simple-mode="isSimpleMode"
              :parse-info-options="preParseInfoOptions"
              :agent-id="agentId"
              :integrate-system="integrateSystem"
              :parse-time-cost="parseTimeCost?.parseTime"
              :is-developer="isDeveloper"
              @select-parse-info="onExpandSelectParseInfo"
              @switch-entity="onSwitchEntity"
              @filters-change="onFiltersChange"
              @date-info-change="onDateInfoChange"
              @refresh="onRefresh"
              :handle-preset-click="handlePresetClick"
            />
          </div>
        </template>

        <template v-if="!preParseMode">
          <parse-tip
            :is-simple-mode="isSimpleMode"
            :parse-loading="parseLoading"
            :parse-info-options="parseInfoOptions"
            :parse-tip="parseTip"
            :current-parse-info="parseInfo"
            :agent-id="agentId"
            :dimension-filters="dimensionFilters"
            :date-info="dateInfo"
            :entity-info="entityInfo"
            :integrate-system="integrateSystem"
            :parse-time-cost="parseTimeCost?.parseTime"
            :is-developer="isDeveloper"
            @select-parse-info="onSelectParseInfo"
            @switch-entity="onSwitchEntity"
            @filters-change="onFiltersChange"
            @date-info-change="onDateInfoChange"
            @refresh="onRefresh"
            :handle-preset-click="handlePresetClick"
          />
        </template>

        <template v-if="executeMode">
          <a-spin :spinning="entitySwitchLoading">
            <div style="min-height: 50px">
              <template
                v-if="
                  !isMobile &&
                  parseInfo?.sqlInfo &&
                  isDeveloper &&
                  isDebugMode &&
                  !isSimpleMode
                "
              >
                <sql-item
                  :agent-id="agentId"
                  :query-id="parseInfo.queryId"
                  :question="msg"
                  :llm-req="llmReq"
                  :llm-resp="llmResp"
                  :integrate-system="integrateSystem"
                  :query-mode="parseInfo.queryMode"
                  :sql-info="parseInfo.sqlInfo"
                  :sql-time-cost="parseTimeCost?.sqlTime"
                  :execute-error-msg="executeErrorMsg"
                />
              </template>
              <execute-item
                :is-simple-mode="isSimpleMode"
                :query-id="parseInfo?.queryId"
                :question="msg"
                :query-mode="parseInfo?.queryMode"
                :execute-loading="executeLoading"
                :execute-tip="executeTip"
                :execute-error-msg="executeErrorMsg"
                :chart-index="0"
                :data="data"
                :trigger-resize="triggerResize"
                :execute-item-node="executeItemNode"
                :is-developer="isDeveloper"
                :render-custom-execute-node="renderCustomExecuteNode"
              />
            </div>
          </a-spin>
        </template>

        <template
          v-if="
            executeMode &&
            !executeLoading &&
            !isSimpleMode &&
            parseInfo?.queryMode !== 'PLAIN_TEXT'
          "
        >
          <similar-question-item
            :query-id="parseInfo?.queryId"
            :default-expanded="parseTip !== '' || executeTip !== ''"
            :similar-queries="data?.similarQueries"
            @select-question="onSelectQuestion"
          />
        </template>
      </div>

      <template
        v-if="
          (parseTip !== '' || (executeMode && !executeLoading)) &&
          parseInfo?.queryMode !== 'PLAIN_TEXT'
        "
      >
        <tools
          :is-last-message="isLastMessage"
          :query-id="parseInfo?.queryId || 0"
          :score-value="score"
          :is-parser-error="isParserError"
          :is-simple-mode="isSimpleMode"
          @export-data="onExportData"
          @re-execute="deleteQueryInfo"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, provide, onMounted, computed } from "vue";
import { message, Spin as ASpin } from "ant-design-vue";
import IconFont from "../IconFont/index.vue";
import ExpandParseTip from "./ExpandParseTip.vue";
import ParseTip from "./ParseTip.vue";
import ExecuteItem from "./ExecuteItem.vue";
import SqlItem from "./SqlItem.vue";
import SimilarQuestionItem from "./SimilarQuestionItem.vue";
import Tools from "../Tools/index.vue";
import dayjs, { Dayjs } from "dayjs";
import { exportCsvFile, isMobile } from "../../utils/utils";
import { ChartItemContextKey } from "./useChartItemContext";
import {
  PREFIX_CLS,
  PARSE_ERROR_TIP,
  SEARCH_EXCEPTION_TIP,
} from "../../common/constants";
import {
  chatExecute,
  chatParse,
  queryData,
  deleteQuery,
  switchEntity,
} from "../../service";
import {
  ChatContextType,
  DateInfoType,
  EntityInfoType,
  FilterItemType,
  MsgDataType,
  ParseStateEnum,
  ParseTimeCostType,
  SimilarQuestionType,
} from "../../common/type";
import type { AgentType } from "../../Chat/type";
import type { PropType } from "vue";

// Props 定义
const props = defineProps({
  msg: {
    type: String,
    required: true,
  },
  conversationId: Number,
  questionId: Number,
  modelId: Number,
  agentId: Number,
  score: Number,
  filter: Array,
  parseInfos: Array as PropType<ChatContextType[]>,
  parseTimeCostValue: Object as PropType<ParseTimeCostType>,
  msgData: Object as PropType<MsgDataType>,
  triggerResize: Boolean,
  isDeveloper: Boolean,
  integrateSystem: String,
  executeItemNode: Object,
  renderCustomExecuteNode: Boolean,
  isSimpleMode: Boolean,
  isDebugMode: Boolean,
  currentAgent: Object as PropType<AgentType>,
  isLastMessage: Boolean,
  onMsgDataLoaded: Function as PropType<
    (data: MsgDataType, valid: boolean, isRefresh?: boolean) => void
  >,
  onUpdateMessageScroll: Function as PropType<() => void>,
  onSendMsg: Function as PropType<(msg: string) => void>,
});

// 状态定义
const parseLoading = ref(false);
const parseTimeCost = ref<ParseTimeCostType | undefined>();
const parseInfo = ref<ChatContextType | undefined>();
const parseInfoOptions = ref<ChatContextType[]>([]);
const preParseInfoOptions = ref<ChatContextType[]>([]);
const parseTip = ref("");
const executeMode = ref(false);
const preParseMode = ref(false);
const showExpandParseTip = ref(false);
const executeLoading = ref(false);
const executeTip = ref("");
const executeErrorMsg = ref("");
const data = ref<MsgDataType | undefined>();
const entitySwitchLoading = ref(false);
const dimensionFilters = ref<FilterItemType[]>([]);
const dateInfo = reactive<DateInfoType>({} as DateInfoType);
const entityInfo = reactive<EntityInfoType>({} as EntityInfoType);
const dataCache = ref<Record<number, { tip: string; data?: MsgDataType }>>({});
const isParserError = ref(false);

const prefixCls = `${PREFIX_CLS}-item`;
const contentClass = computed(() => ({
  [`${prefixCls}-content`]: true,
  [`${prefixCls}-content-mobile`]: isMobile,
}));

// 方法实现

// 提供上下文
provide(ChartItemContextKey, {
  register: (key: string, value: any) => {
    console.log("Register:", key, value);
  },
  call: (...args: any[]) => {
    console.log("Call:", args);
    message.error("该条消息暂不支持该操作");
  },
});

// 更新数据方法
const updateData = (res: any) => {
  let tip: string = "";
  let resultData: MsgDataType | undefined = undefined;
  const {
    queryColumns,
    queryResults,
    queryState,
    queryMode,
    response,
    chatContext,
    errorMsg,
  } = res.data || {};
  executeErrorMsg.value = errorMsg;
  if (res.code === 400 || res.code === 401 || res.code === 412) {
    tip = res.msg;
    isParserError.value = true;
  } else if (res.code !== 200) {
    tip = SEARCH_EXCEPTION_TIP;
    isParserError.value = true;
  } else if (queryState !== "SUCCESS") {
    tip =
      response && typeof response === "string"
        ? response
        : SEARCH_EXCEPTION_TIP;
    isParserError.value = true;
  } else if (
    (queryColumns && queryColumns.length > 0 && queryResults) ||
    queryMode === "WEB_PAGE" ||
    queryMode === "WEB_SERVICE" ||
    queryMode === "PLAIN_TEXT"
  ) {
    resultData = res.data;
    tip = "";
    isParserError.value = false;
  }
  if (chatContext) {
    dataCache.value = {
      ...dataCache.value,
      [chatContext!.id!]: { tip, data: resultData },
    };
  }
  if (resultData) {
    data.value = resultData;
    executeTip.value = "";
    return true;
  }
  executeTip.value = tip || SEARCH_EXCEPTION_TIP;
  return false;
};

// 执行方法
const onExecute = async (
  parseInfoValue: ChatContextType,
  parseInfoArray?: ChatContextType[],
  isSwitchParseInfo?: boolean,
  isRefresh = false
) => {
  executeMode.value = true;
  if (isSwitchParseInfo) {
    entitySwitchLoading.value = true;
  } else {
    executeLoading.value = true;
  }
  try {
    const res: any = await chatExecute(
      props.msg,
      props.conversationId!,
      parseInfoValue,
      props.agentId
    );
    const valid = updateData(res);
    props.onMsgDataLoaded?.(
      {
        ...res.data,
        parseInfos: parseInfoArray,
        queryId: parseInfoValue.queryId,
      },
      valid,
      isRefresh
    );
  } catch (e) {
    const tip = SEARCH_EXCEPTION_TIP;
    executeTip.value = SEARCH_EXCEPTION_TIP;
    dataCache.value = { ...dataCache.value, [parseInfoValue!.id!]: { tip } };
  }
  if (isSwitchParseInfo) {
    entitySwitchLoading.value = false;
  } else {
    executeLoading.value = false;
  }
};

// 更新维度过滤器
const updateDimensionFilters = (filters: FilterItemType[]) => {
  dimensionFilters.value = filters.sort((a, b) => {
    if (a.name < b.name) {
      return -1;
    }
    if (a.name > b.name) {
      return 1;
    }
    return 0;
  });
};

// 发送消息方法
const sendMsg = async () => {
  parseLoading.value = true;
  const parseData: any = await chatParse({
    queryText: props.msg,
    chatId: props.conversationId,
    modelId: props.modelId,
    agentId: props.agentId,
    filters: props.filter,
  });
  parseLoading.value = false;
  const { code, data } = parseData || {};
  const {
    state,
    selectedParses,
    candidateParses,
    queryId,
    parseTimeCost: parseTCost,
    errorMsg,
  } = data || {};
  const parses = selectedParses?.concat(candidateParses || []) || [];
  if (
    code !== 200 ||
    state === ParseStateEnum.FAILED ||
    !parses.length ||
    (!parses[0]?.properties?.type && !parses[0]?.queryMode)
  ) {
    parseTip.value =
      state === ParseStateEnum.FAILED && errorMsg ? errorMsg : PARSE_ERROR_TIP;
    parseInfo.value = { queryId } as any;
    return;
  }
  props.onUpdateMessageScroll?.();
  const parseInfosArray = parses.slice(0, 5).map((item: any) => ({
    ...item,
    queryId,
  }));
  if (parseInfosArray.length > 1) {
    preParseInfoOptions.value = parseInfosArray;
    showExpandParseTip.value = true;
    preParseMode.value = true;
  }
  parseInfoOptions.value = parseInfosArray || [];
  const parseInfoValue = parseInfosArray[0];
  if (
    !(props.currentAgent?.enableFeedback === 1 && parseInfosArray.length > 1)
  ) {
    parseInfo.value = parseInfoValue;
  }
  parseTimeCost.value = parseTCost;
  Object.assign(entityInfo, parseInfoValue.entityInfo || {});
  const dateInfoValue = parseInfoValue.dateInfo;
  if (dateInfoValue) {
    Object.assign(dateInfo, dateInfoValue);
  }
  if (
    parseInfoValue.dimensionFilters &&
    parseInfoValue.dimensionFilters.length > 0
  ) {
    updateDimensionFilters(parseInfoValue.dimensionFilters);
  }
  return parseInfoValue;
};

// 实现重置状态方法
const resetState = () => {
  parseLoading.value = false;
  parseTimeCost.value = undefined;
  parseInfo.value = undefined;
  parseInfoOptions.value = [];
  preParseMode.value = false;
  showExpandParseTip.value = false;
  preParseInfoOptions.value = [];
  parseTip.value = "";
  executeMode.value = false;
  dimensionFilters.value = [];
  data.value = undefined;
  executeErrorMsg.value = "";
  Object.assign(dateInfo, {} as DateInfoType);
  Object.assign(entityInfo, {} as EntityInfoType);
  dataCache.value = {};
  isParserError.value = false;
};

// 组件挂载和外部属性变更的处理
onMounted(async () => {
  initChatItem(props.msg, props.msgData)

  if (props.parseInfos && props.parseInfos.length > 0) {
    parseInfoOptions.value = props.parseInfos;
    parseInfo.value = props.parseInfos[0];
    return;
  }
  if (props.msgData) {
    data.value = props.msgData;
    return;
  }
  if (!props.conversationId) return;
  const parseInfoValue = await sendMsg();
  if (!parseInfoValue) return;
  onExecute(parseInfoValue, parseInfoOptions.value);
});

watch(
  () => props.triggerResize,
  () => {
    if (!props.triggerResize) return;
    // 可能需要处理resize逻辑
  }
);

// 实体切换方法
const onSwitchEntity = async (entityId: string) => {
  if (!parseInfo.value || entitySwitchLoading.value) return;
  try {
    const res: any = await switchEntity(
      entityId,
      parseInfo.value.id!,
      props.modelId!
    );
    if (res.code === 200 && res.data) {
      const { dimensionFilters: filters, dateInfo: newDateInfo, id } = res.data;
      const parseInfoValue = {
        ...parseInfo.value,
        dimensionFilters: filters,
        dateInfo: newDateInfo,
        id,
      };
      parseInfo.value = parseInfoValue;
      if (filters && filters.length > 0) {
        updateDimensionFilters(filters);
      }
      if (newDateInfo) {
        Object.assign(dateInfo, newDateInfo);
      }
      onExecute(parseInfoValue, undefined, true);
    }
  } catch (e) {
    message.error("切换失败");
  }
};

// 导出数据方法
const onExportData = () => {
  const { queryColumns, queryResults } = data.value || {};
  if (!!queryResults) {
    const exportData = queryResults.map(item => {
      return Object.keys(item).reduce(
        (result, key) => {
          const columnName =
            queryColumns?.find(column => column.nameEn === key)?.name || key;
          result[columnName] = item[key];
          return result;
        },
        {} as Record<string, any>
      );
    });
    exportCsvFile(exportData);
  }
};

// 删除查询信息
const deleteQueryInfo = async (queryId: number) => {
  const { code }: any = await deleteQuery(queryId);
  if (code === 200) {
    resetState();
    initChatItem(props.msg, undefined);
  }
};

// 选择解析信息
const onSelectParseInfo = async (parseInfoValue: ChatContextType) => {
  parseInfo.value = parseInfoValue;
  updateDimensionFilters(parseInfoValue.dimensionFilters || []);
  setDateInfo(parseInfoValue.dateInfo);
  if (parseInfoValue.entityInfo) {
    Object.assign(entityInfo, parseInfoValue.entityInfo);
  }
  if (dataCache.value[parseInfoValue.id!]) {
    const { tip, data: cachedData } = dataCache.value[parseInfoValue.id!];
    executeTip.value = tip;
    data.value = cachedData;
    props.onMsgDataLoaded?.(
      {
        ...(cachedData as any),
        parseInfos: parseInfoOptions.value,
        queryId: parseInfoValue.queryId,
      },
      true,
      true
    );
  } else {
    onExecute(parseInfoValue, parseInfoOptions.value, true);
  }
};

// 展开选择解析信息
const onExpandSelectParseInfo = async (parseInfoValue: ChatContextType) => {
  parseInfo.value = parseInfoValue;
  preParseMode.value = false;
  const { id: parseId, queryId } = parseInfoValue;
  parseLoading.value = true;
  const { code, data }: any = await chatParse({
    queryText: props.msg,
    chatId: props.conversationId,
    modelId: props.modelId,
    agentId: props.agentId,
    filters: props.filter,
    parseId,
    queryId,
    parseInfo: parseInfoValue,
  });
  parseLoading.value = false;
  if (code === 200) {
    parseTimeCost.value = data.parseTimeCost;
    const parseInfo = data.selectedParses[0];
    parseInfo.queryId = data.queryId;
    parseInfoOptions.value = [parseInfo];
    parseInfo.value = parseInfo;
    updateDimensionFilters(parseInfo.dimensionFilters || []);
    setDateInfo(parseInfo.dateInfo);
    if (parseInfo.entityInfo) {
      Object.assign(entityInfo, parseInfo.entityInfo);
    }
    onExecute(parseInfo, [parseInfo], true, true);
  }
};

// 选择问题
const onSelectQuestion = (question: SimilarQuestionType) => {
  props.onSendMsg?.(question.queryText);
};

// 初始化聊天项
const initChatItem = (msg: string, msgData?: MsgDataType) => {
  console.log(msg, msgData)
  if (msgData) {
    const parseInfoOptionsValue =
      props.parseInfos && props.parseInfos.length > 0
        ? props.parseInfos.map(item => ({ ...item, queryId: msgData.queryId }))
        : [{ ...msgData.chatContext, queryId: msgData.queryId }];
    const parseInfoValue = parseInfoOptionsValue[0];
    parseInfoOptions.value = parseInfoOptionsValue;
    parseInfo.value = parseInfoValue;
    parseTimeCost.value = props.parseTimeCostValue;
    updateDimensionFilters(parseInfoValue.dimensionFilters || []);
    setDateInfo(parseInfoValue.dateInfo);
    executeMode.value = true;
    updateData({ code: 200, data: msgData, msg: "success" });
  } else if (msg) {
    sendMsg();
  }
};

// 设置日期信息
const setDateInfo = (newDateInfo?: DateInfoType) => {
  if (newDateInfo) {
    Object.assign(dateInfo, newDateInfo);
  }
};

// 处理日期信息变更
const onDateInfoChange = (dates: [Dayjs | null, Dayjs | null] | null) => {
  if (dates && dates[0] && dates[1]) {
    const [start, end] = dates;
    Object.assign(dateInfo, {
      ...(dateInfo || {}),
      startDate: dayjs(start).format("YYYY-MM-DD"),
      endDate: dayjs(end).format("YYYY-MM-DD"),
      dateMode: "BETWEEN",
      unit: 0,
    });
  }
};

// 处理预设点击
const handlePresetClick = (dateRange: [Dayjs, Dayjs]) => {
  onDateInfoChange(dateRange);
};

// 处理过滤器变更
const onFiltersChange = (filters: FilterItemType[]) => {
  dimensionFilters.value = filters;
};

// 刷新
const onRefresh = async (parseInfoValue?: ChatContextType) => {
  entitySwitchLoading.value = true;
  const { dimensions, metrics, id, queryId } =
    parseInfoValue || parseInfo.value || {};
  const chatContextValue = {
    dimensions,
    metrics,
    dateInfo,
    dimensionFilters: dimensionFilters.value,
    parseId: id,
    queryId,
  };
  const res: any = await queryData(chatContextValue);
  entitySwitchLoading.value = false;
  if (res.code === 200) {
    const resChatContext = res.data?.chatContext;
    const contextValue = { ...(resChatContext || chatContextValue), queryId };
    const dataValue = {
      ...res.data,
      chatContext: contextValue,
      parseInfos: parseInfoOptions.value,
      queryId,
    };
    props.onMsgDataLoaded?.(dataValue, true, true);
    data.value = dataValue;
    parseInfo.value = contextValue;
    dataCache.value = {
      ...dataCache.value,
      [id!]: { tip: "", data: dataValue },
    };
  }
};

// 计算属性
const llmReq = computed(() => parseInfo.value?.properties?.CONTEXT?.llmReq);
const llmResp = computed(() => parseInfo.value?.properties?.CONTEXT?.llmResp);
</script>

<style lang="less">
@import "./style.less";
</style>
