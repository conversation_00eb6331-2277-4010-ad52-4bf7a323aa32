import type { Meta, StoryObj } from '@storybook/vue3';
import DrillDownDimensions from './index.vue';
import { ref } from 'vue';
import type { DrillDownDimensionType, FilterItemType } from '../../common/type';

const meta: Meta<typeof DrillDownDimensions> = {
  title: 'Components/DrillDownDimensions',
  component: DrillDownDimensions,
  tags: ['autodocs'],
  argTypes: {
    drillDownDimensions: { control: 'object' },
    drillDownDimension: { control: 'object' },
    secondDrillDownDimension: { control: 'object' },
    originDimensions: { control: 'object' },
    dimensionFilters: { control: 'object' },
    onSelectDimension: { action: 'onSelectDimension' },
    onSelectSecondDimension: { action: 'onSelectSecondDimension' },
  },
};

export default meta;
type Story = StoryObj<typeof DrillDownDimensions>;

// 模拟数据
const mockDimensions: DrillDownDimensionType[] = [
  { id: 1, name: '维度1', bizName: 'dimension1', model: 1 },
  { id: 2, name: '维度2', bizName: 'dimension2', model: 1 },
  { id: 3, name: '维度3', bizName: 'dimension3', model: 1 },
  { id: 4, name: '维度4', bizName: 'dimension4', model: 1 },
  { id: 5, name: '维度5', bizName: 'dimension5', model: 1 },
  { id: 6, name: '维度6', bizName: 'dimension6', model: 1 },
  { id: 7, name: '维度7', bizName: 'dimension7', model: 1 },
];

// 基础故事
export const Default: Story = {
  render: (args) => ({
    components: { DrillDownDimensions },
    setup() {
      const drillDownDimension = ref<DrillDownDimensionType | undefined>(undefined);
      const secondDrillDownDimension = ref<DrillDownDimensionType | undefined>(undefined);
      
      const selectDimension = (dimension?: DrillDownDimensionType) => {
        drillDownDimension.value = dimension;
        if (args.onSelectDimension) {
          args.onSelectDimension(dimension);
        }
      };
      
      const selectSecondDimension = (dimension?: DrillDownDimensionType) => {
        secondDrillDownDimension.value = dimension;
        if (args.onSelectSecondDimension) {
          args.onSelectSecondDimension(dimension);
        }
      };

      return { 
        drillDownDimensions: args.drillDownDimensions as DrillDownDimensionType[],
        drillDownDimension, 
        secondDrillDownDimension, 
        originDimensions: args.originDimensions,
        dimensionFilters: args.dimensionFilters,
        selectDimension, 
        selectSecondDimension 
      };
    },
    template: `
      <DrillDownDimensions
        :drillDownDimensions="drillDownDimensions"
        :drillDownDimension="drillDownDimension"
        :secondDrillDownDimension="secondDrillDownDimension"
        :originDimensions="originDimensions"
        :dimensionFilters="dimensionFilters"
        :onSelectDimension="selectDimension"
        :onSelectSecondDimension="selectSecondDimension"
      />
    `,
  }),
  args: {
    drillDownDimensions: mockDimensions,
  },
};

// 已选择一级维度的故事
export const WithSelectedDimension: Story = {
  render: (args) => ({
    components: { DrillDownDimensions },
    setup() {
      const drillDownDimension = ref<DrillDownDimensionType>(mockDimensions[0]);
      const secondDrillDownDimension = ref<DrillDownDimensionType | undefined>(undefined);
      
      const selectDimension = (dimension?: DrillDownDimensionType) => {
        drillDownDimension.value = dimension || mockDimensions[0];
        if (args.onSelectDimension) {
          args.onSelectDimension(dimension);
        }
      };
      
      const selectSecondDimension = (dimension?: DrillDownDimensionType) => {
        secondDrillDownDimension.value = dimension;
        if (args.onSelectSecondDimension) {
          args.onSelectSecondDimension(dimension);
        }
      };

      return { 
        drillDownDimensions: args.drillDownDimensions as DrillDownDimensionType[],
        drillDownDimension, 
        secondDrillDownDimension, 
        originDimensions: args.originDimensions,
        dimensionFilters: args.dimensionFilters,
        selectDimension, 
        selectSecondDimension 
      };
    },
    template: `
      <DrillDownDimensions
        :drillDownDimensions="drillDownDimensions"
        :drillDownDimension="drillDownDimension"
        :secondDrillDownDimension="secondDrillDownDimension"
        :originDimensions="originDimensions"
        :dimensionFilters="dimensionFilters"
        :onSelectDimension="selectDimension"
        :onSelectSecondDimension="selectSecondDimension"
      />
    `,
  }),
  args: {
    drillDownDimensions: mockDimensions,
  },
};

// 同时选择一级和二级维度的故事
export const WithBothDimensions: Story = {
  render: (args) => ({
    components: { DrillDownDimensions },
    setup() {
      const drillDownDimension = ref<DrillDownDimensionType>(mockDimensions[0]);
      const secondDrillDownDimension = ref<DrillDownDimensionType>(mockDimensions[1]);
      
      const selectDimension = (dimension?: DrillDownDimensionType) => {
        drillDownDimension.value = dimension || mockDimensions[0];
        if (args.onSelectDimension) {
          args.onSelectDimension(dimension);
        }
      };
      
      const selectSecondDimension = (dimension?: DrillDownDimensionType) => {
        secondDrillDownDimension.value = dimension || mockDimensions[1];
        if (args.onSelectSecondDimension) {
          args.onSelectSecondDimension(dimension);
        }
      };

      return { 
        drillDownDimensions: args.drillDownDimensions as DrillDownDimensionType[],
        drillDownDimension, 
        secondDrillDownDimension, 
        originDimensions: args.originDimensions,
        dimensionFilters: args.dimensionFilters,
        selectDimension, 
        selectSecondDimension 
      };
    },
    template: `
      <DrillDownDimensions
        :drillDownDimensions="drillDownDimensions"
        :drillDownDimension="drillDownDimension"
        :secondDrillDownDimension="secondDrillDownDimension"
        :originDimensions="originDimensions"
        :dimensionFilters="dimensionFilters"
        :onSelectDimension="selectDimension"
        :onSelectSecondDimension="selectSecondDimension"
      />
    `,
  }),
  args: {
    drillDownDimensions: mockDimensions,
  },
}; 