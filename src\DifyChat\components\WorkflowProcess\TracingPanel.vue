<template>
  <div :class="['tracing-panel', className]" @click.stop="stopPropagation">
    <template v-for="node in treeNodes" :key="node.id">
      <div
        v-if="isParallelFirstNode(node)"
        :class="['parallel-node']"
        :data-parallel-id="node.id"
        @mouseenter="handleParallelMouseEnter(node.id)"
        @mouseleave="handleParallelMouseLeave"
      >
        <div class="parallel-header">
          <button
            @click="toggleCollapse(node.id)"
            :class="[
              'toggle-button',
              hoveredParallel === node.id
                ? 'toggle-button-hovered'
                : 'toggle-button-normal',
            ]"
          >
            <RiArrowDownSLine
              v-if="hoveredParallel === node.id"
              class="icon-small"
            />
            <RiMenu4Line v-else class="icon-small icon-tertiary" />
          </button>
          <div class="parallel-title">
            <span>{{ node.parallelDetail.parallelTitle }}</span>
          </div>
          <div class="divider"></div>
        </div>
        <div
          :class="['parallel-content', { hidden: collapsedNodes.has(node.id) }]"
        >
          <div
            :class="[
              'vertical-line',
              hoveredParallel === node.id
                ? 'vertical-line-hovered'
                : 'vertical-line-normal',
            ]"
          ></div>
          <template
            v-for="childNode in node.parallelDetail.children"
            :key="childNode.id"
          >
            <div>
              <div
                :class="[
                  'branch-title',
                  hoveredParallel === childNode.id
                    ? 'text-tertiary'
                    : 'text-quaternary',
                ]"
              >
                {{ childNode?.parallelDetail?.branchTitle }}
              </div>
              <NodePanel
                :nodeInfo="childNode"
                :onShowIterationDetail="handleShowIterationResultList"
                :onShowLoopDetail="handleShowLoopResultList"
                :onShowRetryDetail="handleShowRetryResultList"
                :onShowAgentOrToolLog="handleShowAgentOrToolLog"
                :hideInfo="hideNodeInfo"
                :hideProcessDetail="hideNodeProcessDetail"
              />
            </div>
          </template>
        </div>
      </div>
      <div v-else :key="node.id">
        <div
          :class="[
            'branch-title',
            hoveredParallel === node.id ? 'text-tertiary' : 'text-quaternary',
          ]"
        >
          {{ node?.parallelDetail?.branchTitle }}
        </div>
        <NodePanel
          :nodeInfo="node"
          :onShowIterationDetail="handleShowIterationResultList"
          :onShowLoopDetail="handleShowLoopResultList"
          :onShowRetryDetail="handleShowRetryResultList"
          :onShowAgentOrToolLog="handleShowAgentOrToolLog"
          :hideInfo="hideNodeInfo"
          :hideProcessDetail="hideNodeProcessDetail"
        />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { RiArrowDownSLine, RiMenu4Line } from "@remixicon/vue";
import { useLogs } from "./hooks";
import NodePanel from "./node.vue";
import type { NodeTracing } from "@/DifyChat/types/app";
// import formatNodeList from "@/app/components/workflow/run/utils/format-log";

interface Props {
  list: NodeTracing[];
  className?: string;
  hideNodeInfo?: boolean;
  hideNodeProcessDetail?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  className: "",
  hideNodeInfo: false,
  hideNodeProcessDetail: false,
});

// const treeNodes = computed(() => formatNodeList(props.list));
const treeNodes = computed(() =>props.list);
const collapsedNodes = ref<Set<string>>(new Set());
const hoveredParallel = ref<string | null>(null);

const isParallelFirstNode = (node: NodeTracing) => {
  return !!node.parallelDetail?.isParallelStartNode;
};

const toggleCollapse = (id: string) => {
  const newSet = new Set(collapsedNodes.value);
  if (newSet.has(id)) newSet.delete(id);
  else newSet.add(id);
  collapsedNodes.value = newSet;
};

const handleParallelMouseEnter = (id: string) => {
  hoveredParallel.value = id;
};

const handleParallelMouseLeave = (e: MouseEvent) => {
  const relatedTarget = e.relatedTarget as Element | null;
  if (relatedTarget && "closest" in relatedTarget) {
    const closestParallel = relatedTarget.closest("[data-parallel-id]");
    if (closestParallel)
      hoveredParallel.value = closestParallel.getAttribute("data-parallel-id");
    else hoveredParallel.value = null;
  } else {
    hoveredParallel.value = null;
  }
};

const stopPropagation = (e: Event) => {
  e.stopPropagation();
  if ("nativeEvent" in e) {
    (e as any).nativeEvent.stopImmediatePropagation();
  }
};

const {
  handleShowIterationResultList,
  handleShowLoopResultList,
  handleShowRetryResultList,
  handleShowAgentOrToolLog,
} = useLogs();
</script>

<style scoped>
.tracing-panel {
  padding: 0.5rem 0;
}

.parallel-node {
  position: relative;
  margin-bottom: 0.5rem;
  margin-left: 1rem;
}

.parallel-header {
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
}

.parallel-title {
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  color: #354052;
}

.toggle-button {
  margin-right: 0.5rem;
  transition: color 0.2s;
}

.toggle-button-hovered {
  border-radius: 0.25rem;
  border: 1px solid #1018280a;
  background-color: #155aef;
  color: #ffffff;
}

.toggle-button-normal {
  color: #354052;
}

.toggle-button-normal:hover {
  color: #101828;
}

.icon-small {
  height: 0.75rem;
  width: 0.75rem;
}

.icon-tertiary {
  color: #676f83;
}

.divider {
  margin: 0 0.5rem;
  height: 1px;
  flex-grow: 1;
  background: linear-gradient(
    to right,
    rgba(16, 24, 40, 0.08),
    rgba(255, 255, 255, 0)
  );
}

.parallel-content {
  position: relative;
  padding-left: 0.5rem;
}

.hidden {
  display: none;
}

.vertical-line {
  position: absolute;
  bottom: 0;
  left: 5px;
  top: 0;
  width: 2px;
}

.vertical-line-hovered {
  background-color: #296dff;
}

.vertical-line-normal {
  background-color: #1018280a;
}

.branch-title {
  font-size: 0.625rem;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: -0.375rem;
  padding-left: 1rem;
}

.text-tertiary {
  color: #676f83;
}

.text-quaternary {
  color: #1018284d;
}
</style>
