import { Meta, StoryObj } from '@storybook/vue3';
import MetricTrend from './index.vue';
import { MsgDataType } from '../../../common/type';

const meta: Meta<typeof MetricTrend> = {
  component: MetricTrend,
  title: 'Components/ChatMsg/MetricTrend',
  tags: ['autodocs'],
  argTypes: {
    question: {
      control: 'text',
      description: '趋势图问题'
    },
    loading: {
      control: 'boolean',
      description: '加载状态'
    },
    chartIndex: {
      control: 'number',
      description: '图表索引'
    },
    triggerResize: {
      control: 'boolean',
      description: '触发图表重绘'
    },
    currentDateOption: {
      control: 'number',
      description: '当前日期选项'
    },
    onApplyAuth: {
      action: 'onApplyAuth',
      description: '申请权限回调函数'
    }
  },
  parameters: {
    docs: {
      description: {
        component: '指标趋势图组件，用于展示指标随时间的变化趋势'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof MetricTrend>;

// 基础趋势图样例
export const Basic: Story = {
  args: {
    question: '2023年销售趋势',
    loading: false,
    chartIndex: 0,
    triggerResize: false,
    currentDateOption: 7,
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          name: '日期',
          bizName: 'date',
          type: 'DATE',
          showType: 'DATE',
          authorized: true
        },
        {
          name: '销售额',
          bizName: 'sales',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: true
        }
      ],
      queryResults: [
        { date: '2023-01-01', sales: '15689234.56' },
        { date: '2023-01-02', sales: '16892345.67' },
        { date: '2023-01-03', sales: '18923456.78' },
        { date: '2023-01-04', sales: '17892345.89' },
        { date: '2023-01-05', sales: '19823456.90' }
      ],
      aggregateInfo: {
        metricInfos: [
          {
            date: '2023-01-05',
            value: '19823456.90',
            statistics: {
              '环比': '+10.5%',
              '同比': '+8.3%'
            }
          }
        ]
      },
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      },
      chatContext: {
        dateOptions: [
          { label: '最近7天', value: 7 },
          { label: '最近30天', value: 30 },
          { label: '最近90天', value: 90 }
        ]
      }
    }
  }
};

// 多指标趋势图
export const MultiMetrics: Story = {
  args: {
    question: '销售和利润趋势',
    loading: false,
    chartIndex: 0,
    triggerResize: false,
    currentDateOption: 7,
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          name: '日期',
          bizName: 'date',
          type: 'DATE',
          showType: 'DATE',
          authorized: true
        },
        {
          name: '销售额',
          bizName: 'sales',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: true
        },
        {
          name: '利润',
          bizName: 'profit',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: true
        }
      ],
      queryResults: [
        { date: '2023-01-01', sales: '15689234.56', profit: '2345678.90' },
        { date: '2023-01-02', sales: '16892345.67', profit: '2456789.01' },
        { date: '2023-01-03', sales: '18923456.78', profit: '2567890.12' },
        { date: '2023-01-04', sales: '17892345.89', profit: '2678901.23' },
        { date: '2023-01-05', sales: '19823456.90', profit: '2789012.34' }
      ],
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      },
      chatContext: {
        dateOptions: [
          { label: '最近7天', value: 7 },
          { label: '最近30天', value: 30 },
          { label: '最近90天', value: 90 }
        ]
      }
    }
  }
};

// 加载中状态
export const Loading: Story = {
  args: {
    question: '销售趋势',
    loading: true,
    chartIndex: 0,
    triggerResize: false,
    currentDateOption: 7,
    data: {
      queryMode: 'SQL',
      queryColumns: [],
      queryResults: [],
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    }
  }
};

// 需要权限认证
export const NeedAuthorization: Story = {
  args: {
    question: '销售趋势',
    loading: false,
    chartIndex: 0,
    triggerResize: false,
    currentDateOption: 7,
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          name: '销售额',
          bizName: 'sales',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: false
        }
      ],
      queryResults: [],
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    },
    onApplyAuth: (model: string) => console.log('申请权限:', model)
  }
}; 