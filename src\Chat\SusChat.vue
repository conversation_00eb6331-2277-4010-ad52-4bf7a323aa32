<template>
  <div class="chatBody">
    <div class="chatContent">
      <!-- 聊天头部：显示当前智能体信息和模式切换 -->
      <div v-if="currentAgent" class="chatHeader">
        <a-row style="width: 100%">
          <a-col flex="1 1 200px">
            <a-space>
              <div class="chatHeaderTitle">
                {{ currentClientAgent.chartName }}
              </div>
              <div class="chatHeaderTip">
                {{ currentClientAgent.description }}
              </div>
              <a-tooltip title="精简模式下，问答结果将以文本形式输出">
                <a-switch
                  :key="currentAgent.id"
                  style="position: relative; top: -1px"
                  size="small"
                  v-model:checked="isSimpleMode"
                  checked-children="精简模式"
                  un-checked-children="精简模式"
                />
              </a-tooltip>
            </a-space>
          </a-col>
          <a-col flex="0 1 118px"></a-col>
        </a-row>
      </div>
      <!-- 消息容器：显示聊天记录 -->
      <MessageContainer
        id="messageContainer"
        :isSimpleMode="isSimpleMode"
        :isDebugMode="isDebugMode"
        :messageList="messageList"
        :chatId="currentConversation?.chatId"
        :historyVisible="historyVisible"
        :currentAgent="currentAgent"
        :chatVisible="chatVisible"
        :isDeveloper="isDeveloper"
        :integrateSystem="integrateSystem"
        @msgDataLoaded="onMsgDataLoaded"
        @sendMsg="onSendMsg"
      />
      <!-- 聊天底部：输入框和功能按钮 -->
      <ChatFooter
        ref="chatFooterRef"
        v-model:inputMsg="inputMsg"
        :chatId="currentConversation?.chatId"
        :currentAgent="currentAgent"
        @toggleHistoryVisible="onToggleHistoryVisible"
        @sendMsg="sendMsg"
        @addConversation="onAddConversation"
        @openAgents="onOpenAgents"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from "vue";
import { updateMessageContainerScroll, uuid, jsonParse } from "../utils/utils";
import {
  Row as ARow,
  Col as ACol,
  Space as ASpace,
  Switch as ASwitch,
  Tooltip as ATooltip,
} from "ant-design-vue";

import MessageContainer from "./MessageContainer/index.vue";
import ChatFooter from "./ChatFooter/index.vue";
import Conversation from "./Conversation/index.vue";

import classNames from "classnames";

import { cloneDeep, isBoolean, throttle } from "lodash";

import {
  ConversationDetailType,
  MessageItem,
  MessageTypeEnum,
  AgentType,
} from "./type";

import {
  HistoryMsgItemType,
  MsgDataType,
  SendMsgParamsType,
} from "../common/type";
import { getHistoryMsg } from "../service";

import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

import type { ClientAgentType } from "@/types/client";

// 设置日期本地化
dayjs.locale("zh-cn");

const emit = defineEmits([
  "currentAgentChange",
  "reportMsgEvent",
  "openAgents",
  "toggleHistoryVisible",
  "addConversation",
]);

const props = defineProps<{
  chatVisible: boolean;
  isDeveloper: boolean;
  integrateSystem: string;
  isCopilot: boolean;
  isPublic: boolean;
  // currentAgent: ClientAgentType;
  currentClientAgent: ClientAgentType;
}>();

// 状态定义
const messageList = ref<MessageItem[]>([]);
const inputMsg = ref("");
const hasNextPage = ref(false);
const historyInited = ref(false);
const currentConversation = ref<ConversationDetailType | undefined>(undefined);
const historyVisible = ref(false);

const isSimpleMode = ref<boolean>(false);
const isDebugMode = ref<boolean>(true);
const conversationRef = ref();
const chatFooterRef = ref();

const currentAgent = computed(() => {
  const chatbiConfig = props.currentClientAgent.chatbiConfig;

  if (chatbiConfig) {
    console.log(JSON.parse(chatbiConfig as unknown as string));
    return JSON.parse(chatbiConfig as unknown as string);
  } else {
    return {};
  }
});

const updateAgentConfigMode = (agent: AgentType) => {
  const toolConfig = jsonParse(agent?.toolConfig, {});
  const { simpleMode, debugMode } = toolConfig;
  isSimpleMode.value = isBoolean(simpleMode) ? simpleMode : false;
  isDebugMode.value = isBoolean(debugMode) ? debugMode : true;
};

watch(
  currentAgent,
  agent => {
    if (agent.value) updateAgentConfigMode(agent.value);
  },
  { immediate: true }
);

const inputFocus = () => {
  chatFooterRef.value?.inputFocus();
};
const inputBlur = () => {
  chatFooterRef.value?.inputBlur();
};

const onSendMsg = async (
  msg?: string,
  list?: MessageItem[],
  modelId?: number,
  sendMsgParams?: SendMsgParamsType
) => {
  const currentMsg = msg || inputMsg.value;
  if (currentMsg.trim() === "") {
    inputMsg.value = "";
    return;
  }
  const msgs = [
    ...(list || messageList.value),
    {
      id: uuid(),
      msg: currentMsg,
      msgValue: currentMsg,
      modelId: modelId === -1 ? undefined : modelId,
      agentId: currentAgent.value.id,
      type: MessageTypeEnum.QUESTION,
      filters: sendMsgParams?.filters,
    },
  ];
  messageList.value = msgs;
  updateMessageContainerScroll();
  inputMsg.value = "";
};

const sendMsg = (msg: string, modelId?: number) => {
  onSendMsg(msg, messageList.value, modelId);
  inputBlur();
};

const onMsgDataLoaded = (
  data: MsgDataType,
  questionId: string | number,
  question: string,
  valid: boolean,
  isRefresh?: boolean
) => {
  emit("reportMsgEvent", question, valid);
  conversationRef.value?.updateData(currentAgent.value.id);
  if (!data) return;
  const msgs = cloneDeep(messageList.value);
  const msg = msgs.find(item => item.id === questionId);
  if (msg) {
    msg.msgData = data;
    messageList.value = msgs;
  }
  if (!isRefresh) {
    updateMessageContainerScroll(`${questionId}`);
  }
};

const onToggleHistoryVisible = () => {
  emit("toggleHistoryVisible");
};

const onOpenAgents = () => {
  emit("openAgents");
};

const onAddConversation = () => {
  emit("addConversation");
  inputFocus();
};

const onSelectAgent = () => {};

const onSelectConversation = (
  conversation: ConversationDetailType,
  sendMsgParams?: SendMsgParamsType,
  isAdd?: boolean
) => {
  currentConversation.value = {
    ...conversation,
    initialMsgParams: sendMsgParams,
    isAdd,
  };
  if (conversation) {
    if (conversation.chatId !== -1) {
      localStorage.setItem("CONVERSATION_ID", `${conversation.chatId}`);
    }
  } else {
    localStorage.removeItem("CONVERSATION_ID");
  }
};

onMounted(() => {});

watch(
  () => props.chatVisible,
  val => {
    if (val) {
      nextTick(() => {
        inputFocus();
        updateMessageContainerScroll();
      });
    }
  }
);

watch(
  () => historyInited.value,
  val => {
    if (val) {
      nextTick(() => {
        const messageContainerEle = document.getElementById("messageContainer");
        messageContainerEle?.addEventListener(
          "scroll",
          throttle((e: any) => {
            if (e.target.scrollTop === 0 && hasNextPage.value) {
              // updateHistoryMsg(pageNo.value + 1); // 如需分页加载历史消息可解开
              // pageNo.value = pageNo.value + 1;
            }
          }, 200)
        );
      });
    }
  }
);

onUnmounted(() => {
  const messageContainerEle = document.getElementById("messageContainer");
  messageContainerEle?.removeEventListener("scroll", () => {});
});

// 暴露方法
defineExpose({
  // 可按需暴露方法
});
</script>

<style lang="less" scoped>
@import "./index.less";
</style>
