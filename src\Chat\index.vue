<template>
  <div :class="chatClass">
    <div class="chatSection">
      <!-- 左侧智能体列表，仅在非移动端且有多于一个智能体时显示 -->
      <AgentList
        v-if="agentListVisible"
        :agentList="agentList"
        :currentAgent="currentAgent"
        @selectAgent="onSelectAgent"
      />
      <!-- <AgentList
        v-if="!isMobile && agentList.length > 1 && agentListVisible"
        :agentList="agentList"
        :currentAgent="currentAgent"
        @selectAgent="onSelectAgent"
      /> -->

      <!-- <DifyChat
          ref="difyChatRef"
          :chartId="currentAgent?.chartId"
          :currentAgent="currentAgent"
          :conversationId="difyConversationId"  
          @toggleHistoryVisible="onToggleHistoryVisible"
          @openAgents="onOpenAgents"
          @update:conversationId="onUpdateConversationId"
          :isPublic="isPublic"
        /> -->

      <div class="chatApp" v-if="currentAgent?.chartType === 'chatBI-chat'">
        <SusChat
          ref="susChatRef"
          :currentClientAgent="currentAgent"
          :isDeveloper="isDeveloper"
          @openAgents="onOpenAgents"
          @toggleHistoryVisible="onToggleHistoryVisible"
          @addConversation="onAddConversation"
        />

        <!--        <div v-if="currentConversation" class="chatBody">-->
        <!--          <div class="chatContent">-->
        <!--            &lt;!&ndash; 聊天头部：显示当前智能体信息和模式切换 &ndash;&gt;-->
        <!--            &lt;!&ndash; v-if="currentAgent && !isMobile && !noInput" &ndash;&gt;-->
        <!--            <div v-if="currentAgent && !noInput" class="chatHeader">-->
        <!--              <a-row style="width: 100%">-->
        <!--                <a-col flex="1 1 200px">-->
        <!--                  <a-space>-->
        <!--                    <div class="chatHeaderTitle">{{ currentAgent.name }}</div>-->
        <!--                    <div class="chatHeaderTip">-->
        <!--                      {{ currentAgent.description }}-->
        <!--                    </div>-->
        <!--                    <a-tooltip title="精简模式下，问答结果将以文本形式输出">-->
        <!--                      <a-switch-->
        <!--                        :key="currentAgent.id"-->
        <!--                        style="position: relative; top: -1px"-->
        <!--                        size="small"-->
        <!--                        v-model:checked="isSimpleMode"-->
        <!--                        checked-children="精简模式"-->
        <!--                        un-checked-children="精简模式"-->
        <!--                      />-->
        <!--                    </a-tooltip>-->
        <!--                  </a-space>-->
        <!--                </a-col>-->
        <!--                <a-col flex="0 1 118px"></a-col>-->
        <!--              </a-row>-->
        <!--            </div>-->
        <!--            &lt;!&ndash; 消息容器：显示聊天记录 &ndash;&gt;-->
        <!--            <MessageContainer-->
        <!--              id="messageContainer"-->
        <!--              :isSimpleMode="isSimpleMode"-->
        <!--              :isDebugMode="isDebugMode"-->
        <!--              :messageList="messageList"-->
        <!--              :chatId="currentConversation?.chatId"-->
        <!--              :historyVisible="historyVisible"-->
        <!--              :currentAgent="currentAgent"-->
        <!--              :chatVisible="chatVisible"-->
        <!--              :isDeveloper="isDeveloper"-->
        <!--              :integrateSystem="integrateSystem"-->
        <!--              @msgDataLoaded="onMsgDataLoaded"-->
        <!--              @sendMsg="onSendMsg"-->
        <!--            />-->
        <!--            &lt;!&ndash; 聊天底部：输入框和功能按钮 &ndash;&gt;-->
        <!--            <ChatFooter-->
        <!--              v-if="!noInput"-->
        <!--              ref="chatFooterRef"-->
        <!--              v-model:inputMsg="inputMsg"-->
        <!--              :chatId="currentConversation?.chatId"-->
        <!--              :agentList="agentList"-->
        <!--              :currentAgent="currentAgent"-->
        <!--              @toggleHistoryVisible="onToggleHistoryVisible"-->
        <!--              @sendMsg="sendMsg"-->
        <!--              @addConversation="onAddConversation"-->
        <!--              @selectAgent="onSelectAgent"-->
        <!--              @openAgents="onOpenAgents"-->
        <!--              @openShowcase="-->
        <!--                () => {-->
        <!--                  showCaseVisible = !showCaseVisible;-->
        <!--                }-->
        <!--              "-->
        <!--            />-->
        <!--          </div>-->
        <!--        </div>-->
      </div>

      <div
        class="chatApp"
        v-if="
          currentAgent?.chartType === 'chat' ||
          currentAgent?.chartType === 'advanced-chat'
        "
      >
        <DifyChat
          ref="difyChatRef"
          :chartId="currentAgent?.chartId"
          :currentAgent="currentAgent"
          :conversationId="difyConversationId"
          @toggleHistoryVisible="onToggleHistoryVisible"
          @openAgents="onOpenAgents"
          @update:conversationId="onUpdateConversationId"
          :isPublic="isPublic"
        />
      </div>

      <!-- 右侧会话列表 -->
      <Conversation
        v-if="currentAgent?.chartType === 'chatBI-chat'"
        :currentAgent="currentAgent"
        :currentClientAgent="currentAgent"
        :currentConversation="currentConversation"
        :historyVisible="historyVisible"
        @selectConversation="onSelectConversation"
        @closeConversation="onCloseConversation"
        ref="conversationRef"
      />

      <DifyConversation
        v-if="
          (currentAgent?.chartType === 'chat' ||
            currentAgent?.chartType === 'advanced-chat') &&
          !isPublic
        "
        :currentAgent="currentAgent"
        :currentConversation="currentDifyConversation"
        :historyVisible="historyVisible"
        :currentConversationId="difyConversationId"
        :selectConversation="onDifySelectConversation"
        @closeConversation="onCloseConversation"
        ref="conversationRef"
      />
    </div>
    <!-- 移动端智能体选择器 -->
    <!-- <MobileAgents
      :open="mobileAgentsVisible"
      :agentList="agentList"
      :currentAgent="currentAgent"
      @selectAgent="onSelectAgent"
      @close="
        () => {
          mobileAgentsVisible = false;
        }
      "
    /> -->
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  onMounted,
  onUnmounted,
  nextTick,
  watch,
} from "vue";
import {
  updateMessageContainerScroll,
  uuid,
  setToken,
  jsonParse,
} from "../utils/utils";
import MessageContainer from "./MessageContainer/index.vue";
import Conversation from "./Conversation/index.vue";
import ChatFooter from "./ChatFooter/index.vue";
import { cloneDeep, isBoolean, throttle } from "lodash";
import AgentList from "./AgentList/index.vue";
import MobileAgents from "./MobileAgents/index.vue";
import {
  ConversationDetailType,
  MessageItem,
  MessageTypeEnum,
  AgentType,
} from "./type";
import {
  HistoryMsgItemType,
  MsgDataType,
  SendMsgParamsType,
} from "../common/type";
import { queryAgentList } from "./service";
import { getHistoryMsg } from "../service";
import ShowCase from "../ShowCase/index.vue";
import {
  ConfigProvider,
  Drawer,
  Modal,
  Row,
  Col,
  Space,
  Switch,
  Tooltip,
} from "ant-design-vue";
import classNames from "classnames";
import DifyChat from "@/DifyChat/index.vue";
import DifyConversation from "@/DifyChat/components/Conversation.vue";

import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

import type { ClientAgentType, DifyConversationItem } from "@/types/client";
import { setKKServerUrl, setKKLocalUrl, setISMICRO } from "@/config/config";

import { validateNull } from "@/utils/utils";

import SusChat from "@/Chat/SusChat.vue";

// 判断是否是移动设备
// export const isMobile = window.navigator.userAgent.match(
//   /(iPhone|iPod|Android|ios)/i
// );

// 设置日期本地化
dayjs.locale("zh-cn");

export default defineComponent({
  name: "Chat",
  components: {
    MessageContainer,
    Conversation,
    ChatFooter,
    AgentList,
    MobileAgents,
    ShowCase,
    AConfigProvider: ConfigProvider,
    ADrawer: Drawer,
    AModal: Modal,
    ARow: Row,
    ACol: Col,
    ASpace: Space,
    ASwitch: Switch,
    ATooltip: Tooltip,
    DifyChat,
    DifyConversation,
    SusChat,
  },
  props: {
    token: String, // 认证token
    agentIds: Array, // 智能体ID列表
    initialAgentId: Number, // 初始智能体ID
    chatVisible: Boolean, // 聊天界面是否可见
    noInput: Boolean, // 是否禁用输入
    isDeveloper: Boolean, // 是否是开发者模式
    integrateSystem: String, // 集成系统标识
    isCopilot: Boolean, // 是否是Copilot模式
    isPublic: Boolean, // 是否是公开模式
    kkServerUrl: String, // kkfile服务器URL
    kkLocalUrl: String, // kkfile本地URL
    isMicro: Boolean, // 是否是微服务模式
  },
  emits: ["currentAgentChange", "reportMsgEvent"],
  setup(props, { emit, expose }) {
    // 状态定义
    const messageList = ref<MessageItem[]>([]); // 消息列表
    const inputMsg = ref(""); // 输入框内容
    const pageNo = ref(1); // 当前页码
    const hasNextPage = ref(false); // 是否有下一页
    const historyInited = ref(false); // 历史记录是否已初始化
    const historyVisible = ref(false); // 历史记录是否可见
    const agentList = ref<AgentType[]>([]); // 智能体列表
    const currentAgent = ref<ClientAgentType>(); // 当前选中的智能体
    const mobileAgentsVisible = ref(false); // 移动端智能体列表是否可见
    const agentListVisible = ref(true); // 智能体列表是否可见
    const showCaseVisible = ref(false); // 展示案例是否可见
    const isSimpleMode = ref<boolean>(false); // 是否处于精简模式
    const isDebugMode = ref<boolean>(true); // 是否处于调试模式

    const currentConversation = ref<ConversationDetailType | undefined>(
      undefined
    ); // 当前会话
    const currentDifyConversation = ref<DifyConversationItem | undefined>(
      undefined
    );

    // 组件引用
    const conversationRef = ref();
    const chatFooterRef = ref();
    const difyChatRef = ref();
    const susChatRef = ref();

    // 计算属性：聊天界面类名
    const chatClass = computed(() => {
      return classNames("hr-chat-box", {
        // mobile: isMobile,
        historyVisible: historyVisible.value,
      });
    });

    const difyConversationId = ref<string>("");

    const onUpdateConversationId = (id: string) => {
      difyConversationId.value = id;
    };

    // 发送Copilot消息
    const sendCopilotMsg = (params: SendMsgParamsType) => {
      agentListVisible.value = false;
      const { agentId, msg, modelId } = params;
      if (currentAgent.value?.id !== agentId) {
        messageList.value = [];
        const agent =
          agentList.value.find(item => item.id === agentId) ||
          ({} as AgentType);
        updateCurrentAgent({ ...agent, initialSendMsgParams: params });
      } else {
        onSendMsg(msg, messageList.value, modelId, params);
      }
    };

    // 更新智能体配置模式
    const updateAgentConfigMode = (agent: AgentType) => {
      const toolConfig = jsonParse(agent?.toolConfig, {});
      const { simpleMode, debugMode } = toolConfig;
      if (isBoolean(simpleMode)) {
        isSimpleMode.value = simpleMode;
      } else {
        isSimpleMode.value = false;
      }
      if (isBoolean(debugMode)) {
        isDebugMode.value = debugMode;
      } else {
        isDebugMode.value = true;
      }
    };

    // 更新当前智能体
    const updateCurrentAgent = (agent?: AgentType) => {
      currentAgent.value = agent;
      emit("currentAgentChange", agent);
      localStorage.setItem("AGENT_ID", `${agent?.id}`);
      // TODO 智能体类型区分
      // if (agent) {
      //   updateAgentConfigMode(agent);
      // }
      // if (!props.isCopilot) {
      //   window.history.replaceState(
      //     {},
      //     "",
      //     `${window.location.pathname}?agentId=${agent?.id}`
      //   );
      // }
    };

    // 初始化智能体列表
    const initAgentList = async () => {
      const res = await queryAgentList();
      const agentListValue = res.data || [];
      // .filter(item => {
      //   return (
      //     item.status === 1 &&
      //     (props.agentIds === undefined || props.agentIds.includes(item.id))
      //   );
      // });
      agentList.value = agentListValue;
      if (agentListValue.length > 0) {
        const agentId =
          props.initialAgentId || localStorage.getItem("AGENT_ID");
        if (agentId) {
          const agent = agentListValue.find(item => item.id === +agentId);
          updateCurrentAgent(agent || agentListValue[0]);
        } else {
          updateCurrentAgent(agentListValue[0]);
        }
      }
    };

    // 发送欢迎消息
    const sendHelloRsp = (agent?: AgentType) => {
      if (props.noInput) {
        return;
      }
      messageList.value = [
        {
          id: uuid(),
          type: MessageTypeEnum.AGENT_LIST,
          msg:
            agent?.name ||
            currentAgent.value?.name ||
            agentList.value?.[0]?.name,
        },
      ];
    };

    // 转换历史消息格式
    const convertHistoryMsg = (list: HistoryMsgItemType[]) => {
      return list.map((item: HistoryMsgItemType) => ({
        id: item.questionId,
        questionId: item.questionId,
        type: MessageTypeEnum.QUESTION,
        msg: item.queryText,
        parseInfos: item.parseInfos,
        parseTimeCost: item.parseTimeCost,
        msgData: {
          ...(item.queryResult || {}),
          similarQueries: item.similarQueries,
        },
        score: item.score,
        agentId: currentAgent.value?.id,
      }));
    };

    // 更新历史消息
    const updateHistoryMsg = async (page: number) => {
      const res = await getHistoryMsg(
        page,
        currentConversation.value!.chatId,
        3
      );
      const { hasNextPage: hasNext, list } = res?.data || {
        hasNextPage: false,
        list: [],
      };
      const msgList = [
        ...convertHistoryMsg(list),
        ...(page === 1 ? [] : messageList.value),
      ];
      messageList.value = msgList;
      hasNextPage.value = hasNext;
      if (page === 1) {
        if (list.length === 0) {
          sendHelloRsp();
        }
        updateMessageContainerScroll();
        historyInited.value = true;
        inputFocus();
      } else {
        const msgEle = document.getElementById(`${messageList.value[0]?.id}`);
        msgEle?.scrollIntoView();
      }
    };

    // 处理滚动事件
    const handleScroll = throttle((e: any) => {
      if (e.target.scrollTop === 0 && hasNextPage.value) {
        updateHistoryMsg(pageNo.value + 1);
        pageNo.value = pageNo.value + 1;
      }
    }, 200);

    // 输入框焦点控制
    const inputFocus = () => {
      // if (!isMobile) {
      chatFooterRef.value?.inputFocus();
      // }
    };

    const inputBlur = () => {
      chatFooterRef.value?.inputBlur();
    };

    // 发送消息
    const onSendMsg = async (
      msg?: string,
      list?: MessageItem[],
      modelId?: number,
      sendMsgParams?: SendMsgParamsType
    ) => {
      const currentMsg = msg || inputMsg.value;
      if (currentMsg.trim() === "") {
        inputMsg.value = "";
        return;
      }

      const msgAgent = agentList.value.find(
        item => currentMsg.indexOf(item.name) === 1
      );
      const certainAgent = currentMsg[0] === "/" && msgAgent;
      const agentIdValue = certainAgent ? msgAgent.id : undefined;
      const agent = agentList.value.find(
        item => item.id === sendMsgParams?.agentId
      );

      if (agent || certainAgent) {
        updateCurrentAgent(agent || msgAgent);
      }
      const msgs = [
        ...(list || messageList.value),
        {
          id: uuid(),
          msg: currentMsg,
          msgValue: certainAgent
            ? currentMsg.replace(`/${certainAgent.name}`, "").trim()
            : currentMsg,
          modelId: modelId === -1 ? undefined : modelId,
          agentId: agent?.id || agentIdValue || currentAgent.value?.id,
          type: MessageTypeEnum.QUESTION,
          filters: sendMsgParams?.filters,
        },
      ];
      messageList.value = msgs;
      updateMessageContainerScroll();
      inputMsg.value = "";
    };

    // 输入消息变化处理
    const onInputMsgChange = (value: string) => {
      inputMsg.value = value || "";
    };

    // 保存会话到本地存储
    const saveConversationToLocal = (conversation: ConversationDetailType) => {
      if (conversation) {
        if (conversation.chatId !== -1) {
          localStorage.setItem("CONVERSATION_ID", `${conversation.chatId}`);
        }
      } else {
        localStorage.removeItem("CONVERSATION_ID");
      }
    };

    // 选择会话
    const onSelectConversation = (
      conversation: ConversationDetailType,
      sendMsgParams?: SendMsgParamsType,
      isAdd?: boolean
    ) => {
      currentConversation.value = {
        ...conversation,
        initialMsgParams: sendMsgParams,
        isAdd,
      };
      saveConversationToLocal(conversation);
    };

    // dify选择会话
    const onDifySelectConversation = (conversation: any) => {
      // console.log("onDifySelectConversation", conversation);
      currentDifyConversation.value = {
        ...conversation,
      };

      difyConversationId.value = conversation.id;
      difyChatRef.value?.updateConversation(conversation.id);
    };

    // 消息数据加载完成处理
    const onMsgDataLoaded = (
      data: MsgDataType,
      questionId: string | number,
      question: string,
      valid: boolean,
      isRefresh?: boolean
    ) => {
      emit("reportMsgEvent", question, valid);
      // if (!isMobile) {
      conversationRef.value?.updateData(currentAgent.value?.id);
      // }
      if (!data) {
        return;
      }
      const msgs = cloneDeep(messageList.value);
      const msg = msgs.find(item => item.id === questionId);
      if (msg) {
        msg.msgData = data;
        messageList.value = msgs;
      }
      if (!isRefresh) {
        updateMessageContainerScroll(`${questionId}`);
      }
    };

    // 切换历史记录可见性
    const onToggleHistoryVisible = () => {
      historyVisible.value = !historyVisible.value;
    };

    const onOpenAgents = () => {
      agentListVisible.value = !agentListVisible.value;
    };

    // 添加新会话
    const onAddConversation = () => {
      conversationRef.value?.onAddConversation();
      inputFocus();
    };

    // 选择智能体
    const onSelectAgent = (agent: AgentType) => {
      if (agent.chartId === currentAgent.value?.chartId) {
        return;
      }
      if (
        messageList.value.length === 1 &&
        messageList.value[0].type === MessageTypeEnum.AGENT_LIST
      ) {
        messageList.value = [];
      }
      updateCurrentAgent(agent);

      difyChatRef.value?.startNewConversation();
      difyConversationId.value = "";

      updateMessageContainerScroll();
    };

    // 添加新会话
    const onAddDifyConversation = () => {
      difyChatRef.value?.startNewConversation();
      difyConversationId.value = "";
    };

    // 发送消息的包装方法
    const sendMsg = (msg: string, modelId?: number) => {
      onSendMsg(msg, messageList.value, modelId);
      // if (isMobile) {
      inputBlur();
      // }
    };

    // 关闭会话
    const onCloseConversation = () => {
      historyVisible.value = false;
    };

    // 生命周期钩子
    onMounted(() => {
      if (props.token) {
        setToken(props.token);
      }
      if (props.kkServerUrl) {
        setKKServerUrl(props.kkServerUrl);
      }
      if (props.kkLocalUrl) {
        setKKLocalUrl(props.kkLocalUrl);
      }
      if (!validateNull(props.isMicro)) {
        setISMICRO(props.isMicro);
      }
      initAgentList();
    });

    // 监听聊天界面可见性变化
    watch(
      () => props.chatVisible,
      val => {
        if (val) {
          nextTick(() => {
            inputFocus();
            updateMessageContainerScroll();
          });
        }
      }
    );

    // 监听当前会话变化
    watch(
      () => currentConversation.value,
      val => {
        if (!val) {
          return;
        }
        const { initialMsgParams, isAdd } = val;
        if (isAdd) {
          inputFocus();
          if (initialMsgParams) {
            onSendMsg(
              initialMsgParams.msg,
              [],
              initialMsgParams.modelId,
              initialMsgParams
            );
            return;
          }
          sendHelloRsp();
          return;
        }
        // TODO 区分agent类型
        // updateHistoryMsg(1);
        // pageNo.value = 1;
      }
    );

    // 监听当前会话变化
    watch(
      () => currentDifyConversation.value,
      val => {
        if (!val) {
          return;
        }
        const { initialMsgParams, isAdd } = val;
        if (isAdd) {
          inputFocus();
          if (initialMsgParams) {
            onSendMsg(
              initialMsgParams.msg,
              [],
              initialMsgParams.modelId,
              initialMsgParams
            );
            return;
          }
          sendHelloRsp();
          return;
        }
        // TODO 区分agent类型
        // updateHistoryMsg(1);
        // pageNo.value = 1;
      }
    );

    // 监听历史记录初始化状态
    watch(
      () => historyInited.value,
      val => {
        if (val) {
          nextTick(() => {
            const messageContainerEle =
              document.getElementById("messageContainer");
            messageContainerEle?.addEventListener("scroll", handleScroll);
          });
        }
      }
    );

    // 组件卸载时清理事件监听
    onUnmounted(() => {
      const messageContainerEle = document.getElementById("messageContainer");
      messageContainerEle?.removeEventListener("scroll", handleScroll);
    });

    // 暴露方法给父组件
    expose({
      sendCopilotMsg,
    });

    return {
      messageList,
      inputMsg,
      pageNo,
      hasNextPage,
      historyInited,
      currentConversation,
      currentDifyConversation,
      historyVisible,
      agentList,
      currentAgent,
      mobileAgentsVisible,
      agentListVisible,
      showCaseVisible,
      isSimpleMode,
      isDebugMode,
      conversationRef,
      chatFooterRef,
      difyChatRef,
      chatClass,
      onSendMsg,
      onInputMsgChange,
      onSelectConversation,
      onMsgDataLoaded,
      onToggleHistoryVisible,
      onOpenAgents,
      onAddConversation,
      onSelectAgent,
      sendMsg,
      onCloseConversation,
      onDifySelectConversation,
      onUpdateConversationId,
      // isMobile,
      difyConversationId,
      onAddDifyConversation,
      susChatRef,
    };
  },
});
</script>

<style lang="less" scoped>
@import "./index.less";
</style>
