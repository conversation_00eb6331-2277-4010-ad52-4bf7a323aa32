<template>
  <div :class="chatClass">
    <div class="chatSection">
      <!-- 左侧智能体列表，仅在非移动端且有多于一个智能体时显示 -->
      <AgentList
        v-if="agentListVisible"
        :agentList="agentList"
        :currentAgent="currentAgent"
        @selectAgent="onSelectAgent"
      />


      <div class="chatApp" v-if="currentAgent?.chartType === 'chatBI-chat'">
        <SusChat
          ref="susChatRef"
          :currentClientAgent="currentAgent"
          :isDeveloper="isDeveloper"
          :chatVisible="chatVisible"
          :integrateSystem="integrateSystem"
          :historyVisible="historyVisible"
          @openAgents="onOpenAgents"
          @toggleHistoryVisible="onToggleHistoryVisible"
          @addConversation="onAddConversation"
        />


      </div>

      <div
        class="chatApp"
        v-if="
          currentAgent?.chartType === 'chat' ||
          currentAgent?.chartType === 'advanced-chat'
        "
      >
        <DifyChat
          ref="difyChatRef"
          :chartId="currentAgent?.chartId"
          :currentAgent="currentAgent"
          :conversationId="difyConversationId"
          @toggleHistoryVisible="onToggleHistoryVisible"
          @openAgents="onOpenAgents"
          @update:conversationId="onUpdateConversationId"
          :isPublic="isPublic"
        />
      </div>

      <!-- 右侧会话列表 -->
      <Conversation
        v-if="currentAgent?.chartType === 'chatBI-chat'"
        :currentAgent="currentAgent"
        :currentClientAgent="currentAgent"
        :currentConversation="currentConversation"
        :historyVisible="historyVisible"
        @selectConversation="onSelectConversation"
        @closeConversation="onCloseConversation"
        ref="conversationRef"
      />

      <DifyConversation
        v-if="
          (currentAgent?.chartType === 'chat' ||
            currentAgent?.chartType === 'advanced-chat') &&
          !isPublic
        "
        :currentAgent="currentAgent"
        :currentConversation="currentDifyConversation"
        :historyVisible="historyVisible"
        :currentConversationId="difyConversationId"
        :selectConversation="onDifySelectConversation"
        @closeConversation="onCloseConversation"
        ref="conversationRef"
      />
    </div>

  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  onMounted,
  onUnmounted,
  nextTick,
  watch,
} from "vue";
import {
  updateMessageContainerScroll,
  uuid,
  setToken,
} from "../utils/utils";
import Conversation from "./Conversation/index.vue";
import { cloneDeep, throttle } from "lodash";
import AgentList from "./AgentList/index.vue";
import {
  ConversationDetailType,
  MessageItem,
  MessageTypeEnum,
  AgentType,
} from "./type";
import {
  HistoryMsgItemType,
  MsgDataType,
  SendMsgParamsType,
} from "../common/type";
import { queryAgentList } from "./service";
import { getHistoryMsg } from "../service";

import classNames from "classnames";
import DifyChat from "@/DifyChat/index.vue";
import DifyConversation from "@/DifyChat/components/Conversation.vue";

import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

import type { ClientAgentType, DifyConversationItem } from "@/types/client";
import { setKKServerUrl, setKKLocalUrl, setISMICRO } from "@/config/config";

import { validateNull } from "@/utils/utils";

import SusChat from "@/Chat/SusChat.vue";

// 判断是否是移动设备
// export const isMobile = window.navigator.userAgent.match(
//   /(iPhone|iPod|Android|ios)/i
// );

// 设置日期本地化
dayjs.locale("zh-cn");

export default defineComponent({
  name: "Chat",
  components: {
    Conversation,
    AgentList,
    DifyChat,
    DifyConversation,
    SusChat,
  },
  props: {
    token: String, // 认证token
    agentIds: Array, // 智能体ID列表
    initialAgentId: Number, // 初始智能体ID
    chatVisible: Boolean, // 聊天界面是否可见
    noInput: Boolean, // 是否禁用输入
    isDeveloper: Boolean, // 是否是开发者模式
    integrateSystem: String, // 集成系统标识
    isCopilot: Boolean, // 是否是Copilot模式
    isPublic: Boolean, // 是否是公开模式
    kkServerUrl: String, // kkfile服务器URL
    kkLocalUrl: String, // kkfile本地URL
    isMicro: Boolean, // 是否是微服务模式
  },
  emits: ["currentAgentChange", "reportMsgEvent"],
  setup(props, { emit, expose }) {
    // 状态定义
    const historyVisible = ref(false); // 历史记录是否可见
    const agentList = ref<AgentType[]>([]); // 智能体列表
    const currentAgent = ref<ClientAgentType>(); // 当前选中的智能体
    const agentListVisible = ref(true); // 智能体列表是否可见

    // 为了兼容性保留的状态（主要用于 Copilot 和其他功能）
    const messageList = ref<MessageItem[]>([]); // 消息列表
    const inputMsg = ref(""); // 输入框内容
    const pageNo = ref(1); // 当前页码
    const hasNextPage = ref(false); // 是否有下一页
    const historyInited = ref(false); // 历史记录是否已初始化

    const currentConversation = ref<ConversationDetailType | undefined>(
      undefined
    ); // 当前会话
    const currentDifyConversation = ref<DifyConversationItem | undefined>(
      undefined
    );

    // 组件引用
    const conversationRef = ref();
    const difyChatRef = ref();
    const susChatRef = ref();

    // 计算属性：聊天界面类名
    const chatClass = computed(() => {
      return classNames("hr-chat-box", {
        // mobile: isMobile,
        historyVisible: historyVisible.value,
      });
    });

    const difyConversationId = ref<string>("");

    const onUpdateConversationId = (id: string) => {
      difyConversationId.value = id;
    };

    // 发送Copilot消息
    const sendCopilotMsg = (params: SendMsgParamsType) => {
      agentListVisible.value = false;
      const { agentId, msg, modelId } = params;
      // 由于 ClientAgentType 没有 id 属性，这里需要从 agentList 中查找对应的 agent
      const agent = agentList.value.find(item => item.id === agentId);
      if (!agent || (agent && agent.id !== agentId)) {
        messageList.value = [];
        if (agent) {
          updateCurrentAgent({ ...agent, initialSendMsgParams: params });
        }
      } else {
        onSendMsg(msg, messageList.value, modelId, params);
      }
    };



    // 更新当前智能体
    const updateCurrentAgent = (agent?: AgentType) => {
      // 将 AgentType 转换为 ClientAgentType
      if (agent) {
        const clientAgent: ClientAgentType = {
          ...agent,
          chartType: (agent.chartType || 'chatBI-chat') as any,
        };
        currentAgent.value = clientAgent;
        emit("currentAgentChange", clientAgent);
      } else {
        currentAgent.value = undefined;
        emit("currentAgentChange", undefined);
      }
      localStorage.setItem("AGENT_ID", `${agent?.id}`);
    };

    // 初始化智能体列表
    const initAgentList = async () => {
      const res = await queryAgentList();
      const agentListValue = res.data || [];
      // .filter(item => {
      //   return (
      //     item.status === 1 &&
      //     (props.agentIds === undefined || props.agentIds.includes(item.id))
      //   );
      // });
      agentList.value = agentListValue;
      if (agentListValue.length > 0) {
        const agentId =
          props.initialAgentId || localStorage.getItem("AGENT_ID");
        if (agentId) {
          const agent = agentListValue.find(item => item.id === +agentId);
          updateCurrentAgent(agent || agentListValue[0]);
        } else {
          updateCurrentAgent(agentListValue[0]);
        }
      }
    };

    // 发送欢迎消息
    const sendHelloRsp = (agent?: AgentType) => {
      if (props.noInput) {
        return;
      }
      messageList.value = [
        {
          id: uuid(),
          type: MessageTypeEnum.AGENT_LIST,
          msg:
            agent?.name ||
            currentAgent.value?.chartName ||
            agentList.value?.[0]?.name,
        },
      ];
    };

    // 转换历史消息格式
    const convertHistoryMsg = (list: HistoryMsgItemType[]) => {
      // 从 agentList 中找到对应的 agent 来获取 id
      const agentId = agentList.value.find(agent =>
        agent.chartId === currentAgent.value?.chartId
      )?.id;

      return list.map((item: HistoryMsgItemType) => ({
        id: item.questionId,
        questionId: item.questionId,
        type: MessageTypeEnum.QUESTION,
        msg: item.queryText,
        parseInfos: item.parseInfos,
        parseTimeCost: item.parseTimeCost,
        msgData: {
          ...(item.queryResult || {}),
          similarQueries: item.similarQueries,
        },
        score: item.score,
        agentId: agentId,
      }));
    };

    // 更新历史消息
    const updateHistoryMsg = async (page: number) => {
      const res = await getHistoryMsg(
        page,
        currentConversation.value!.chatId,
        3
      );
      const { hasNextPage: hasNext, list } = res?.data || {
        hasNextPage: false,
        list: [],
      };
      const msgList = [
        ...convertHistoryMsg(list),
        ...(page === 1 ? [] : messageList.value),
      ];
      messageList.value = msgList;
      hasNextPage.value = hasNext;
      if (page === 1) {
        if (list.length === 0) {
          sendHelloRsp();
        }
        updateMessageContainerScroll();
        historyInited.value = true;
      } else {
        const msgEle = document.getElementById(`${messageList.value[0]?.id}`);
        msgEle?.scrollIntoView();
      }
    };

    // 处理滚动事件
    const handleScroll = throttle((e: any) => {
      if (e.target.scrollTop === 0 && hasNextPage.value) {
        updateHistoryMsg(pageNo.value + 1);
        pageNo.value = pageNo.value + 1;
      }
    }, 200);

    // 输入框焦点控制


    // 发送消息
    const onSendMsg = async (
      msg?: string,
      list?: MessageItem[],
      modelId?: number,
      sendMsgParams?: SendMsgParamsType
    ) => {
      const currentMsg = msg || inputMsg.value;
      if (currentMsg.trim() === "") {
        inputMsg.value = "";
        return;
      }

      const msgAgent = agentList.value.find(
        item => currentMsg.indexOf(item.name) === 1
      );
      const certainAgent = currentMsg[0] === "/" && msgAgent;
      const agentIdValue = certainAgent ? msgAgent.id : undefined;
      const agent = agentList.value.find(
        item => item.id === sendMsgParams?.agentId
      );

      if (agent || certainAgent) {
        updateCurrentAgent(agent || msgAgent);
      }
      // 获取当前 agent 的 id
      const currentAgentId = agentList.value.find(a =>
        a.chartId === currentAgent.value?.chartId
      )?.id;

      const msgs = [
        ...(list || messageList.value),
        {
          id: uuid(),
          msg: currentMsg,
          msgValue: certainAgent
            ? currentMsg.replace(`/${certainAgent.name}`, "").trim()
            : currentMsg,
          modelId: modelId === -1 ? undefined : modelId,
          agentId: agent?.id || agentIdValue || currentAgentId,
          type: MessageTypeEnum.QUESTION,
          filters: sendMsgParams?.filters,
        },
      ];
      messageList.value = msgs;
      updateMessageContainerScroll();
      inputMsg.value = "";
    };



    // 保存会话到本地存储
    const saveConversationToLocal = (conversation: ConversationDetailType) => {
      if (conversation) {
        if (conversation.chatId !== -1) {
          localStorage.setItem("CONVERSATION_ID", `${conversation.chatId}`);
        }
      } else {
        localStorage.removeItem("CONVERSATION_ID");
      }
    };

    // 选择会话
    const onSelectConversation = (
      conversation: ConversationDetailType,
      sendMsgParams?: SendMsgParamsType,
      isAdd?: boolean
    ) => {
      currentConversation.value = {
        ...conversation,
        initialMsgParams: sendMsgParams,
        isAdd,
      };
      saveConversationToLocal(conversation);
    };

    // dify选择会话
    const onDifySelectConversation = (conversation: any) => {
      // console.log("onDifySelectConversation", conversation);
      currentDifyConversation.value = {
        ...conversation,
      };

      difyConversationId.value = conversation.id;
      difyChatRef.value?.updateConversation(conversation.id);
    };

    // 消息数据加载完成处理
    const onMsgDataLoaded = (
      data: MsgDataType,
      questionId: string | number,
      question: string,
      valid: boolean,
      isRefresh?: boolean
    ) => {
      emit("reportMsgEvent", question, valid);
      // 获取当前 agent 的 id
      const currentAgentId = agentList.value.find(a =>
        a.chartId === currentAgent.value?.chartId
      )?.id;
      conversationRef.value?.updateData(currentAgentId);

      if (!data) {
        return;
      }
      const msgs = cloneDeep(messageList.value);
      const msg = msgs.find(item => item.id === questionId);
      if (msg) {
        msg.msgData = data;
        messageList.value = msgs;
      }
      if (!isRefresh) {
        updateMessageContainerScroll(`${questionId}`);
      }
    };

    // 切换历史记录可见性
    const onToggleHistoryVisible = () => {
      historyVisible.value = !historyVisible.value;
    };

    const onOpenAgents = () => {
      agentListVisible.value = !agentListVisible.value;
    };

    // 添加新会话
    const onAddConversation = () => {
      conversationRef.value?.onAddConversation();
    };

    // 选择智能体
    const onSelectAgent = (agent: AgentType) => {
      if (agent.chartId === currentAgent.value?.chartId) {
        return;
      }
      if (
        messageList.value.length === 1 &&
        messageList.value[0].type === MessageTypeEnum.AGENT_LIST
      ) {
        messageList.value = [];
      }
      updateCurrentAgent(agent);

      difyChatRef.value?.startNewConversation();
      difyConversationId.value = "";

      updateMessageContainerScroll();
    };

    // 添加新会话
    const onAddDifyConversation = () => {
      difyChatRef.value?.startNewConversation();
      difyConversationId.value = "";
    };



    // 关闭会话
    const onCloseConversation = () => {
      historyVisible.value = false;
    };

    // 生命周期钩子
    onMounted(() => {
      if (props.token) {
        setToken(props.token);
      }
      if (props.kkServerUrl) {
        setKKServerUrl(props.kkServerUrl);
      }
      if (props.kkLocalUrl) {
        setKKLocalUrl(props.kkLocalUrl);
      }
      if (!validateNull(props.isMicro)) {
        setISMICRO(props.isMicro);
      }
      initAgentList();
    });

    // 监听聊天界面可见性变化
    watch(
      () => props.chatVisible,
      val => {
        if (val) {
          nextTick(() => {
            updateMessageContainerScroll();
          });
        }
      }
    );



    // 监听当前 Dify 会话变化（简化处理，因为 DifyConversationItem 类型不包含这些属性）
    watch(
      () => currentDifyConversation.value,
      val => {
        if (!val) {
          return;
        }
        // Dify 会话的处理逻辑在 DifyChat 组件中
      }
    );

    // 监听历史记录初始化状态
    watch(
      () => historyInited.value,
      val => {
        if (val) {
          nextTick(() => {
            const messageContainerEle =
              document.getElementById("messageContainer");
            messageContainerEle?.addEventListener("scroll", handleScroll);
          });
        }
      }
    );

    // 组件卸载时清理事件监听
    onUnmounted(() => {
      const messageContainerEle = document.getElementById("messageContainer");
      messageContainerEle?.removeEventListener("scroll", handleScroll);
    });

    // 暴露方法给父组件
    expose({
      sendCopilotMsg,
    });

    return {
      currentConversation,
      currentDifyConversation,
      historyVisible,
      agentList,
      currentAgent,
      agentListVisible,
      conversationRef,
      difyChatRef,
      chatClass,
      onSelectConversation,
      onMsgDataLoaded,
      onToggleHistoryVisible,
      onOpenAgents,
      onAddConversation,
      onSelectAgent,
      onCloseConversation,
      onDifySelectConversation,
      onUpdateConversationId,
      difyConversationId,
      onAddDifyConversation,
      susChatRef,
    };
  },
});
</script>

<style lang="less" scoped>
@import "./index.less";
</style>
