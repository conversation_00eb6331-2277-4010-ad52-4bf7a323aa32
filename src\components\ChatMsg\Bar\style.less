@import "../../../styles/index.less";

@bar-cls: ~"@{supersonic-chat-prefix}-bar";

.@{bar-cls} {
  &-chart {
    height: 260px;
    margin-top: 16px;
  }

  &-top-bar {
    display: flex;
    align-items: baseline;
    flex-wrap: wrap;
    column-gap: 8px;
    row-gap: 12px;
  }

  &-filter-section-wrapper {
    display: flex;
    align-items: center;
    color: var(--text-color-third);
  }

  &-filter-section {
    display: flex;
    align-items: center;
    font-size: 13px;
    column-gap: 12px;
    color: var(--text-color-third);
  }

  &-filter-item {
    display: flex;
    align-items: center;
  }

  &-filter-item-label {
    color: var(--text-color-third);
  }

  &-filter-item-value {
    color: var(--text-color);
    font-weight: 500;
  }

  &-indicator-name {
    font-size: 14px;
    color: var(--text-color);
    font-weight: 500;
    margin-top: 2px;
  }

  &-date-range {
    margin-top: 12px;
    font-size: 13px;
    color: var(--text-color-third);
  }
}
