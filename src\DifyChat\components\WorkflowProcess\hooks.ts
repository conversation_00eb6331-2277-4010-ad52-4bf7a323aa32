import { ref, computed } from "vue";
import type {
  AgentLogItemWithChildren,
  IterationDurationMap,
  LoopDurationMap,
  LoopVariableMap,
  NodeTracing,
} from "@/DifyChat/types/app";

export const useLogs = () => {
  // 重试详情状态
  const showRetryDetail = ref(false);
  const setShowRetryDetailTrue = () => {
    showRetryDetail.value = true;
  };
  const setShowRetryDetailFalse = () => {
    showRetryDetail.value = false;
  };
  const retryResultList = ref<NodeTracing[]>([]);
  const handleShowRetryResultList = (detail: NodeTracing[]) => {
    setShowRetryDetailTrue();
    retryResultList.value = detail;
  };

  // 迭代详情状态
  const showIteratingDetail = ref(false);
  const setShowIteratingDetailTrue = () => {
    showIteratingDetail.value = true;
  };
  const setShowIteratingDetailFalse = () => {
    showIteratingDetail.value = false;
  };
  const iterationResultList = ref<NodeTracing[][]>([]);
  const iterationResultDurationMap = ref<IterationDurationMap>({});
  const handleShowIterationResultList = (
    detail: NodeTracing[][],
    iterDurationMap: IterationDurationMap
  ) => {
    setShowIteratingDetailTrue();
    iterationResultList.value = detail;
    iterationResultDurationMap.value = iterDurationMap;
  };

  // 循环详情状态
  const showLoopingDetail = ref(false);
  const setShowLoopingDetailTrue = () => {
    showLoopingDetail.value = true;
  };
  const setShowLoopingDetailFalse = () => {
    showLoopingDetail.value = false;
  };
  const loopResultList = ref<NodeTracing[][]>([]);
  const loopResultDurationMap = ref<LoopDurationMap>({});
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const loopResultVariableMap = ref<Record<string, any>>({});
  const handleShowLoopResultList = (
    detail: NodeTracing[][],
    loopDurationMap: LoopDurationMap,
    loopVariableMap: LoopVariableMap
  ) => {
    setShowLoopingDetailTrue();
    loopResultList.value = detail;
    loopResultDurationMap.value = loopDurationMap;
    loopResultVariableMap.value = loopVariableMap;
  };

  // Agent或Tool日志状态
  const agentOrToolLogItemStack = ref<AgentLogItemWithChildren[]>([]);
  const agentOrToolLogListMap = ref<Record<string, AgentLogItemWithChildren[]>>(
    {}
  );
  const handleShowAgentOrToolLog = (detail?: AgentLogItemWithChildren) => {
    if (!detail) {
      agentOrToolLogItemStack.value = [];
      return;
    }
    const { id, children } = detail;
    let currentAgentOrToolLogItemStack = [...agentOrToolLogItemStack.value];
    const index = currentAgentOrToolLogItemStack.findIndex(
      logItem => logItem.id === id
    );

    if (index > -1)
      currentAgentOrToolLogItemStack = currentAgentOrToolLogItemStack.slice(
        0,
        index + 1
      );
    else
      currentAgentOrToolLogItemStack = [
        ...currentAgentOrToolLogItemStack.slice(),
        detail,
      ];

    agentOrToolLogItemStack.value = currentAgentOrToolLogItemStack;

    if (children) {
      agentOrToolLogListMap.value = {
        ...agentOrToolLogListMap.value,
        [id]: children,
      };
    }
  };

  // 计算是否显示特殊结果面板
  const showSpecialResultPanel = computed(
    () =>
      showRetryDetail.value ||
      showIteratingDetail.value ||
      showLoopingDetail.value ||
      !!agentOrToolLogItemStack.value.length
  );

  return {
    showSpecialResultPanel,
    showRetryDetail,
    setShowRetryDetailTrue,
    setShowRetryDetailFalse,
    retryResultList,
    handleShowRetryResultList,

    showIteratingDetail,
    setShowIteratingDetailTrue,
    setShowIteratingDetailFalse,
    iterationResultList,
    iterationResultDurationMap,
    handleShowIterationResultList,

    showLoopingDetail,
    setShowLoopingDetailTrue,
    setShowLoopingDetailFalse,
    loopResultList,
    loopResultDurationMap,
    loopResultVariableMap,
    handleShowLoopResultList,

    agentOrToolLogItemStack,
    agentOrToolLogListMap,
    handleShowAgentOrToolLog,
  };
};
