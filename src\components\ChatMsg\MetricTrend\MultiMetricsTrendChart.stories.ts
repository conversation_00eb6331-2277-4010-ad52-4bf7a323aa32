import { Meta, StoryObj } from '@storybook/vue3';
import MultiMetricsTrendChart from './MultiMetricsTrendChart.vue';
import { ColumnType } from '../../../common/type';

const meta: Meta<typeof MultiMetricsTrendChart> = {
  component: MultiMetricsTrendChart,
  title: 'Components/ChatMsg/MultiMetricsTrendChart',
  tags: ['autodocs'],
  argTypes: {
    dateColumnName: {
      control: 'text',
      description: '日期列名'
    },
    metricFields: {
      control: 'array',
      description: '指标字段列表'
    },
    resultList: {
      control: 'array',
      description: '结果列表'
    },
    triggerResize: {
      control: 'boolean',
      description: '触发图表重绘'
    },
    chartType: {
      control: 'select',
      options: ['line', 'bar'],
      description: '图表类型'
    },
    question: {
      control: 'text',
      description: '问题描述'
    }
  },
  parameters: {
    docs: {
      description: {
        component: '多指标趋势图组件，用于同时展示多个指标随时间的变化趋势'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof MultiMetricsTrendChart>;

// 基础多指标趋势图
export const Basic: Story = {
  args: {
    dateColumnName: 'date',
    metricFields: [
      {
        name: '销售额',
        bizName: 'sales',
        showType: 'NUMBER',
        dataFormatType: 'decimal',
        dataFormat: {
          decimalPlaces: 2
        }
      },
      {
        name: '利润',
        bizName: 'profit',
        showType: 'NUMBER',
        dataFormatType: 'decimal',
        dataFormat: {
          decimalPlaces: 2
        }
      }
    ],
    resultList: [
      { date: '2023-01-01', sales: '15689234.56', profit: '2345678.90' },
      { date: '2023-01-02', sales: '16892345.67', profit: '2456789.01' },
      { date: '2023-01-03', sales: '18923456.78', profit: '2567890.12' },
      { date: '2023-01-04', sales: '17892345.89', profit: '2678901.23' },
      { date: '2023-01-05', sales: '19823456.90', profit: '2789012.34' }
    ],
    triggerResize: false,
    chartType: 'line',
    question: '销售和利润趋势'
  }
};

// 柱状图
export const BarChart: Story = {
  args: {
    dateColumnName: 'date',
    metricFields: [
      {
        name: '销售额',
        bizName: 'sales',
        showType: 'NUMBER',
        dataFormatType: 'decimal',
        dataFormat: {
          decimalPlaces: 2
        }
      },
      {
        name: '利润',
        bizName: 'profit',
        showType: 'NUMBER',
        dataFormatType: 'decimal',
        dataFormat: {
          decimalPlaces: 2
        }
      }
    ],
    resultList: [
      { date: '2023-01-01', sales: '15689234.56', profit: '2345678.90' },
      { date: '2023-01-02', sales: '16892345.67', profit: '2456789.01' },
      { date: '2023-01-03', sales: '18923456.78', profit: '2567890.12' },
      { date: '2023-01-04', sales: '17892345.89', profit: '2678901.23' },
      { date: '2023-01-05', sales: '19823456.90', profit: '2789012.34' }
    ],
    triggerResize: false,
    chartType: 'bar',
    question: '销售和利润趋势'
  }
};

// 多指标混合类型
export const MixedTypes: Story = {
  args: {
    dateColumnName: 'date',
    metricFields: [
      {
        name: '销售额',
        bizName: 'sales',
        showType: 'NUMBER',
        dataFormatType: 'decimal',
        dataFormat: {
          decimalPlaces: 2
        }
      },
      {
        name: '增长率',
        bizName: 'growth_rate',
        showType: 'NUMBER',
        dataFormatType: 'percent',
        dataFormat: {
          decimalPlaces: 2,
          needMultiply100: true
        }
      }
    ],
    resultList: [
      { date: '2023-01-01', sales: '15689234.56', growth_rate: '0.125' },
      { date: '2023-01-02', sales: '16892345.67', growth_rate: '0.083' },
      { date: '2023-01-03', sales: '18923456.78', growth_rate: '0.156' },
      { date: '2023-01-04', sales: '17892345.89', growth_rate: '0.092' },
      { date: '2023-01-05', sales: '19823456.90', growth_rate: '0.134' }
    ],
    triggerResize: false,
    chartType: 'line',
    question: '销售额和增长率趋势'
  }
}; 