import { Meta, StoryFn } from '@storybook/vue3';
import NoPermissionChart from './index.vue';
import { CLS_PREFIX } from '../../../common/constants';
import { fn } from '@storybook/test';

export default {
  title: 'Components/ChatMsg/NoPermissionChart',
  component: NoPermissionChart,
  tags: ['autodocs'],
  argTypes: {
    model: {
      control: { type: 'text' },
      description: '需要申请的模型名称',
    },
    chartType: {
      control: { type: 'select' },
      options: ['', 'barChart'],
      description: '图表类型',
    },
    onApplyAuth: {
      action: 'applyAuth',
      description: '申请权限的回调函数',
    },
  },
  parameters: {
    docs: {
      description: {
        component: '无权限图表组件，用于用户没有特定模型查看图表的权限时展示',
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#333333' },
      ],
    },
  },
} as Meta<typeof NoPermissionChart>;

const Template: StoryFn<typeof NoPermissionChart> = (args) => ({
  components: { NoPermissionChart },
  setup() {
    return { args, CLS_PREFIX };
  },
  template: `
    <div style="padding: 20px; max-width: 600px; border: 1px solid #eee; border-radius: 8px;">
      <NoPermissionChart v-bind="args" @applyAuth="args.onApplyAuth" />
    </div>
  `,
});

export const 默认图表 = Template.bind({});
默认图表.args = {
  model: 'GPT-4',
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};

export const 柱状图 = Template.bind({});
柱状图.args = {
  model: 'GPT-4',
  chartType: 'barChart',
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};

export const 不同模型 = Template.bind({});
不同模型.args = {
  model: 'Claude-3',
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};

export const 深色背景 = Template.bind({});
深色背景.args = {
  model: 'GPT-4',
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};
深色背景.parameters = {
  backgrounds: { default: 'dark' },
};
深色背景.decorators = [
  () => ({ 
    template: '<div style="padding: 20px; color: white; background-color: #333; border-radius: 8px;"><story /></div>' 
  }),
]; 