import { Meta, StoryObj } from '@storybook/vue3';
import WebPage from './index.vue';

// 更多关于如何设置故事：https://storybook.js.org/docs/writing-stories
const meta = {
  title: 'Components/ChatMsg/WebPage',
  component: WebPage,
  // 此组件将有一个自动生成的文档页面：https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  argTypes: {
    id: {
      control: 'text',
      description: 'iframe的唯一标识符'
    },
    data: {
      control: 'object',
      description: '消息数据对象'
    }
  },
  // 参数的默认值
  args: {
    id: '1001',
    data: {
      response: {
        name: '示例网页',
        webPage: {
          url: 'https://example.com',
          params: []
        }
      }
    }
  },
} satisfies Meta<typeof WebPage>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const 基础示例: Story = {
  args: {
    id: '1001',
    data: {
      response: {
        name: '示例网页',
        webPage: {
          url: 'https://example.com',
          params: []
        }
      }
    }
  }
};

// 带参数的网页
export const 带参数的网页: Story = {
  args: {
    id: '1002',
    data: {
      response: {
        name: '参数化网页',
        webPage: {
          url: 'https://example.com/dashboard',
          params: [
            {
              key: 'theme',
              value: 'dark',
              paramType: 'NORMAL'
            },
            {
              key: 'layout',
              value: 'compact',
              paramType: 'NORMAL'
            }
          ]
        }
      }
    }
  }
};

// 指定高度的网页
export const 指定高度的网页: Story = {
  args: {
    id: '1003',
    data: {
      response: {
        name: '自定义高度网页',
        webPage: {
          url: 'https://example.com/widget',
          params: [
            {
              key: 'height',
              value: 400,
              paramType: 'FORWARD'
            }
          ]
        }
      }
    }
  }
};

// 仪表盘示例
export const 仪表盘示例: Story = {
  args: {
    id: '1004',
    data: {
      response: {
        name: '业务数据仪表盘',
        webPage: {
          url: 'https://example.com/analytics?type=dashboard',
          params: [
            {
              key: 'dateRange',
              value: 'last7days',
              paramType: 'NORMAL'
            },
            {
              key: 'view',
              value: 'summary',
              paramType: 'NORMAL'
            }
          ]
        }
      }
    }
  }
};

// 小部件示例
export const 小部件示例: Story = {
  args: {
    id: '1005',
    data: {
      response: {
        name: '数据小部件',
        webPage: {
          url: 'https://example.com/widget?type=widget',
          params: [
            {
              key: 'chartType',
              value: 'line',
              paramType: 'NORMAL'
            },
            {
              key: 'height',
              value: 300,
              paramType: 'FORWARD'
            }
          ]
        }
      }
    }
  }
};

// 移动端视图
export const 移动端视图: Story = {
  args: {
    id: '1006',
    data: {
      response: {
        name: '移动端适配页面',
        webPage: {
          url: 'https://example.com',
          params: [
            {
              key: 'device',
              value: 'mobile',
              paramType: 'NORMAL'
            },
            {
              key: 'height',
              value: 600,
              paramType: 'FORWARD'
            }
          ]
        }
      }
    }
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}; 