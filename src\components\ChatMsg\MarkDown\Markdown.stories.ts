import type { Meta, StoryObj } from '@storybook/vue3';
import MarkDown from './index.vue';

// 定义元数据
const meta = {
  title: 'Components/ChatMsg/MarkDown',
  component: MarkDown,
  tags: ['autodocs'],
  argTypes: {
    markdown: {
      control: 'text',
      description: 'Markdown 文本内容',
    },
    loading: {
      control: 'boolean',
      description: '是否显示加载状态',
    },
    onApplyAuth: {
      action: 'onApplyAuth',
      description: '申请权限回调函数',
    },
  },
} satisfies Meta<typeof MarkDown>;

export default meta;

type Story = StoryObj<typeof meta>;

// 简单文本
export const SimpleText: Story = {
  args: {
    markdown: '这是一段简单的 Markdown 文本',
  },
};

// 标题展示
export const Headers: Story = {
  args: {
    markdown: `# 一级标题
## 二级标题
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题`,
  },
};

// 列表展示
export const Lists: Story = {
  args: {
    markdown: `
- 无序列表项 1
- 无序列表项 2
  - 嵌套列表项 2.1
  - 嵌套列表项 2.2
- 无序列表项 3

1. 有序列表项 1
2. 有序列表项 2
   1. 嵌套列表项 2.1
   2. 嵌套列表项 2.2
3. 有序列表项 3
`,
  },
};

// 代码块展示
export const CodeBlock: Story = {
  args: {
    markdown: `\`\`\`javascript
function helloWorld() {
  console.log('Hello, world!');
}

// 调用函数
helloWorld();
\`\`\``,
  },
};

// 表格展示
export const Table: Story = {
  args: {
    markdown: `
| 姓名 | 年龄 | 职业 |
| ---- | ---- | ---- |
| 张三 | 25   | 工程师 |
| 李四 | 30   | 设计师 |
| 王五 | 28   | 产品经理 |
`,
  },
};

// 链接和图片
export const LinksAndImages: Story = {
  args: {
    markdown: `
[GitHub](https://github.com)

![示例图片](https://via.placeholder.com/150)
`,
  },
};

// 复杂组合示例
export const ComplexExample: Story = {
  args: {
    markdown: `# Markdown 综合示例

这是一个**粗体文本**，这是一个*斜体文本*。

## 代码示例

\`\`\`python
def greet(name):
    return f"Hello, {name}!"

print(greet("World"))
\`\`\`

## 引用

> 这是一段引用文本
> 
> 这是引用的第二行

## 任务列表

- [x] 已完成任务
- [ ] 未完成任务
- [ ] 另一个未完成任务

## 表格

| 功能 | 描述 | 支持情况 |
| ---- | ---- | -------- |
| 表格 | 显示数据 | ✅ |
| 代码高亮 | 代码语法高亮 | ✅ |
| 任务列表 | 可勾选的任务 | ✅ |
`,
  },
};

export const Default: Story = {
  args: {
    markdown: '# 标题\n\n这是一段 Markdown 文本。',
  },
};

export const WithLoading: Story = {
  args: {
    markdown: '# 标题\n\n这是一段 Markdown 文本。',
    loading: true,
  },
};

export const WithCodeBlock: Story = {
  args: {
    markdown: `# 代码示例

\`\`\`javascript
function hello() {
  console.log('Hello, World!');
}
\`\`\`
`,
  },
}; 