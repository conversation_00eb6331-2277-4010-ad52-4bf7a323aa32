<template>
  <a-modal
    :open="visible"
    title="点评一下~"
    @ok="onOk"
    @cancel="onClose"
    ok-text="提交"
    cancel-text="取消"
  >
    <div :class="`${prefixCls}-feedback-item`">
      <div :class="`${prefixCls}-feedback-item-title`">评价</div>
      <a-textarea
        placeholder="请输入评价"
        :rows="3"
        v-model:value="feedbackText"
        @click="handleClick"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message, Modal as AModal, Input as AInput } from 'ant-design-vue';
import { CLS_PREFIX } from '../../common/constants';

const ATextarea = AInput.TextArea;

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  feedbackValue: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['submit', 'close']);

const feedbackText = ref(props.feedbackValue);
const prefixCls = `${CLS_PREFIX}-tools`;

const onOk = () => {
  if (feedbackText.value.trim() === '') {
    message.warning('请输入点评内容');
    return;
  }
  emit('submit', feedbackText.value);
};

const onClose = () => {
  emit('close');
};

const handleClick = (e: Event) => {
  e.stopPropagation();
};
</script>

<style lang="less" scoped>
@import './style.less';
</style>
