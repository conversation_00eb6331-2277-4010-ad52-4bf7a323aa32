/* eslint-disable @typescript-eslint/no-explicit-any */
import type { VisionFile } from "./app";

export type LogAnnotation = {
  content: string;
  account: {
    id: string;
    name: string;
    email: string;
  };
  created_at: number;
};

export type Annotation = {
  id: string;
  authorName: string;
  logAnnotation?: LogAnnotation;
  created_at?: number;
};

export const MessageRatings = ["like", "dislike", null] as const;
export type MessageRating = (typeof MessageRatings)[number];

export type MessageMore = {
  time: string;
  tokens: number;
  latency: number | string;
};

export type Feedbacktype = {
  rating: MessageRating;
  content?: string | null;
};

export type FeedbackFunc = (
  messageId: string,
  feedback: Feedbacktype
) => Promise<any>;
export type SubmitAnnotationFunc = (
  messageId: string,
  content: string
) => Promise<any>;

export type DisplayScene = "web" | "console";

export type ToolInfoInThought = {
  name: string;
  input: string;
  output: string;
  isFinished: boolean;
};

export type ThoughtItem = {
  id: string;
  tool: string; // plugin or dataset. May has multi.
  thought: string;
  tool_input: string;
  message_id: string;
  observation: string;
  position: number;
  files?: string[];
  message_files?: VisionFile[];
};

export type CitationItem = {
  content: string;
  data_source_type: string;
  dataset_name: string;
  dataset_id: string;
  document_id: string;
  document_name: string;
  hit_count: number;
  index_node_hash: string;
  segment_id: string;
  segment_position: number;
  score: number;
  word_count: number;
};

export type IChatItem = {
  id: string;
  content: string;
  citation?: CitationItem[];
  /**
   * Specific message type
   */
  isAnswer: boolean;
  /**
   * The user feedback result of this message
   */
  feedback?: Feedbacktype;
  /**
   * The admin feedback result of this message
   */
  adminFeedback?: Feedbacktype;
  /**
   * Whether to hide the feedback area
   */
  feedbackDisabled?: boolean;
  /**
   * More information about this message
   */
  more?: MessageMore;
  annotation?: Annotation;
  useCurrentUserAvatar?: boolean;
  isOpeningStatement?: boolean;
  suggestedQuestions?: string[];
  log?: { role: string; text: string }[];
  agent_thoughts?: ThoughtItem[];
  message_files?: VisionFile[];
};

export type MessageEnd = {
  id: string;
  metadata: {
    retriever_resources?: CitationItem[];
    annotation_reply: {
      id: string;
      account: {
        id: string;
        name: string;
      };
    };
  };
};

export type MessageReplace = {
  id: string;
  task_id: string;
  answer: string;
  conversation_id: string;
};

export type AnnotationReply = {
  id: string;
  task_id: string;
  answer: string;
  conversation_id: string;
  annotation_id: string;
  annotation_author_name: string;
};

export type DifyAgentType = {
  id: string;
  name: string;
  description: string;
  opening_statement?: string; //开场白
  more_like_this?: {
    enabled: boolean;
  };
  user_input_form?: any[];
  sensitive_word_avoidance?: {
    enabled: boolean;
    type: string;
    configs: any[];
  };
  file_upload?: {
    image: {
      detail: string;
      enabled: boolean;
      number_limits: number;
      transfer_methods: string[];
    };
    enabled: boolean;
    allowed_file_types: any[];
    allowed_file_extensions: string[];
    allowed_file_upload_methods: string[];
    number_limits: number;
  };
  system_parameters?: {
    image_file_size_limit: number;
    video_file_size_limit: number;
    audio_file_size_limit: number;
    file_size_limit: number;
    workflow_file_upload_limit: number;
  };
};

export enum SupportUploadFileTypes {
  image = "image",
  document = "document",
  audio = "audio",
  video = "video",
  custom = "custom",
}
