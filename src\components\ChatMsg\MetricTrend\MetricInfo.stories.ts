import { Meta, StoryObj } from '@storybook/vue3';
import MetricInfo from './MetricInfo.vue';
import { AggregateInfoType, ColumnType } from '../../../common/type';

const meta: Meta<typeof MetricInfo> = {
  component: MetricInfo,
  title: 'Components/ChatMsg/MetricInfo',
  tags: ['autodocs'],
  argTypes: {
    aggregateInfo: {
      control: 'object',
      description: '聚合信息'
    },
    currentMetricField: {
      control: 'object',
      description: '当前指标字段'
    }
  },
  parameters: {
    docs: {
      description: {
        component: '指标信息组件，用于展示指标的当前值和统计信息'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof MetricInfo>;

// 基础指标信息
export const Basic: Story = {
  args: {
    aggregateInfo: {
      metricInfos: [
        {
          date: '2023-01-05',
          value: '19823456.90',
          statistics: {
            '环比': '+10.5%',
            '同比': '+8.3%'
          }
        }
      ]
    },
    currentMetricField: {
      name: '销售额',
      bizName: 'sales',
      showType: 'NUMBER',
      dataFormatType: 'decimal',
      dataFormat: {
        decimalPlaces: 2
      }
    }
  }
};

// 百分比指标
export const Percentage: Story = {
  args: {
    aggregateInfo: {
      metricInfos: [
        {
          date: '2023-01-05',
          value: '0.1856',
          statistics: {
            '环比': '+2.5%',
            '同比': '-1.3%'
          }
        }
      ]
    },
    currentMetricField: {
      name: '增长率',
      bizName: 'growth_rate',
      showType: 'NUMBER',
      dataFormatType: 'percent',
      dataFormat: {
        decimalPlaces: 2,
        needMultiply100: true
      }
    }
  }
};

// 大数值指标
export const LargeNumber: Story = {
  args: {
    aggregateInfo: {
      metricInfos: [
        {
          date: '2023-01-05',
          value: '1234567890.12',
          statistics: {
            '环比': '+15.5%',
            '同比': '+12.3%'
          }
        }
      ]
    },
    currentMetricField: {
      name: '销售额',
      bizName: 'sales',
      showType: 'NUMBER',
      dataFormatType: 'decimal',
      dataFormat: {
        decimalPlaces: 2
      }
    }
  }
}; 