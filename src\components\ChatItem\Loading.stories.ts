import { Meta, StoryFn } from "@storybook/vue3";
import Loading from "./Loading.vue";
import { PREFIX_CLS } from "../../common/constants";

export default {
  title: "ChatItem/Loading",
  component: Loading,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component: "加载中动画组件，显示三个跳动的点来表示加载状态",
      },
    },
    backgrounds: {
      default: "light",
      values: [
        { name: "light", value: "#ffffff" },
        { name: "dark", value: "#333333" },
      ],
    },
  },
} as Meta<typeof Loading>;

const Template: StoryFn<typeof Loading> = args => ({
  components: { Loading },
  setup() {
    return { args, PREFIX_CLS };
  },
  template: `
    <div style="padding: 20px;">
      <Loading v-bind="args" />
    </div>
  `,
});

export const 默认 = Template.bind({});

export const 带背景 = Template.bind({});
带背景.decorators = [
  () => ({
    template:
      '<div style="padding: 20px; background-color: #f0f0f0; border-radius: 4px;"><story /></div>',
  }),
];
