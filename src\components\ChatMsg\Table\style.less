@import '../../../styles/index.less';

@table-prefix-cls: ~'@{supersonic-chat-prefix}-table';

.@{table-prefix-cls} {
  margin-top: 6px;

  &-photo {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-filter-section-wrapper {
    display: flex;
    align-items: center;
    color: var(--text-color-third);
    margin-bottom: 12px;
  }

  &-filter-section {
    display: flex;
    align-items: center;
    font-size: 13px;
    column-gap: 12px;
    color: var(--text-color-third);
  }

  &-top-bar {
    display: flex;
    align-items: baseline;
    flex-wrap: wrap;
    column-gap: 8px;
    row-gap: 12px;
    font-style: italic;
    margin-bottom: 15px;
  }
  &-indicator-name {
    font-size: 14px;
    color: var(--text-color);
    font-weight: 500;
    margin-top: 2px;
  }

  &-filter-item {
    display: flex;
    align-items: center;
  }

  &-filter-item-label {
    color: var(--text-color-third);
  }

  &-filter-item-value {
    color: var(--text-color);
    font-weight: 500;
  }

  &-drill-down-dimensions {
    margin-top: 12px;
  }

  table {
    width: 100%;
  }

  &-even-row {
    background-color: #fbfbfb;
  }

  .ant-table-container table > thead > tr:first-child th:first-child {
    border-top-left-radius: 12px !important;
    border-bottom-left-radius: 12px !important;
  }

  .ant-table-container table > thead > tr:first-child th:last-child {
    border-top-right-radius: 12px !important;
    border-bottom-right-radius: 12px !important;
  }

  .ant-table-tbody > tr.ant-table-row:hover > td {
    background-color: #fafafa !important;
  }

  .ant-table-cell {
    text-align: center !important;
  }

  .ant-table-thead {
    .ant-table-cell {
      padding-top: 10px;
      padding-bottom: 10px;
      color: #666;
      font-size: 13px;
      background: #f0f2f5;

      &::before {
        display: none;
      }
    }
  }

  .@{table-prefix-cls}-formatted-value {
    font-weight: 500;
  }

  .ant-table-thead .ant-table-cell {
    padding-top: 8.5px;
    padding-bottom: 8.5px;
    color: #737b7b;
    font-weight: 500;
    font-size: 14px;
    background-color: #edf2f2;
  }

  .ant-table-tbody {
    .ant-table-cell {
      padding: 12px 2px;
      color: var(--text-color);
      font-size: 14px;
    }
  }

  .ant-table-pagination.ant-pagination {
    margin-bottom: 0;
  }
}