import { Meta, StoryObj } from '@storybook/vue3';
import SqlItem from './SqlItem.vue';
import { SqlInfoType } from '../../common/type';

// 模拟数据
const mockSqlInfo: SqlInfoType = {
  parsedS2SQL: 'SELECT COUNT(*) as order_count FROM orders WHERE created_at >= "2023-01-01" AND created_at <= "2023-12-31"',
  correctedS2SQL: 'SELECT COUNT(*) as order_count FROM orders WHERE created_at >= "2023-01-01" AND created_at <= "2023-12-31" AND status = "completed"',
  querySQL: 'SELECT COUNT(*) as order_count FROM orders WHERE created_at >= "2023-01-01" AND created_at <= "2023-12-31" AND status = "completed"'
};

// 模拟 LLM 请求数据
const mockLlmReq = {
  schema: {
    fieldNameList: ['订单', '订单数', '状态'],
    values: [
      {
        fieldName: '状态',
        fieldValue: 'completed'
      }
    ]
  },
  terms: [
    {
      name: '订单',
      alias: ['order'],
      description: '用户发起的购买请求'
    },
    {
      name: '订单数',
      alias: ['order_count'],
      description: '用户成功下单的数量'
    }
  ],
  priorExts: '默认只计算已完成的订单'
};

// 模拟 LLM 响应数据
const mockLlmResp = {
  sqlRespMap: {
    'queryId1': {
      fewShots: [
        {
          question: '2022年的订单数是多少?',
          sql: 'SELECT COUNT(*) as order_count FROM orders WHERE created_at >= "2022-01-01" AND created_at <= "2022-12-31" AND status = "completed"'
        },
        {
          question: '2023年1月的订单数是多少?',
          sql: 'SELECT COUNT(*) as order_count FROM orders WHERE created_at >= "2023-01-01" AND created_at <= "2023-01-31" AND status = "completed"'
        }
      ]
    }
  }
};

// 定义组件的Meta
const meta = {
  title: 'ChatItem/SqlItem',
  component: SqlItem,
  tags: ['autodocs'],
  argTypes: {
    agentId: { 
      control: 'number',
      description: '代理ID'
    },
    queryId: { 
      control: 'number',
      description: '查询ID'
    },
    question: { 
      control: 'text',
      description: '问题内容'
    },
    llmReq: { 
      control: 'object',
      description: 'LLM请求数据'
    },
    llmResp: { 
      control: 'object',
      description: 'LLM响应数据'
    },
    integrateSystem: { 
      control: 'text',
      description: '集成系统'
    },
    queryMode: { 
      control: {
        type: 'select',
        options: ['LLM_S2SQL', 'PLAIN_TEXT', 'RULE_S2SQL']
      },
      description: '查询模式'
    },
    sqlInfo: { 
      control: 'object',
      description: 'SQL信息'
    },
    sqlTimeCost: { 
      control: 'number',
      description: 'SQL执行耗时'
    },
    executeErrorMsg: { 
      control: 'text',
      description: '执行错误消息'
    }
  },
  parameters: {
    docs: {
      description: {
        component: 'SqlItem组件用于展示SQL生成过程及结果，包括解析S2SQL、修正S2SQL和最终执行SQL等信息。主要用于开发者模式下的调试。'
      }
    }
  },
  decorators: [
    () => ({
      template: '<div style="max-width: 800px; margin: 0 auto; padding: 20px;"><story /></div>'
    })
  ]
} satisfies Meta<typeof SqlItem>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Basic: Story = {
  args: {
    agentId: 1,
    queryId: 1001,
    question: '2023年的订单数是多少?',
    sqlInfo: mockSqlInfo,
    sqlTimeCost: 158,
    executeErrorMsg: '',
    queryMode: 'LLM_S2SQL'
  }
};

// 带 LLM 数据示例
export const WithLLMData: Story = {
  args: {
    agentId: 1,
    queryId: 1001,
    question: '2023年的订单数是多少?',
    llmReq: mockLlmReq,
    llmResp: mockLlmResp,
    sqlInfo: mockSqlInfo,
    sqlTimeCost: 158,
    executeErrorMsg: '',
    queryMode: 'LLM_S2SQL'
  }
};

// 执行错误示例
export const WithExecuteError: Story = {
  args: {
    agentId: 1,
    queryId: 1001,
    question: '2023年的订单数是多少?',
    sqlInfo: mockSqlInfo,
    sqlTimeCost: 158,
    executeErrorMsg: 'Error: Table "orders" does not exist',
    queryMode: 'LLM_S2SQL'
  }
};

// 规则引擎解析示例
export const RuleBasedParsing: Story = {
  args: {
    agentId: 1,
    queryId: 1001,
    question: '2023年的订单数是多少?',
    sqlInfo: mockSqlInfo,
    sqlTimeCost: 158,
    executeErrorMsg: '',
    queryMode: 'RULE_S2SQL'
  }
}; 