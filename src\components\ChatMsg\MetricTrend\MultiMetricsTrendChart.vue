<template>
  <div :class="`${prefixCls}-flow-trend-chart`" ref="chartRef" />
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, inject } from 'vue';
import { CHART_SECONDARY_COLOR, CLS_PREFIX, THEME_COLOR_LIST } from '../../../common/constants';
import { getFormattedValue } from '../../../utils/utils';
import * as echarts from 'echarts';
import type { ECharts } from 'echarts';
import dayjs from 'dayjs';
import type { ColumnType } from '../../../common/type';
import { isArray } from 'lodash';

const props = defineProps<{
  dateColumnName: string;
  metricFields: ColumnType[];
  resultList: any[];
  triggerResize?: boolean;
  chartType?: string;
  question: string;
}>();

const prefixCls = `${CLS_PREFIX}-metric-trend`;
const chartRef = ref<HTMLElement | null>(null);
const instanceRef = ref<ECharts>();

// 渲染图表
const renderChart = () => {
  if (!chartRef.value) return;
  
  let instanceObj: any;
  if (!instanceRef.value) {
    instanceObj = echarts.init(chartRef.value);
    instanceRef.value = instanceObj;
  } else {
    instanceObj = instanceRef.value;
    instanceObj.clear();
  }

  const xData = props.resultList?.map((item: any) => {
    const date = isArray(item[props.dateColumnName])
      ? item[props.dateColumnName].join('-')
      : `${item[props.dateColumnName]}`;
    return date.length === 10 ? dayjs(date).format('MM-DD') : date;
  });

  instanceObj.setOption({
    legend: {
      left: 0,
      top: 0,
      icon: 'rect',
      itemWidth: 15,
      itemHeight: 5,
      type: 'scroll',
    },
    xAxis: {
      type: 'category',
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: CHART_SECONDARY_COLOR,
        },
      },
      axisLine: {
        lineStyle: {
          color: CHART_SECONDARY_COLOR,
        },
      },
      axisLabel: {
        showMaxLabel: true,
        color: '#999',
      },
      data: xData,
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          opacity: 0.3,
        },
      },
      axisLabel: {
        formatter: function (value: any) {
          return value === 0 ? 0 : getFormattedValue(value);
        },
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any[]) {
        const param = params[0];
        const valueLabels = params
          .sort((a, b) => b.value - a.value)
          .map(
            (item: any) =>
              `<div style="margin-top: 3px;">${
                item.marker
              } <span style="display: inline-block; width: 70px; margin-right: 12px;">${
                item.seriesName
              }</span><span style="display: inline-block; width: 90px; text-align: right; font-weight: 500;">${
                item.value === '' ? '-' : getFormattedValue(item.value)
              }</span></div>`
          )
          .join('');
        return `${param.name}<br />${valueLabels}`;
      },
    },
    grid: {
      left: '1%',
      right: '4%',
      bottom: '3%',
      top: 45,
      containLabel: true,
    },
    series: props.metricFields.map((metricField, index) => {
      const seriesType = props.chartType || 'line';
      return {
        type: seriesType,
        name: metricField.name,
        symbol: seriesType === 'line' ? 'circle' : undefined,
        showSymbol: seriesType === 'line' && props.resultList.length === 1,
        smooth: seriesType === 'line',
        data: props.resultList.map((item: any) => {
          const value = item[metricField.bizName];
          return (metricField.dataFormatType === 'percent' ||
            metricField.dataFormatType === 'decimal') &&
            metricField.dataFormat?.needMultiply100
            ? value * 100
            : value;
        }),
        color: THEME_COLOR_LIST[index],
      };
    }),
  });
  instanceObj.resize();
};

// 暴露下载图表方法
const downloadChartAsImage = () => {
  if (instanceRef.value) {
    const dataURL = instanceRef.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff',
    });
    const link = document.createElement('a');
    link.download = `${props.question || '图表'}.png`;
    link.href = dataURL;
    link.click();
  }
};

// 注册到上下文
const register = inject('register', null) as ((name: string, fn: Function) => void) | null;
if (register) {
  register('downloadChartAsImage', downloadChartAsImage);
}

// 监听数据变化重新渲染图表
watch(
  [() => props.resultList, () => props.chartType],
  () => {
    renderChart();
  },
  { deep: true }
);

// 监听尺寸变化
watch(
  () => props.triggerResize,
  (newVal) => {
    if (newVal && instanceRef.value) {
      instanceRef.value.resize();
    }
  }
);

// 组件挂载时渲染图表
onMounted(() => {
  renderChart();
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
