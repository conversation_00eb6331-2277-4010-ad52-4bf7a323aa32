<template>
  <div v-if="!isUnauthorized">
    <div :class="`${prefixCls}-top-bar`">
      <div :class="`${prefixCls}-indicator-name`">{{ question }}</div>
    </div>
    <a-spin :spinning="loading">
      <div :class="`${prefixCls}-chart`" ref="chartRef" />
    </a-spin>
  </div>
  <no-permission-chart
    v-else
    :model="entityInfo?.dataSetInfo.name || ''"
    chartType="barChart"
    @apply-auth="onApplyAuth"
  />
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  onUnmounted,
  PropType,
  watch,
  computed,
  toRefs,
} from "vue";
import {
  CHART_BLUE_COLOR,
  CHART_SECONDARY_COLOR,
  PREFIX_CLS,
} from "../../../common/constants";
import { MsgDataType, ColumnType } from "../../../common/type";
import {
  formatByDecimalPlaces,
  getChartLightenColor,
  getFormattedValue,
} from "../../../utils/utils";
import * as echarts from "echarts";
// import type { ECharts } from 'echarts';
import NoPermissionChart from "../NoPermissionChart/index.vue";
import { useExportByEcharts } from "../../../hooks/useExportByEcharts";
import { useChartItemContext } from "../../ChatItem/useChartItemContext";
import { Spin } from "ant-design-vue";

export default defineComponent({
  name: "BarChart",
  components: {
    NoPermissionChart,
    ASpin: Spin,
  },
  props: {
    data: {
      type: Object as PropType<MsgDataType>,
      required: true,
    },
    question: {
      type: String,
      required: true,
    },
    triggerResize: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      required: true,
    },
    metricField: {
      type: Object as PropType<ColumnType>,
      required: true,
    },
    onApplyAuth: {
      type: Function as PropType<(model: string) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const {
      data,
      question,
      loading,
      metricField,
      onApplyAuth: applyAuth,
    } = toRefs(props);
    const prefixCls = `${PREFIX_CLS}-bar`;
    const chartRef = ref<HTMLElement | null>(null);
    const instanceRef = ref<any>(null);

    const categoryColumnName = computed(
      () =>
        data.value.queryColumns?.find(column => column.showType === "CATEGORY")
          ?.bizName || ""
    );

    const metricColumn = computed(() =>
      data.value.queryColumns?.find(column => column.showType === "NUMBER")
    );

    const metricColumnName = computed(() => metricColumn.value?.bizName || "");

    const isUnauthorized = computed(
      () => metricColumn.value && !metricColumn.value?.authorized
    );

    const entityInfo = computed(() => data.value.entityInfo);

    const renderChart = () => {
      if (!chartRef.value) return;

      let instanceObj;
      if (!instanceRef.value) {
        instanceObj = echarts.init(chartRef.value);
        instanceRef.value = instanceObj;
      } else {
        instanceObj = instanceRef.value;
      }

      const chartData = data.value.queryResults || [];
      const xData = chartData.map(item =>
        item[categoryColumnName.value] !== undefined
          ? item[categoryColumnName.value]
          : "未知"
      );

      instanceObj.setOption({
        xAxis: {
          type: "category",
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: CHART_SECONDARY_COLOR,
            },
          },
          axisLabel: {
            width: 200,
            overflow: "truncate",
            showMaxLabel: true,
            hideOverlap: false,
            interval: 0,
            color: "#333",
            rotate: 30,
          },
          data: xData,
        },
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              opacity: 0.3,
            },
          },
          axisLabel: {
            formatter: function (value: any) {
              return value === 0
                ? 0
                : metricField.value.dataFormatType === "percent"
                  ? `${formatByDecimalPlaces(value, metricField.value.dataFormat?.decimalPlaces || 2)}%`
                  : getFormattedValue(value);
            },
          },
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params: any[]) {
            const param = params[0];
            const valueLabels = params
              .map(
                (item: any) =>
                  `<div style="margin-top: 3px;">${
                    item.marker
                  } <span style="display: inline-block; width: 70px; margin-right: 12px;">${
                    item.seriesName
                  }</span><span style="display: inline-block; width: 90px; text-align: right; font-weight: 500;">${
                    item.value === ""
                      ? "-"
                      : metricField.value.dataFormatType === "percent" ||
                          metricField.value.dataFormatType === "decimal"
                        ? `${formatByDecimalPlaces(
                            item.value,
                            metricField.value.dataFormat?.decimalPlaces || 2
                          )}${metricField.value.dataFormatType === "percent" ? "%" : ""}`
                        : getFormattedValue(item.value)
                  }</span></div>`
              )
              .join("");
            return `${param.name}<br />${valueLabels}`;
          },
        },
        grid: {
          left: "2%",
          right: "1%",
          bottom: "3%",
          top: 20,
          containLabel: true,
        },
        series: {
          type: "bar",
          name: metricColumn.value?.name,
          barWidth: 20,
          itemStyle: {
            borderRadius: [10, 10, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: CHART_BLUE_COLOR },
              { offset: 1, color: getChartLightenColor(CHART_BLUE_COLOR) },
            ]),
          },
          label: {
            show: true,
            position: "top",
            formatter: function ({ value }: any) {
              return value === 0
                ? 0
                : metricField.value.dataFormatType === "percent"
                  ? `${formatByDecimalPlaces(value, metricField.value.dataFormat?.decimalPlaces || 2)}%`
                  : getFormattedValue(value);
            },
          },
          data: chartData.map(item => {
            return item[metricColumnName.value];
          }),
        },
      });

      instanceObj.resize();
    };

    // 监听数据变化
    watch(
      () => data.value.queryResults,
      newVal => {
        if (newVal && newVal.length > 0 && metricColumn.value?.authorized) {
          renderChart();
        }
      },
      { deep: true }
    );

    // 监听窗口大小变化
    watch(
      () => props.triggerResize,
      newVal => {
        if (newVal && instanceRef.value) {
          instanceRef.value.resize();
        }
      }
    );

    onMounted(() => {
      if (
        data.value.queryResults &&
        data.value.queryResults.length > 0 &&
        metricColumn.value?.authorized
      ) {
        renderChart();
      }
    });

    onUnmounted(() => {
      if (instanceRef.value) {
        instanceRef.value.dispose();
        instanceRef.value = null;
      }
    });

    const { downloadChartAsImage } = useExportByEcharts({
      instanceRef,
      question: question.value,
    });

    const { register } = useChartItemContext();
    if (register) {
      register("downloadChartAsImage", downloadChartAsImage);
    }

    return {
      chartRef,
      prefixCls,
      isUnauthorized,
      entityInfo,
      loading,
      question,
      onApplyAuth: applyAuth.value,
    };
  },
});
</script>

<style lang="less">
@import "./style.less";
</style>
