import axios from '../service/axiosInstance';
import { isMobile } from '../utils/utils';
import { ShowCaseType } from './type';

// 在Storybook环境中使用代理
const isStorybook = window.location.href.includes('iframe.html') || window.location.href.includes('?story=');
const prefix = isStorybook ? (isMobile ? '/openapi' : '/api') : 'http://127.0.0.1:19080';

export function queryShowCase(agentId: number, current: number, pageSize: number) {
  return axios.post<ShowCaseType>(`${prefix}/chat/manage/queryShowCase?agentId=${agentId}`, {
    current,
    pageSize,
  });
}
