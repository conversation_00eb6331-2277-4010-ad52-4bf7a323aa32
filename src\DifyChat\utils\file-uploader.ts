import mime from "mime";
import { SupportUploadFileTypes } from "../types/type";

export const FILE_EXTS: Record<string, string[]> = {
  [SupportUploadFileTypes.image]: ["JPG", "JPEG", "PNG", "GIF", "WEBP", "SVG"],
  [SupportUploadFileTypes.document]: [
    "TXT",
    "MD",
    "MDX",
    "MARKDOWN",
    "PDF",
    "HTML",
    "XLSX",
    "XLS",
    "DOC",
    "DOCX",
    "CSV",
    "EML",
    "MSG",
    "PPTX",
    "PPT",
    "XML",
    "EPUB",
  ],
  [SupportUploadFileTypes.audio]: ["MP3", "M4A", "WAV", "AMR", "MPGA"],
  [SupportUploadFileTypes.video]: ["MP4", "MOV", "MPEG", "WEBM"],
};

export const getFileExtension = (
  fileName: string,
  fileMimetype: string,
  isRemote?: boolean
) => {
  let extension = "";
  if (fileMimetype) extension = mime.getExtension(fileMimetype) || "";

  if (fileName && !extension) {
    const fileNamePair = fileName.split(".");
    const fileNamePairLength = fileNamePair.length;

    if (fileNamePairLength > 1)
      extension = fileNamePair[fileNamePairLength - 1];
    else extension = "";
  }

  if (isRemote) extension = "";

  return extension;
};

export const getSupportFileType = (
  fileName: string,
  fileMimetype: string,
  isCustom?: boolean
) => {
  if (isCustom) return SupportUploadFileTypes.custom;

  const extension = getFileExtension(fileName, fileMimetype);
  for (const key in FILE_EXTS) {
    if (FILE_EXTS[key].includes(extension.toUpperCase())) return key;
  }

  return "";
};
