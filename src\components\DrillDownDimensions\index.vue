<template>
  <div :class="prefixCls">
    <DimensionSection
      :drillDownDimension="drillDownDimension"
      :dimensions="dimensions"
      :onSelectDimension="onSelectDimension"
      :onCancelDrillDown="cancelDrillDown"
    />
    <DimensionSection
      v-if="drillDownDimension && dimensions.length > 1"
      :drillDownDimension="secondDrillDownDimension"
      :dimensions="filteredDimensions"
      :isSecondDrillDown="true"
      :onSelectDimension="onSelectSecondDimension"
      :onCancelDrillDown="cancelSecondDrillDown"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { CLS_PREFIX } from '../../common/constants';
import type { DrillDownDimensionType, FilterItemType } from '../../common/type';
import DimensionSection from './DimensionSection.vue';

interface Props {
  drillDownDimensions: DrillDownDimensionType[];
  drillDownDimension?: DrillDownDimensionType;
  secondDrillDownDimension?: DrillDownDimensionType;
  originDimensions?: DrillDownDimensionType[];
  dimensionFilters?: FilterItemType[];
  onSelectDimension: (dimension?: DrillDownDimensionType) => void;
  onSelectSecondDimension: (dimension?: DrillDownDimensionType) => void;
}

const props = withDefaults(defineProps<Props>(), {
  drillDownDimension: undefined,
  secondDrillDownDimension: undefined,
  originDimensions: undefined,
  dimensionFilters: undefined
});

const MAX_DIMENSION_COUNT = 20;
const prefixCls = `${CLS_PREFIX}-drill-down-dimensions`;
const dimensions = ref<DrillDownDimensionType[]>([]);

const filteredDimensions = computed(() => {
  if (!props.drillDownDimension) return [];
  return dimensions.value.filter(dimension => dimension.id !== props.drillDownDimension?.id);
});

const initData = () => {
  dimensions.value = props.drillDownDimensions
    .filter(
      dimension =>
        !props.dimensionFilters?.some(filter => filter.name === dimension.name) &&
        (!props.originDimensions || !props.originDimensions.some(item => item.id === dimension.id))
    )
    .slice(0, MAX_DIMENSION_COUNT);
};

const cancelDrillDown = () => {
  props.onSelectDimension(undefined);
};

const cancelSecondDrillDown = () => {
  props.onSelectSecondDimension(undefined);
};

onMounted(() => {
  initData();
});
</script>

<style>
.ss-chat-drill-down-dimensions {
  display: flex;
  align-items: center;
  column-gap: 12px;
}

.ss-chat-drill-down-dimensions-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  column-gap: 6px;
}

.ss-chat-drill-down-dimensions-title {
  color: var(--text-color-third);
}

.ss-chat-drill-down-dimensions-content {
  display: flex;
  align-items: center;
}

.ss-chat-drill-down-dimensions-content-item-name {
  color: var(--chat-blue);
  font-weight: 500;
  border-bottom: 1px solid var(--chat-blue);
  padding: 1px;
  cursor: pointer;
}

.ss-chat-drill-down-dimensions-content-item-active {
  color: #fff;
  border-bottom: none;
  background-color: var(--chat-blue);
  border-radius: 2px;
}

.ss-chat-drill-down-dimensions-menu-item-active {
  color: var(--chat-blue);
}

.ss-chat-drill-down-dimensions-down-arrow {
  color: var(--chat-blue);
}

.ss-chat-drill-down-dimensions-cancel-drill-down {
  margin-left: 20px;
  color: var(--text-color-third);
  cursor: pointer;
  padding: 0 4px;
  border: 1px solid var(--text-color-third);
  border-radius: 4px;
  font-size: 12px;
}

.ss-chat-drill-down-dimensions-cancel-drill-down:hover {
  color: var(--chat-blue);
  border-color: var(--chat-blue);
}
</style> 