import { Meta, StoryObj } from '@storybook/vue3';
import ShowCase from './index.vue';
import { HistoryMsgItemType } from '../common/type';
import { ShowCaseItemType } from './type';

const meta: Meta<typeof ShowCase> = {
  title: 'ShowCase/ShowCase',
  component: ShowCase,
  tags: ['autodocs'],
  argTypes: {
    height: { control: 'text' },
    agentId: { control: 'number' },
    sendMsg: { action: 'sendMsg' },
  },
};

export default meta;
type Story = StoryObj<typeof ShowCase>;

// 模拟数据
const mockShowCaseData = {
  showCaseMap: {
    '1': [
      {
        questionId: 1001,
        queryText: '过去30天销售额是多少?',
        score: 5,
        chatId: 101,
        parseInfos: [
          {
            parseType: 'DATE',
            parseValue: '过去30天',
          },
          {
            parseType: 'METRIC',
            parseValue: '销售额',
          },
        ],
        queryResult: {
          queryState: 'SUCCESS',
          queryMode: 'METRIC_AGG',
          queryText: 'SELECT sum(sales) FROM sales_table WHERE date >= "2023-01-01" AND date <= "2023-01-31"',
          textResult: '过去30天销售额为100000元',
          queryResults: [{ sales: 100000 }],
          queryColumns: [
            { authorized: true, name: '销售额', nameEn: 'sales', bizName: 'sales', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 2, needMultiply100: false } },
          ],
        },
      },
    ],
    '2': [
      {
        questionId: 1002,
        queryText: '各地区销售额分布情况?',
        score: 4,
        chatId: 102,
        parseInfos: [
          {
            parseType: 'DIMENSION',
            parseValue: '地区',
          },
          {
            parseType: 'METRIC',
            parseValue: '销售额',
          },
        ],
        queryResult: {
          queryState: 'SUCCESS',
          queryMode: 'METRIC_AGG',
          textResult: '各地区销售额分布如下：华东地区占比最高，达到40%',
          queryResults: [
            { region: '华东', sales: 40000 },
            { region: '华南', sales: 30000 },
            { region: '华北', sales: 20000 },
            { region: '西部', sales: 10000 },
          ],
          queryColumns: [
            { authorized: true, name: '地区', nameEn: 'region', bizName: 'region', showType: 'CATEGORY', type: 'CATEGORY', dataFormatType: 'string' },
            { authorized: true, name: '销售额', nameEn: 'sales', bizName: 'sales', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 2, needMultiply100: false } },
          ],
        },
      },
    ],
    '3': [
      {
        questionId: 1003,
        queryText: '客单价与上月相比如何变化?',
        score: 4.5,
        chatId: 103,
        parseInfos: [
          {
            parseType: 'DATE',
            parseValue: '上月',
          },
          {
            parseType: 'METRIC',
            parseValue: '客单价',
          },
        ],
        queryResult: {
          queryState: 'SUCCESS',
          queryMode: 'METRIC_AGG',
          textResult: '本月客单价比上月上涨了5.2%',
          queryResults: [
            { month: '上月', avgOrder: 127.5 },
            { month: '本月', avgOrder: 134.1 },
          ],
          queryColumns: [
            { authorized: true, name: '月份', nameEn: 'month', bizName: 'month', showType: 'CATEGORY', type: 'CATEGORY', dataFormatType: 'string' },
            { authorized: true, name: '客单价', nameEn: 'avgOrder', bizName: 'avgOrder', showType: 'NUMBER', type: 'NUMBER', dataFormatType: 'number', dataFormat: { decimalPlaces: 2, needMultiply100: false } },
          ],
        },
      },
    ],
  },
  current: 1,
  pageSize: 20,
};

// 模拟API响应
const mockApiResponses = () => {
  // 劫持全局fetch或axios等API调用
  const originalFetch = window.fetch;
  window.fetch = function(input, init) {
    const url = typeof input === 'string' 
      ? input 
      : input instanceof Request 
        ? input.url
        : input.toString();
    
    // 模拟获取ShowCase数据
    if (url.includes('/chat/manage/queryShowCase')) {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ code: 200, data: mockShowCaseData }),
      } as Response);
    }
    
    // 其它请求走原来的fetch
    return originalFetch(input, init);
  };
};

// 基础示例
export const Default: Story = {
  args: {
    agentId: 1,
    height: '600px',
  },
  decorators: [
    () => ({
      template: '<div style="padding: 20px;"><story /></div>',
      beforeMount() {
        mockApiResponses();
      }
    }),
  ],
};

// 移动端视图
export const MobileView: Story = {
  args: {
    ...Default.args,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
  decorators: Default.decorators,
};

// 固定高度
export const FixedHeight: Story = {
  args: {
    ...Default.args,
    height: '400px',
  },
  decorators: Default.decorators,
};

// 多个展示项
export const MultipleShowcases: Story = {
  args: {
    ...Default.args,
  },
  decorators: [
    () => ({
      template: '<div style="padding: 20px;"><story /></div>',
      beforeMount() {
        const originalShowCaseData = { ...mockShowCaseData };
        // 添加更多展示项
        for (let i = 4; i < 10; i++) {
          const questionType = i % 3;
          const baseItem = originalShowCaseData.showCaseMap[String(questionType + 1)][0];
          const newItem = JSON.parse(JSON.stringify(baseItem));
          newItem.questionId = 1000 + i;
          newItem.queryText = `示例问题 ${i}`;
          newItem.chatId = 100 + i;
          mockShowCaseData.showCaseMap[String(i)] = [newItem];
        }
        mockApiResponses();
      },
    }),
  ],
};

// 加载状态
export const Loading: Story = {
  args: {
    ...Default.args,
  },
  decorators: [
    () => ({
      template: '<div style="padding: 20px;"><story /></div>',
      beforeMount() {
        // 替换原始的queryShowCase函数，增加延迟
        const originalFetch = window.fetch;
        window.fetch = function(input, init) {
          const url = typeof input === 'string' 
            ? input 
            : input instanceof Request 
              ? input.url
              : input.toString();
          
          if (url.includes('/chat/manage/queryShowCase')) {
            return new Promise((resolve) => {
              setTimeout(() => {
                resolve({
                  ok: true,
                  json: () => Promise.resolve({ code: 200, data: mockShowCaseData })
                } as Response);
              }, 2000);
            });
          }
          
          return originalFetch(input, init);
        };
      }
    }),
  ],
}; 