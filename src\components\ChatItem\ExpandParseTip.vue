<template>
  <div :class="`${prefixCls}-parse-tip`">
    <template v-if="!isSimpleMode">
      <div :class="`${prefixCls}-title-bar`">
        <div>
          意图确认
          <span v-if="!!parseTimeCost && isDeveloper" :class="`${prefixCls}-title-tip`">
            (耗时: {{ parseTimeCost }}ms)
          </span>
        </div>
      </div>
    </template>

    <div>
      <template v-for="(parseInfo, index) in parseInfoOptions" :key="`${parseInfo.id}-${parseInfo.textInfo}`">
        <template v-if="isSimpleMode">
          <div style="margin-bottom: 10px">
            <span :class="`${prefixCls}-content-parser-options-title`" style="display: flex">
              <span style="padding-top: 3px; margin-right: 10px">解析{{ index + 1 }}: </span>
              <MarkDown :markdown="parseInfo.textInfo" />
            </span>
          </div>
        </template>
        <template v-else>
          <div style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: '1px solid #eee'">
            <div style="margin-bottom: 10px; height: '30px'; line-height: '30px'">
              <span :class="`${prefixCls}-content-parser-options-title`">解析{{ index + 1 }}:</span>
            </div>
            <div :class="`${prefixCls}-tip`">
              <template>
                <parse-tip 
                  :parse-info="parseInfo" 
                  :dimension-filters="parseInfo.dimensionFilters" 
                  :entity-info="parseInfo.entityInfo" 
                />
                <template v-if="!(!!parseInfo.properties?.type && parseInfo.queryMode !== 'LLM_S2SQL')">
                  <template v-if="!!parseInfo.dateInfo || !!parseInfo.dimensionFilters?.length">
                    <div :class="`${prefixCls}-tip-item`">
                      <div :class="`${prefixCls}-tip-item-name`">筛选条件：</div>
                      <div :class="`${prefixCls}-tip-item-content`">
                        <div :class="`${prefixCls}-tip-item-filter-content`">
                          <template v-if="!!parseInfo.dateInfo">
                            <div :class="tipItemOptionClass">
                              <span :class="`${prefixCls}-tip-item-filter-name`">数据时间：</span>
                              <template v-if="!!currentParseInfo?.nativeQuery">
                                <span :class="itemValueClass">
                                  {{ parseInfo.dateInfo.startDate === parseInfo.dateInfo.endDate
                                    ? parseInfo.dateInfo.startDate
                                    : `${parseInfo.dateInfo.startDate} ~ ${parseInfo.dateInfo.endDate}` }}
                                </span>
                              </template>
                              <template v-else>
                                <a-range-picker
                                  :value="[dayjs(parseInfo.dateInfo.startDate), dayjs(parseInfo.dateInfo.endDate)]"
                                  :disabled="true"
                                  format="YYYY-MM-DD"
                                  @change="onDateInfoChange">
                                  <template #renderExtraFooter>
                                    <a-row :gutter="[28, 28]">
                                      <a-col v-for="(_, key) in ranges" :key="key">
                                        <a-button size="small" @click="() => handlePresetClick(ranges[key])">
                                          {{ key }}
                                        </a-button>
                                      </a-col>
                                    </a-row>
                                  </template>
                                </a-range-picker>
                              </template>
                            </div>
                          </template>
                          <template v-for="(filter, filterIndex) in parseInfo.dimensionFilters" :key="`${filter.name}_${filterIndex}`">
                            <FilterItem
                              :disabled="true"
                              :model-id="parseInfo.modelId!"
                              :filters="parseInfo.dimensionFilters"
                              :filter="filter"
                              :index="filterIndex"
                              :chat-context="parseInfo"
                              :entity-alias="entityAlias"
                              :agent-id="agentId"
                              :integrate-system="integrateSystem"
                              :on-filters-change="onFiltersChange"
                              :on-switch-entity="onSwitchEntity" />
                          </template>
                        </div>
                      </div>
                    </div>
                  </template>
                </template>
              </template>
            </div>
          </div>
        </template>
      </template>

      <template v-if="parseInfoOptions?.length > 1">
        <div :class="`${prefixCls}-content-parser-container`">
          <div :class="`${prefixCls}-content-options`">
            <span v-if="!currentParseInfo" :class="`${prefixCls}-breathing-text`">
              请选择适合的解析意图，并进行下一步:
            </span>

            <template v-for="(parseInfo, index) in parseInfoOptions" :key="parseInfo.id">
              <div
                :class="[
                  `${prefixCls}-content-option`,
                  parseInfo.id === currentParseInfo?.id ? `${prefixCls}-content-option-active` : '',
                  currentParseInfo ? `${prefixCls}-content-option-disabled` : '',
                ]"
                @click="handleSelectParseInfo(parseInfo)">
                解析{{ index + 1 }}
              </div>
            </template>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { Button, DatePicker, Row, Col } from 'ant-design-vue';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons-vue';
import Loading from './Loading.vue';
import FilterItem from './FilterItem.vue';
import { isMobile } from '../../utils/utils';
import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import ParseTip from './ParseTipUtils.vue';
import MarkDown from '../ChatMsg/MarkDown/index.vue';
import { ChatContextType, FilterItemType } from '../../common/type';
import classNames from 'classnames';
import { PREFIX_CLS } from '../../common/constants';

import 'dayjs/locale/zh-cn';

dayjs.extend(quarterOfYear);
dayjs.locale('zh-cn');

const prefixCls = `${PREFIX_CLS}-item`;

type RangeValue = [dayjs.Dayjs, dayjs.Dayjs];
type RangeKeys = '近7日' | '近14日' | '近30日' | '本周' | '本月' | '上月' | '本季度' | '本年';

export default defineComponent({
  name: 'ExpandParseTip',
  components: {
    Loading,
    FilterItem,
    ParseTip,
    MarkDown,
    CheckCircleFilled,
    CloseCircleFilled,
    AButton: Button,
    ARangePicker: DatePicker.RangePicker,
    ARow: Row,
    ACol: Col
  },
  props: {
    isSimpleMode: {
      type: Boolean,
      default: false
    },
    parseInfoOptions: {
      type: Array as () => ChatContextType[],
      required: true
    },
    agentId: {
      type: Number,
      default: undefined
    },
    integrateSystem: {
      type: String,
      default: undefined
    },
    parseTimeCost: {
      type: Number,
      default: undefined
    },
    isDeveloper: {
      type: Boolean,
      default: false
    },
    onSelectParseInfo: {
      type: Function as unknown as () => (parseInfo: ChatContextType) => void,
      required: true
    },
    onSwitchEntity: {
      type: Function as unknown as () => (entityId: string) => void,
      required: true
    },
    onFiltersChange: {
      type: Function as unknown as () => (filters: FilterItemType[]) => void,
      required: true
    },
    onDateInfoChange: {
      type: Function as unknown as () => (dateRange: any) => void,
      required: true
    },
    onRefresh: {
      type: Function as unknown as () => (parseInfo: ChatContextType) => void,
      required: true
    },
    handlePresetClick: {
      type: Function,
      required: true
    }
  },
  setup(props) {
    const currentParseInfo = ref<ChatContextType>();

    const ranges = computed(() => {
      return {
        '近7日': [dayjs().subtract(7, 'day'), dayjs()],
        '近14日': [dayjs().subtract(14, 'day'), dayjs()],
        '近30日': [dayjs().subtract(30, 'day'), dayjs()],
        '本周': [dayjs().startOf('week'), dayjs().endOf('week')],
        '本月': [dayjs().startOf('month'), dayjs().endOf('month')],
        '上月': [
          dayjs().subtract(1, 'month').startOf('month'),
          dayjs().subtract(1, 'month').endOf('month'),
        ],
        '本季度': [dayjs().startOf('quarter'), dayjs().endOf('quarter')],
        '本年': [dayjs().startOf('year'), dayjs().endOf('year')],
      } as Record<RangeKeys, RangeValue>;
    });

    const entityAlias = computed(() => {
      return currentParseInfo.value?.entity?.alias?.[0]?.split('.')?.[0];
    });

    const tipItemOptionClass = computed(() => {
      return classNames(`${prefixCls}-tip-item-option`, {
        [`${prefixCls}-mobile-tip-item-option`]: isMobile,
      });
    });

    const itemValueClass = computed(() => `${prefixCls}-tip-item-value`);

    const handleSelectParseInfo = (parseInfo: ChatContextType) => {
      if (currentParseInfo.value) {
        return;
      }
      currentParseInfo.value = parseInfo;
      props.onSelectParseInfo(parseInfo);
    };

    return {
      currentParseInfo,
      ranges,
      entityAlias,
      tipItemOptionClass,
      itemValueClass,
      prefixCls,
      dayjs,
      handleSelectParseInfo
    };
  }
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
