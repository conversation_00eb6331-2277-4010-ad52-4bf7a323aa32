<template>
  <span :class="prefixCls">
    <span :class="`${prefixCls}-filter-name`">{{ filter.name }}：</span>
    
    <!-- 操作符选择框 - 数值类型且不是ID字段时显示 -->
    <a-select
      v-if="showOperatorSelect"
      :options="operatorOptions"
      :disabled="disabled"
      :class="`${prefixCls}-operator-control`"
      :value="filter.operator"
      @change="(value: SelectValue) => onOperatorChange(value as string)"
    />
    
    <!-- 数值输入框 - 数值类型且不是ID字段时显示 -->
    <a-input-number
      v-if="isNumberValue && !isIdField"
      :disabled="disabled"
      :class="`${prefixCls}-input-number-control`"
      :value="filter.value"
      @change="handleInputNumberChange"
    />
    
    <!-- 日期选择器 - 有效日期字符串时显示 -->
    <a-date-picker
      v-else-if="isValidDateString"
      :value="dayjs(filter.value)"
      @change="onDateChange"
      :allow-clear="false"
    />
    
    <!-- 下拉选择框 - 字符串类型且不是ID字段时显示 -->
    <a-select
      v-else-if="(isStringValue || isArrayValue) && !isIdField"
      :disabled="disabled"
      :value="filter.value"
      :options="selectOptions"
      :class="`${prefixCls}-select-control`"
      @search="debounceFetcher"
      @change="(value: SelectValue) => onChange(value as string | string[])"
      mode="multiple"
      show-search
      allow-clear
    >
      <template #notFoundContent>
        <a-spin v-if="loading" size="small" />
        <span v-else>无匹配结果</span>
      </template>
    </a-select>
    
    <!-- 实体切换 - 特定实体别名且是ID字段时显示 -->
    <template v-else-if="entityAlias && ['歌曲', '艺人'].includes(entityAlias) && isIdField">
      <SwitchEntity
        :entity-name="filter.value"
        :chat-context="chatContext"
        :on-switch-entity="onSwitchEntity"
      />
      <span :class="`${prefixCls}-switch-entity-tip`">
        (如未匹配到相关{{ entityAlias }}，可点击{{ entityAlias === '艺人' ? '歌手' : entityAlias }}ID切换)
      </span>
    </template>
    
    <!-- 默认显示值 -->
    <span v-else :class="`${prefixCls}-filter-value`">
      {{ typeof filter.value !== 'object' ? filter.value : '' }}
    </span>
  </span>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { PREFIX_CLS } from '../../common/constants';
import type { ChatContextType, FilterItemType } from '../../common/type';
import { queryDimensionValues } from '../../service';
import { debounce, isArray } from 'lodash';
import SwitchEntity from './SwitchEntity.vue';
import dayjs from 'dayjs';
import { Select as ASelect, InputNumber as AInputNumber, DatePicker as ADatePicker, Spin as ASpin } from 'ant-design-vue';
import type { SelectValue } from 'ant-design-vue/es/select';

// 定义Props
const props = defineProps({
  modelId: {
    type: Number,
    required: true
  },
  filters: {
    type: Array as () => FilterItemType[],
    required: true
  },
  filter: {
    type: Object as () => FilterItemType,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  chatContext: {
    type: Object as () => ChatContextType,
    required: true
  },
  agentId: {
    type: Number,
    default: undefined
  },
  disabled: {
    type: Boolean,
    default: false
  },
  entityAlias: {
    type: String,
    default: undefined
  },
  integrateSystem: {
    type: String,
    default: undefined
  },
  onFiltersChange: {
    type: Function as unknown as () => (filters: FilterItemType[]) => void,
    required: true
  },
  onSwitchEntity: {
    type: Function as unknown as () => (entityId: string) => void,
    required: true
  }
});

// 内部状态
const options = ref<{ label: string; value: string | null }[]>([]);
const loading = ref(false);
const fetchId = ref(0);
const prefixCls = `${PREFIX_CLS}-filter-item`;

// 计算值判断
const isNumberValue = computed(() => typeof props.filter.value === 'number' || props.filter.value === null);
const isStringValue = computed(() => typeof props.filter.value === 'string');
const isArrayValue = computed(() => isArray(props.filter.value));
const isIdField = computed(() => props.filter.bizName?.includes('_id'));
const isValidDateString = computed(() => 
  isStringValue.value && dayjs(props.filter.value, 'YYYY-MM-DD').isValid()
);

// 计算是否显示操作符选择框
const showOperatorSelect = computed(() => 
  (isNumberValue.value || props.filter.value === null || 
   (props.filter.operator && !['IN', '=', 'LIKE'].includes(props.filter.operator))) &&
  !isIdField.value
);

// 过滤后的选择项
const selectOptions = computed(() => 
  options.value.filter(option => option.value !== '' && option.value !== null)
);

// 操作符选项
const operatorOptions = [
  { label: '大于等于', value: '>=' },
  { label: '大于', value: '>' },
  { label: '等于', value: '=' },
  { label: '小于等于', value: '<=' },
  { label: '小于', value: '<' },
];

// 初始化数据
const initData = async () => {
  try {
    const { data } = await queryDimensionValues(
      props.modelId,
      props.filter.bizName,
      props.agentId!,
      props.filter.elementID,
      ''
    );
    
    options.value = data?.resultList?.map((item: any) => ({
      label: item[props.filter.bizName],
      value: item[props.filter.bizName],
    })) || [];
  } catch (error) {
    console.error('Failed to fetch dimension values:', error);
  }
};

// 加载选项
const loadOptions = (value: string) => {
  fetchId.value += 1;
  const currentFetchId = fetchId.value;
  options.value = [];
  loading.value = true;
  
  queryDimensionValues(
    props.modelId,
    props.filter.bizName,
    props.agentId!,
    props.filter.elementID,
    value
  ).then(response => {
    if (currentFetchId !== fetchId.value) {
      return;
    }
    
    options.value = response.data?.resultList.map((item: any) => ({
      label: item[props.filter.bizName],
      value: item[props.filter.bizName],
    })) || [];
    
    loading.value = false;
  }).catch(error => {
    console.error('Failed to fetch dimension values:', error);
    loading.value = false;
  });
};

// 防抖处理
const debounceFetcher = debounce(loadOptions, 500);

// 处理操作符改变
const onOperatorChange = (value: string) => {
  const newFilters = props.filters.map((item, indexValue) => {
    if (item.bizName === props.filter.bizName && props.index === indexValue) {
      item.operator = value;
    }
    return item;
  });
  
  props.onFiltersChange(newFilters);
};

// 处理值改变
const onChange = (value: string | string[] | number | null) => {
  const newFilters = props.filters.map((item, indexValue) => {
    if (item.bizName === props.filter.bizName && props.index === indexValue) {
      if (isArray(value)) {
        const arrayValue = value as string[];
        if (arrayValue.length === 1) {
          item.operator = '=';
          item.value = arrayValue[0];
        } else {
          item.operator = 'IN';
          item.value = arrayValue;
        }
      } else {
        item.value = typeof props.filter.value === 'number' || props.filter.value === null 
          ? value 
          : `${value}`;
      }
    }
    return item;
  });
  
  props.onFiltersChange(newFilters);
};

// 处理日期改变
const onDateChange = (_: any, dateString: string | string[]) => {
  const newFilters = props.filters.map((item, indexValue) => {
    if (item.bizName === props.filter.bizName && props.index === indexValue) {
      item.value = dateString;
    }
    return item;
  });
  
  props.onFiltersChange(newFilters);
};

// 处理数值输入框的值变化
const handleInputNumberChange = (value: number | null) => {
  onChange(value);
};

// 生命周期
onMounted(() => {
  if (
    (isStringValue.value || isArrayValue.value) &&
    options.value.length === 0 &&
    props.integrateSystem !== 'showcase'
  ) {
    initData();
  }
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
