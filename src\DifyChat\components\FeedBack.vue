<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}-feedback`">
      <div>这个回答正确吗?</div>
      <div :class="`${prefixCls}-feedback-left`">
        <LikeOutlined
          :class="likeClass"
          @click="like"
          style="margin-right: 10px"
        />
        <DislikeOutlined :class="dislikeClass" @click="dislike" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { DislikeOutlined, LikeOutlined } from "@ant-design/icons-vue";
import { CLS_PREFIX } from "@/common/constants";
import { updateFeedback } from "@/DifyChat/service/index";

// 定义组件的props
const props = defineProps({
  isLastMessage: {
    type: Boolean,
    default: false,
  },
  onReExecute: {
    type: Function,
    default: () => {},
  },
  currentAgent: {
    type: Object,
    default: () => {},
  },
  messageId: {
    type: String,
    default: "",
  },
  feedback: {
    type: Object,
    default: () => {},
  },
});

const prefixCls = `${CLS_PREFIX}-tools`;
const rating = ref<string | null>(null);

const like = async () => {
  let ratingValue: string | null = "like";
  if (rating.value === "like") {
    ratingValue = null;
  }

  const res: any = await updateFeedback({
    chartId: props.currentAgent.chartId,
    messageId: props.messageId,
    rating: ratingValue,
  });

  if (res.ok) {
    rating.value = ratingValue;
  }
};

const dislike = async () => {
  let ratingValue: string | null = "dislike";
  if (rating.value === "dislike") {
    ratingValue = null;
  }

  const res: any = await updateFeedback({
    chartId: props.currentAgent.chartId,
    messageId: props.messageId,
    rating: ratingValue,
  });

  if (res.ok) {
    rating.value = ratingValue;
  }
};

const likeClass = computed(() => {
  return {
    [`${prefixCls}-like`]: true,
    [`${prefixCls}-feedback-active`]: rating.value === "like",
  };
});

const dislikeClass = computed(() => {
  return {
    [`${prefixCls}-dislike`]: true,
    [`${prefixCls}-feedback-active`]: rating.value === "dislike",
  };
});

onMounted(() => {
  rating.value = props.feedback?.rating;
});
</script>

<style lang="less" scoped>
@import "../../styles/index.less";
@tools-cls: ~"@{supersonic-chat-prefix}-tools";

.@{tools-cls} {
  display: flex;
  align-items: center;
  margin-top: 6px;
  column-gap: 6px;

  &-feedback {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    width: 100%;
    padding: 0 10px 0 46px;
    // color: var(--text-color-third);
    column-gap: 6px;
    font-size: 12px;
    color: #999;
  }

  &-like {
    // margin-right: 4px;
  }

  &-feedback-active {
    color: rgb(234, 197, 79);
  }

  &-mobile-tools {
    display: flex;
    flex-direction: column;
    margin-top: 12px;
    row-gap: 10px;
  }

  &-tools {
    margin-top: 0;
  }

  &-feedback {
    margin-left: 2px;
  }

  &-feedback-item {
    display: flex;
    align-items: flex-start;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  &-feedback-item-title {
    width: 40px;
    margin-right: 20px;
    text-align: right;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
  }

  &-font-style {
    font-size: 12px;
    color: grey;
    font-style: italic;
    text-align: center;
  }
}
</style>
