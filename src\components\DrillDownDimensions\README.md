# DrillDownDimensions 组件

下钻维度组件，用于显示和选择维度进行数据下钻操作。

## 功能特点

- 支持显示推荐的下钻维度
- 支持选择一级和二级下钻维度
- 可配置显示的维度数量，超出部分可通过"更多"下拉菜单查看
- 提供维度的选择和取消功能

## 组件结构

该组件包含两个主要部分：

1. `DrillDownDimensions` - 主组件，用于管理维度数据和状态
2. `DimensionSection` - 子组件，用于渲染单个维度区域（一级或二级）

## 使用方法

```vue
<template>
  <DrillDownDimensions
    :drillDownDimensions="dimensions"
    :drillDownDimension="selectedDimension"
    :secondDrillDownDimension="selectedSecondDimension"
    :originDimensions="originDimensions"
    :dimensionFilters="filters"
    :onSelectDimension="handleSelectDimension"
    :onSelectSecondDimension="handleSelectSecondDimension"
  />
</template>

<script setup>
import { ref } from 'vue';
import DrillDownDimensions from './components/DrillDownDimensions/index.vue';
import type { DrillDownDimensionType } from './common/type';

const dimensions = ref<DrillDownDimensionType[]>([
  { id: 1, name: '维度1', bizName: 'dimension1', model: 1 },
  { id: 2, name: '维度2', bizName: 'dimension2', model: 1 },
  // ...更多维度
]);

const selectedDimension = ref<DrillDownDimensionType | undefined>();
const selectedSecondDimension = ref<DrillDownDimensionType | undefined>();

const handleSelectDimension = (dimension?: DrillDownDimensionType) => {
  selectedDimension.value = dimension;
  // 其他处理逻辑...
};

const handleSelectSecondDimension = (dimension?: DrillDownDimensionType) => {
  selectedSecondDimension.value = dimension;
  // 其他处理逻辑...
};
</script>
```

## Props

### DrillDownDimensions

| 属性名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| drillDownDimensions | DrillDownDimensionType[] | 可用的下钻维度列表 | 是 |
| drillDownDimension | DrillDownDimensionType | 当前选中的一级下钻维度 | 否 |
| secondDrillDownDimension | DrillDownDimensionType | 当前选中的二级下钻维度 | 否 |
| originDimensions | DrillDownDimensionType[] | 原始维度列表，用于过滤已存在维度 | 否 |
| dimensionFilters | FilterItemType[] | 维度过滤条件 | 否 |
| onSelectDimension | (dimension?: DrillDownDimensionType) => void | 选择一级维度的回调函数 | 是 |
| onSelectSecondDimension | (dimension?: DrillDownDimensionType) => void | 选择二级维度的回调函数 | 是 |

### DimensionSection

| 属性名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| drillDownDimension | DrillDownDimensionType | 当前选中的下钻维度 | 否 |
| dimensions | DrillDownDimensionType[] | 可用的下钻维度列表 | 是 |
| isSecondDrillDown | boolean | 是否为二级下钻 | 否 |
| onSelectDimension | (dimension?: DrillDownDimensionType) => void | 选择维度的回调函数 | 是 |
| onCancelDrillDown | () => void | 取消下钻的回调函数 | 是 |

## 样式定制

组件使用 CSS 变量来定义样式，主要包括：

- `--text-color-third` - 标题和取消按钮的颜色
- `--chat-blue` - 主要突出颜色，用于维度名称和选中状态

您可以在应用中覆盖这些变量来自定义组件外观。 