<template>
  <div v-if="defaultDimensions.length > 0" :class="`${prefixCls}-section`">
    <div :class="`${prefixCls}-title`">{{ isSecondDrillDown ? '二级' : '推荐' }}下钻维度：</div>
    <div :class="`${prefixCls}-content`">
      <div v-for="(dimension, index) in defaultDimensions" :key="dimension.id">
        <span
          :class="{
            [`${prefixCls}-content-item-name`]: true,
            [`${prefixCls}-content-item-active`]: drillDownDimension?.id === dimension.id
          }"
          @click="handleDimensionClick(dimension)"
        >
          {{ dimension.name }}
        </span>
        <span v-if="index !== defaultDimensions.length - 1">、</span>
      </div>
      <div v-if="dimensions.length > DEFAULT_DIMENSION_COUNT">
        <span>、</span>
        <a-dropdown>
          <span>
            <span :class="`${prefixCls}-content-item-name`">更多</span>
            <down-outlined :class="`${prefixCls}-down-arrow`" />
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item 
                v-for="dimension in dimensions.slice(DEFAULT_DIMENSION_COUNT)" 
                :key="dimension.id"
              >
                <span
                  :class="{ [`${prefixCls}-menu-item-active`]: drillDownDimension?.id === dimension.id }"
                  @click="() => onSelectDimension(dimension)"
                >
                  {{ dimension.name }}
                </span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <div 
        v-if="drillDownDimension" 
        :class="`${prefixCls}-cancel-drill-down`" 
        @click="onCancelDrillDown"
      >
        取消{{ isSecondDrillDown ? '二级' : '' }}下钻
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { DownOutlined } from '@ant-design/icons-vue';
import { Dropdown as ADropdown, Menu as AMenu, MenuItem as AMenuItem } from 'ant-design-vue';
import { CLS_PREFIX } from '../../common/constants';
import type { DrillDownDimensionType } from '../../common/type';

const DEFAULT_DIMENSION_COUNT = 5;

interface Props {
  drillDownDimension?: DrillDownDimensionType;
  dimensions: DrillDownDimensionType[];
  isSecondDrillDown?: boolean;
  onSelectDimension: (dimension?: DrillDownDimensionType) => void;
  onCancelDrillDown: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  isSecondDrillDown: false,
  drillDownDimension: undefined
});

const prefixCls = `${CLS_PREFIX}-drill-down-dimensions`;

const defaultDimensions = computed(() => {
  return props.dimensions.slice(0, DEFAULT_DIMENSION_COUNT);
});

const handleDimensionClick = (dimension: DrillDownDimensionType) => {
  props.onSelectDimension(
    props.drillDownDimension?.id === dimension.id ? undefined : dimension
  );
};

const onCancelDrillDown = () => {
  props.onCancelDrillDown();
};
</script>

<style scoped>
/* 样式会从 DrillDownDimensions/index.vue 导入 */
</style> 