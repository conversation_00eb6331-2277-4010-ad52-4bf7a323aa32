<template>
  <div :key="id" class="answer-wrapper">
    <!-- <Bubble :content="content" /> -->
    <!-- <IconFont type="icon-zhinengsuanfa" class="answerAvatar" /> -->

    <div class="answer-content-wrapper">
      <img v-once :src="avatarSrc" class="answerAvatar" />
      <Typing v-if="!content && !workflowProcess" />
      <Message v-else position="left">
        <WorkflowProcess
          v-if="workflowProcess"
          :data="workflowProcess"
          class="workflow-process"
        />

        <Markdown :markdown="content" />

        <div v-if="content && suggested_questions?.length">
          <div class="suggested-questions-container">
            <div
              class="suggested-question"
              v-for="(question, index) in suggested_questions"
              :key="index"
              @click="handleSuggestedQuestion(question)"
            >
              <span>{{ question }}</span>
            </div>
          </div>
        </div>

        <!-- 引用 -->
        <div v-if="retriever_resources?.length" class="reference-container">
          <Divider orientation="left" style="margin: 0" orientation-margin="0">
            <div class="reference-title">引用</div>
          </Divider>
          <div class="reference-list">
            <div
              v-for="resource in retriever_resources"
              :key="resource.fileUrl"
              class="reference-item"
            >
              <a
                :href="resource.fullUrl"
                target="_blank"
                class="reference-link"
              >
                <span class="reference-icon">
                  <FileTextTwoTone />
                </span>
                <span class="reference-name">{{ resource.document_name }}</span>
              </a>
            </div>
          </div>
        </div>
      </Message>
    </div>

    <div v-if="!isOpening && !feedbackDisabled">
      <FeedBack
        :currentAgent="currentAgent"
        :messageId="id"
        :feedback="feedback"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, reactive } from "vue";
import type { ChatItem } from "../types/app";
import Markdown from "@/components/ChatMsg/MarkDown/index.vue";
import Message from "@/Chat/components/Message/index.vue";
import Typing from "@/components/ChatItem/Typing.vue";
import { API_PREFIX, KK_SERVER_URL, KK_LOCAL_URL } from "@/config/config";
import type { ClientAgentType } from "@/types/client";
import { Base64 } from "js-base64";
import { FileTextTwoTone } from "@ant-design/icons-vue";
import { Divider } from "ant-design-vue";
import FeedBack from "./FeedBack.vue";
import { adaptationUrl } from "@/config/config";
import { flow } from "lodash";

import WorkflowProcess from "@/DifyChat/components/WorkflowProcess/WorkflowProcess.vue";

interface Props {
  item: ChatItem;
  feedbackDisabled: boolean;
  isResponding?: boolean;
  currentAgent: ClientAgentType;
}

const props = withDefaults(defineProps<Props>(), {
  feedbackDisabled: false,
  isResponding: false,
});

const emit = defineEmits<{
  (e: "suggestedQuestion", question: string): void;
}>();

// 使用reactive创建深度响应式对象来跟踪item
const itemData = reactive<ChatItem>({ ...props.item });

const preprocessThinkTag = (content: string) => {
  const thinkOpenTagRegex = /<think>\n/g;
  const thinkCloseTagRegex = /\n<\/think>/g;

  // 检查内容中是否包含结束标签
  const hasEndTag = content.includes("</think>");

  return flow([
    (str: string) =>
      str.replace(
        thinkOpenTagRegex,
        `<details class="think-container" open>\n <summary>${hasEndTag ? "已完成深度思考" : "深度思考中"}</summary>\n`
      ),
    (str: string) => str.replace(thinkCloseTagRegex, "\n</details>"),
  ])(content);
};

const retriever_resources = computed(() => {
  return itemData?.retriever_resources?.map(resource => {
    const fileName = resource.fileUrl.split("?fileName=")[1];

    resource.fullUrl =
      `${KK_SERVER_URL}?url=` +
      encodeURIComponent(
        Base64.encode(
          `${KK_LOCAL_URL}${adaptationUrl(resource.fileUrl)}&fullfilename=${fileName}`
        )
      );

    return {
      ...resource,
    };
  });
});

const suggested_questions = computed(() => {
  return itemData?.suggested_questions || [];
});

// 监听props.item的变化，更新itemData
watch(
  () => props.item,
  newItem => {
    // 深度拷贝更新整个对象
    Object.assign(itemData, newItem);
  },
  { deep: true, immediate: true }
);

// 从响应式对象中提取需要的属性
const id = computed(() => itemData.id);
const content = computed(() => preprocessThinkTag(itemData.content));
// const agentThoughts = computed(() => itemData.agent_thoughts);
const workflowProcess = computed(() => itemData.workflowProcess);
const feedback = computed(() => itemData.feedback);

// const isAgentMode = computed(
//   () => !!agentThoughts.value && agentThoughts.value.length > 0
// );

const avatarSrc = computed(() => {
  return `${API_PREFIX}${props.currentAgent?.chartIcon}`;
});

const isOpening = computed(() => {
  return itemData.id.startsWith("opening-");
});

const handleSuggestedQuestion = (question: string) => {
  console.log("question", question);
  emit("suggestedQuestion", question);
};

// 打印日志，便于调试
// watch(content, newVal => {
//   console.log("content更新:", newVal);
// });
</script>

<style lang="less" scoped>
@import "../../styles/index.less";

.answer-wrapper {
  max-width: 80%;
}

.answer-content-wrapper {
  display: flex;
}

.answerAvatar {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  width: 40px;
  height: 40px;
  margin-right: 6px;
  border-radius: 4px;
  color: var(--chat-blue);
  background-color: #fff;
}

:deep(.vuepress-markdown-body) {
  padding: 0 !important;
  font-size: 14px !important;
}
:deep(.ss-chat-markdown) {
  margin-top: 0 !important;
}

.reference-container {
  margin-top: 8px;
  padding: 8px 0;
  font-size: 13px;
}

.reference-title {
  color: #999;
  font-size: 12px;
  margin-bottom: 6px;
  font-weight: normal;
}

.reference-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.reference-item {
  margin: 0;
}

.reference-link {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  text-decoration: none;
  font-size: 12px;
  line-height: 1.4;

  &:hover {
    color: #1a73e8;
    .reference-name {
      color: inherit;
    }
    .reference-icon {
      color: inherit;
    }
  }
}

.reference-icon {
  color: #999;
  display: flex;
  align-items: center;
  transition: color 0.2s ease;
}

.reference-name {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.2s ease;
}

.suggested-questions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.suggested-question {
  background-color: #f5f7f9;
  border-radius: 6px;
  padding: 5px 12px;
  font-size: 13px;
  color: #1a73e8;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e8edf3;
  max-width: 100%;
  line-height: 13px;

  &:hover {
    background-color: #e8f0fe;
    border-color: #d2e3fc;
  }

  span {
    display: inline-block;
    white-space: nowrap;
    // overflow: hidden;
    text-overflow: ellipsis;
  }
}

.workflow-process {
  margin-bottom: 8px;
}

.answerIcon {
  // Add your styles
}

.typeingIcon {
  // Add your styles
}

.answerWrap {
  // Add your styles
}

.answer {
  // Add your styles
}
</style>
