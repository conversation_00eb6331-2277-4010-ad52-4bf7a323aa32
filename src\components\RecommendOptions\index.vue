<template>
  <div :class="recommendOptionsClass">
    <a-table
      row-key="id"
      :columns="columns"
      :data-source="data"
      :show-header="!isMobile"
      size="small"
      :pagination="false"
      :loading="loading"
      :class="`${prefixCls}-table`"
      :row-class-name="`${prefixCls}-table-row`"
      @row-click="record => onSelect(record.id)"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.key === 'name'">
          <div :class="`${prefixCls}-item-name-column`">
            <a-avatar shape="square" :src="record.url">
              <template #icon>
                <span :class="getIconType()"></span>
              </template>
            </a-avatar>
            <div :class="`${prefixCls}-entity-name`">
              {{ text }}
              <template
                v-if="
                  record.ver && record.ver !== '完整版' && record.ver !== '-'
                "
              >
                ({{ record.ver }})
              </template>
              <template v-if="record.singerName">
                - {{ record.singerName }}
              </template>
            </div>
          </div>
        </template>
        <template v-else-if="column.key === 'onlineSongCnt'">
          {{ text ? getFormattedValue(+text) : "-" }}
        </template>
        <template v-else-if="column.key === 'publishTime'">
          {{ text ? dayjs(text).format("YYYY-MM-DD") : "-" }}
        </template>
        <template
          v-else-if="
            column.key === 'tme3platAvgLogYyPlayCnt' ||
            column.key === 'tme3platJsPlayCnt'
          "
        >
          {{ text ? getFormattedValue(+text) : "-" }}
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, watch } from "vue";
import { getFormattedValue, isMobile } from "../../utils/utils";
import { Table as ATable, Avatar as AAvatar } from "ant-design-vue";
import type { ColumnsType } from "ant-design-vue/es/table";
import dayjs from "dayjs";
import { queryEntities } from "../../service";
import { CLS_PREFIX } from "../../common/constants";

export default defineComponent({
  name: "RecommendOptions",
  components: {
    ATable,
    AAvatar,
  },
  props: {
    entityId: {
      type: [String, Number],
      required: true,
    },
    modelId: {
      type: Number,
      required: true,
    },
    modelName: {
      type: String,
      required: true,
    },
    onSelect: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const data = ref<any[]>([]);
    const loading = ref(false);
    const prefixCls = `${CLS_PREFIX}-recommend-options`;

    const getIconType = () => {
      return props.modelName === "艺人库" ? "icon-geshou" : "icon-zhuanji";
    };

    const initData = async () => {
      loading.value = true;
      try {
        const res = await queryEntities(props.entityId, props.modelId);
        data.value = res.data;
      } finally {
        loading.value = false;
      }
    };

    watch(
      () => props.entityId,
      newValue => {
        if (newValue) {
          initData();
        }
      }
    );

    onMounted(() => {
      if (props.entityId) {
        initData();
      }
    });

    const columns = computed(() => {
      const basicColumn = {
        dataIndex: "name",
        key: "name",
        title: "基本信息",
      };

      const playCntColumnIdex = props.modelName.includes("歌曲")
        ? "tme3platAvgLogYyPlayCnt"
        : "tme3platJsPlayCnt";

      if (isMobile) {
        return [basicColumn] as ColumnsType;
      } else {
        const artistColumn = {
          dataIndex: "onlineSongCnt",
          key: "onlineSongCnt",
          title: "在架歌曲数",
          align: "center",
        };

        const songColumn = {
          dataIndex: "publishTime",
          key: "publishTime",
          title: "发布时间",
          align: "center",
        };

        const playCntColumn = {
          dataIndex: playCntColumnIdex,
          key: playCntColumnIdex,
          align: "center",
          title: props.modelName.includes("歌曲")
            ? "近7天日均运营播放量"
            : "昨日结算播放量",
        };

        return [
          basicColumn,
          props.modelName.includes("艺人") ? artistColumn : songColumn,
          playCntColumn,
        ] as ColumnsType;
      }
    });

    const recommendOptionsClass = computed(() => {
      return {
        [prefixCls]: true,
        [`${prefixCls}-mobile-mode`]: isMobile,
      };
    });

    return {
      data,
      loading,
      prefixCls,
      columns,
      recommendOptionsClass,
      isMobile,
      getFormattedValue,
      dayjs,
      getIconType,
    };
  },
});
</script>

<style lang="less">
@import "../../styles/index.less";

@supersonic-chat-prefix: ss-chat;
@recommend-options-prefix-cls: ~"@{supersonic-chat-prefix}-recommend-options";

.@{recommend-options-prefix-cls} {
  padding: 8px 0 12px;

  &-item-name-column {
    display: flex;
    align-items: center;
    column-gap: 6px;
  }

  &-entity-name {
    &:hover {
      color: var(--primary-color);
    }
  }

  &-table-row {
    cursor: pointer;
  }
}
</style>
