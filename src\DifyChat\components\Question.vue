<template>
  <div class="question-container">
    <div class="question-wrapper">
      <div class="content-wrapper">
        <Bubble
          placement="end"
          :content="content"
          class="question-content-bubble"
        >
          <template #avatar v-if="useCurrentUserAvatar">
            <view class="avatar-wrapper">
              <img
                v-if="userAvatar"
                :src="API_PREFIX + userAvatar"
                class="avatar"
                alt="user avatar"
              />
              <div v-else class="anonymous-avatar">
                <UserOutlined />
              </div>
            </view>
          </template>
        </Bubble>

        <div style="padding-right: 40px">
          <Flex vertical gap="8" class="file-list">
            <Attachments.FileCard
              v-for="(file, index) in messageFiles"
              :key="index"
              :item="file"
            />
          </Flex>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Bubble, Attachments } from "ant-design-x-vue";
import { UserOutlined } from "@ant-design/icons-vue";
import { Flex } from "ant-design-vue";
import { API_PREFIX, DIFY_API_PREFIX } from "@/config/config";
import type { ChatItem, UploadFile, VisionFile } from "../types/app";
import { computed, toRefs } from "vue";

interface Props {
  id: string | number;
  content: string;
  useCurrentUserAvatar?: boolean;
  userAvatar?: string;
  item: ChatItem;
}

const props = withDefaults(defineProps<Props>(), {
  useCurrentUserAvatar: true,
});

const { item } = toRefs(props);

// 判断id是否为question-${Date.now()}格式
const isLocalQuestionItem = computed(() => {
  return (
    item.value.id.startsWith("question-") &&
    /^\d+$/.test(item.value.id.substring(9))
  );
});

const messageFiles = computed(() => {
  if (!item.value.message_files?.length) return [];

  if (isLocalQuestionItem.value) {
    return item.value.message_files.map(file => {
      const uploadFile = file as UploadFile;
      return {
        name: uploadFile.name,
        uid: uploadFile.uid,
        originFileObj: uploadFile.originFileObj,
      };
    });
  } else {
    return item.value.message_files.map(file => {
      const visionFile = file as VisionFile;
      return {
        name: visionFile.filename,
        uid: visionFile.id,
        url: `${DIFY_API_PREFIX}/${visionFile.url}`,
        thumbUrl: `${DIFY_API_PREFIX}/${visionFile.url}`,
      };
    });
  }
});
</script>

<style lang="less" scoped>
.question-container {
  width: 100%;
  margin: 20px 0;
  display: flex;
  justify-content: flex-end;
}

.question-wrapper {
  max-width: 90%;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.avatar-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;

  .avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .default-avatar {
    width: 100%;
    height: 100%;
    background: #1890ff;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
  }

  .anonymous-avatar {
    width: 100%;
    height: 100%;
    background: #ccc;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 20px;
  }
}

.content-wrapper {
  flex: 1;
  min-width: 0;
}

.file-list {
  margin-top: 8px;
}

:deep(.question-content-bubble) {
  .ant-bubble-content {
    float: right;
    box-sizing: border-box;
    padding: 9px 16px;
    color: #fff;
    font-size: 14px;
    background: linear-gradient(
      81.62deg,
      #2870ea 8.72%,
      var(--chat-blue) 85.01%
    );
    border: 1px solid transparent;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.14),
      0 0 2px rgba(0, 0, 0, 0.12);
    min-height: 42px;
  }
}

@media screen and (max-width: 768px) {
  .question-wrapper {
    max-width: 100%;
  }

  .question-content {
    font-size: 13px;
  }
}
</style>
