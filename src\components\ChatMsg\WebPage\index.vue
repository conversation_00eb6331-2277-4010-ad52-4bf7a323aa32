<template>
  <iframe
    :id="`reportIframe_${id}`"
    :name="`reportIframe_${id}`"
    :src="pluginUrl"
    :style="iframeStyle"
    title="reportIframe"
    allowfullscreen
  />
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import type { MsgDataType } from '../../../common/type';
import { getToken, isMobile, isProd } from '../../../utils/utils';
import { webPageHost } from '../../../common/env';

// 组件属性定义
const props = defineProps<{
  id: string | number;
  data: MsgDataType;
}>();

const DEFAULT_HEIGHT = 800;
const pluginUrl = ref('');
const height = ref(DEFAULT_HEIGHT);

// iframe样式计算属性
const iframeStyle = computed(() => ({
  width: isMobile ? 'calc(100vw - 20px)' : 'calc(100vw - 410px)',
  height: `${height.value}px`,
  border: 'none'
}));

// 从props.data提取response数据
const { name, webPage } = props.data.response || {};
const { url, params } = webPage || { url: '', params: [] };

// 计算iframe页面高度
function calcPageHeight(doc: any) {
  const titleAreaEl = doc.getElementById('titleArea');
  const titleAreaHeight = Math.max(
    titleAreaEl?.clientHeight || 0,
    titleAreaEl?.scrollHeight || 0
  );
  const dashboardGridEl = doc.getElementsByClassName('dashboardGrid')?.[0];
  const dashboardGridHeight = Math.max(
    dashboardGridEl?.clientHeight || 0,
    dashboardGridEl?.scrollHeight || 0
  );
  return Math.max(titleAreaHeight + dashboardGridHeight + 10, DEFAULT_HEIGHT);
}

// 处理iframe通信消息
function handleMessage(event: MessageEvent) {
  const messageData = event.data;
  
  if (typeof messageData === 'object' && messageData !== null) {
    const { type, payload } = messageData;
    if (type === 'changeMiniProgramContainerSize') {
      const { msgId, height: newHeight } = payload;
      if (`${msgId}` === `${props.id}`) {
        height.value = newHeight;
      }
      return;
    }
  }
  
  if (messageData === 'storyResize') {
    const ifr: any = document.getElementById(`reportIframe_${props.id}`);
    const iDoc = ifr?.contentDocument || ifr?.document || ifr?.contentWindow;
    setTimeout(() => {
      height.value = isProd() ? calcPageHeight(iDoc) : DEFAULT_HEIGHT;
    }, 200);
    return;
  }
}

// 初始化数据
function initData() {
  if (!url || !params) return;
  
  const heightValue = params.find((option: any) => 
    option.paramType === 'FORWARD' && option.key === 'height'
  )?.value || DEFAULT_HEIGHT;
  
  height.value = heightValue;
  let urlValue = url;
  
  const valueParams = (params || [])
    .filter((option: any) => option.paramType !== 'FORWARD')
    .reduce((result: any, item: any) => {
      result[item.key] = item.value;
      return result;
    }, {});
    
  if (urlValue.includes('?type=dashboard') || urlValue.includes('?type=widget')) {
    const filterData = encodeURIComponent(
      JSON.stringify(
        urlValue.includes('dashboard')
          ? { global: valueParams }
          : { local: valueParams }
      )
    );
    
    urlValue = urlValue.replace(
      '?',
      `?token=${getToken()}&miniProgram=true&reportName=${name}&filterData=${filterData}&`
    );
    urlValue = `${webPageHost}${urlValue}`;
  } else {
    const paramStrings = Object.keys(valueParams || {}).map(key => `${key}=${valueParams[key]}`);
    if (paramStrings.length > 0) {
      if (url.includes('?')) {
        urlValue = urlValue.replace('?', `?${paramStrings.join('&')}&`);
      } else {
        urlValue = `${urlValue}?${paramStrings.join('&')}`;
      }
    }
  }
  
  pluginUrl.value = urlValue;
}

// 生命周期钩子
onMounted(() => {
  window.addEventListener('message', handleMessage);
  initData();
});

onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
});
</script>
