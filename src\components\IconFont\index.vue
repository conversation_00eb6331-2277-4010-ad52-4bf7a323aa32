<template>
  <component :is="Icon" />
</template>

<script setup lang="ts">
import { createVNode } from "vue";
import { createFromIconfontCN } from "@ant-design/icons-vue";

const IconFont = createFromIconfontCN({
  scriptUrl: "/assets/sdkfonts/font_4120566_7rwv3aw6wep.js",
  // at.alicdn.com/t/c/font_4120566_7rwv3aw6wep.js
});

// 定义props
const props = defineProps<{
  type: string;
  class?: string;
  style?: string;
}>();

// 创建图标组件
const Icon = createVNode(IconFont, {
  type: props.type,
  class: props.class,
  style: props.style,
});
</script>
