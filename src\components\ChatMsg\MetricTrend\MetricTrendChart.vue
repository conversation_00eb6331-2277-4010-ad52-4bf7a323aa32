<template>
  <div>
    <NoPermissionChart 
      v-if="!metricField.authorized" 
      :model="model || ''" 
      @apply-auth="onApplyAuth" 
    />
    <div 
      v-else 
      :class="flowTrendChartClass" 
      ref="chartRef"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, inject } from 'vue';
import { CHART_SECONDARY_COLOR, CLS_PREFIX, THEME_COLOR_LIST } from '../../../common/constants';
import {
  formatByDecimalPlaces,
  getFormattedValue,
  getMinMaxDate,
  groupByColumn,
  normalizeTrendData,
} from '../../../utils/utils';
import * as echarts from 'echarts';
import type { ECharts } from 'echarts';
import dayjs from 'dayjs';
import type { ColumnType } from '../../../common/type';
import NoPermissionChart from '../NoPermissionChart/index.vue';
import classNames from 'classnames';
import { isArray } from 'lodash';

const props = defineProps<{
  model?: string;
  dateColumnName: string;
  categoryColumnName: string;
  metricField: ColumnType;
  resultList: any[];
  triggerResize?: boolean;
  chartType?: string;
}>();

const emit = defineEmits<{
  (e: 'applyAuth', model: string): void;
}>();

const prefixCls = `${CLS_PREFIX}-metric-trend`;
const chartRef = ref<HTMLElement | null>(null);
const instanceRef = ref<ECharts>();

// 计算样式类名
const flowTrendChartClass = computed(() => 
  classNames(`${prefixCls}-flow-trend-chart`, {
    [`${prefixCls}-flow-trend-chart-single`]: !props.categoryColumnName,
  })
);

// 渲染图表
const renderChart = () => {
  if (!chartRef.value) return;
  
  let instanceObj: any;
  if (!instanceRef.value) {
    instanceObj = echarts.init(chartRef.value);
    instanceRef.value = instanceObj;
  } else {
    instanceObj = instanceRef.value;
    instanceObj.clear();
  }

  const valueColumnName = props.metricField.bizName;
  const dataSource = props.resultList.map((item: any) => {
    return {
      ...item,
      [props.dateColumnName]: Array.isArray(item[props.dateColumnName])
        ? dayjs(item[props.dateColumnName].join('')).format('MM-DD')
        : item[props.dateColumnName],
    };
  });

  const groupDataValue = groupByColumn(dataSource, props.categoryColumnName);
  const [startDate, endDate] = getMinMaxDate(dataSource, props.dateColumnName);
  const groupData = Object.keys(groupDataValue).reduce((result: any, key) => {
    result[key] =
      startDate &&
      endDate &&
      (props.dateColumnName.includes('date') || props.dateColumnName.includes('month'))
        ? normalizeTrendData(
            groupDataValue[key],
            props.dateColumnName,
            valueColumnName,
            startDate,
            endDate,
            props.dateColumnName.includes('month') ? 'months' : 'days'
          )
        : groupDataValue[key];
    return result;
  }, {});

  const sortedGroupKeys = Object.keys(groupData).sort((a, b) => {
    return (
      groupData[b][groupData[b].length - 1][valueColumnName] -
      groupData[a][groupData[a].length - 1][valueColumnName]
    );
  });

  const xData = groupData[sortedGroupKeys[0]]?.map((item: any) => {
    const date = isArray(item[props.dateColumnName])
      ? item[props.dateColumnName].join('-')
      : `${item[props.dateColumnName]}`;
    return date.length === 10 ? dayjs(date).format('MM-DD') : date;
  });

  instanceObj.setOption({
    legend: props.categoryColumnName && {
      left: 0,
      top: 0,
      icon: 'rect',
      itemWidth: 15,
      itemHeight: 5,
      type: 'scroll',
    },
    xAxis: {
      type: 'category',
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: CHART_SECONDARY_COLOR,
        },
      },
      axisLine: {
        lineStyle: {
          color: CHART_SECONDARY_COLOR,
        },
      },
      axisLabel: {
        showMaxLabel: true,
        color: '#999',
      },
      data: xData,
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          opacity: 0.3,
        },
      },
      axisLabel: {
        formatter: function (value: any) {
          return value === 0
            ? 0
            : props.metricField.dataFormatType === 'percent'
            ? `${formatByDecimalPlaces(value, props.metricField.dataFormat?.decimalPlaces || 2)}%`
            : getFormattedValue(value);
        },
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any[]) {
        const param = params[0];
        const valueLabels = params
          .sort((a, b) => b.value - a.value)
          .map(
            (item: any) =>
              `<div style="margin-top: 3px;">${
                item.marker
              } <span style="display: inline-block; width: 70px; margin-right: 12px;">${
                item.seriesName
              }</span><span style="display: inline-block; width: 90px; text-align: right; font-weight: 500;">${
                item.value === ''
                  ? '-'
                  : props.metricField.dataFormatType === 'percent' ||
                    props.metricField.dataFormatType === 'decimal'
                  ? `${formatByDecimalPlaces(
                      item.value,
                      props.metricField.dataFormat?.decimalPlaces || 2
                    )}${props.metricField.dataFormatType === 'percent' ? '%' : ''}`
                  : getFormattedValue(item.value)
              }</span></div>`
          )
          .join('');
        return `${param.name}<br />${valueLabels}`;
      },
    },
    grid: {
      left: '1%',
      right: '4%',
      bottom: '3%',
      top: props.categoryColumnName ? 45 : 20,
      containLabel: true,
    },
    series: sortedGroupKeys.slice(0, 20).map((category, index) => {
      const data = groupData[category];
      const seriesType = props.chartType || 'line';
      return {
        type: seriesType,
        name: props.categoryColumnName ? category : props.metricField.name,
        symbol: seriesType === 'line' ? 'circle' : undefined,
        showSymbol: seriesType === 'line' && data.length === 1,
        smooth: seriesType === 'line',
        data: data.map((item: any) => {
          const value = item[valueColumnName];
          return (props.metricField.dataFormatType === 'percent' ||
            props.metricField.dataFormatType === 'decimal') &&
            props.metricField.dataFormat?.needMultiply100
            ? value * 100
            : value;
        }),
        color: THEME_COLOR_LIST[index],
      };
    }),
  });
  instanceObj.resize();
};

// 处理事件
const onApplyAuth = (model: string) => {
  emit('applyAuth', model);
};

// 暴露下载图表方法
const downloadChartAsImage = () => {
  if (instanceRef.value) {
    const dataURL = instanceRef.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff',
    });
    const link = document.createElement('a');
    link.download = `${props.metricField.name || '图表'}.png`;
    link.href = dataURL;
    link.click();
  }
};

// 注册到上下文
const register = inject('register', null) as ((name: string, fn: Function) => void) | null;
if (register) {
  register('downloadChartAsImage', downloadChartAsImage);
}

// 监听数据变化重新渲染图表
watch(
  [() => props.resultList, () => props.metricField, () => props.chartType],
  () => {
    if (props.metricField.authorized) {
      renderChart();
    }
  },
  { deep: true }
);

// 监听尺寸变化
watch(
  () => props.triggerResize,
  (newVal) => {
    if (newVal && instanceRef.value) {
      instanceRef.value.resize();
    }
  }
);

// 组件挂载时渲染图表
onMounted(() => {
  if (props.metricField.authorized) {
    renderChart();
  }
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
