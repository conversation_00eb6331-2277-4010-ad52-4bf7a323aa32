import type { <PERSON>a, StoryObj } from "@storybook/vue3";
import { fn } from "@storybook/test";

import SimilarQuestionItem from "./SimilarQuestionItem.vue";
import { SimilarQuestionType } from "../../common/type";

// 示例数据
const mockSimilarQuestions: SimilarQuestionType[] = [
  {
    queryId: 1,
    parseId: 101,
    queryText: "本月销售额与上月相比增长了多少？",
  },
  {
    queryId: 2,
    parseId: 102,
    queryText: "上季度的用户增长情况如何？",
  },
  {
    queryId: 3,
    parseId: 103,
    queryText: "哪个地区的转化率最高？",
  },
  {
    queryId: 4,
    parseId: 104,
    queryText: "最近30天内每日活跃用户的趋势是什么？",
  },
  {
    queryId: 5,
    parseId: 105,
    queryText: "营销活动的投资回报率是多少？",
  },
];

const meta = {
  title: "ChatItem/SimilarQuestionItem",
  component: SimilarQuestionItem,
  tags: ["autodocs"],
  argTypes: {
    queryId: {
      control: "number",
      description: "查询ID",
    },
    similarQueries: {
      control: "object",
      description: "相似问题列表",
    },
    defaultExpanded: {
      control: "boolean",
      description: "是否默认展开",
    },
    onSelectQuestion: {
      action: "select-question",
      description: "选择问题时的回调函数",
    },
  },
  parameters: {
    docs: {
      description: {
        component: "这是一个用于显示相似问题的组件",
      },
    },
  },
} satisfies Meta<typeof SimilarQuestionItem>;

export default meta;
type Story = StoryObj<typeof meta>;

// 默认状态 - 折叠
export const Default: Story = {
  args: {
    queryId: 1001,
    similarQueries: mockSimilarQuestions,
    defaultExpanded: true,
    onSelectQuestion: fn(),
  },
  render: args => ({
    components: { SimilarQuestionItem },
    setup() {
      return {
        args,
      };
    },
    template: `
      <div style="width: 600px; background-color: #f5f5f5; padding: 20px;">
        <SimilarQuestionItem 
          :query-id="args.queryId"
          :similar-queries="args.similarQueries"
          :default-expanded="args.defaultExpanded"
          :onSelectQuestion="args.onSelectQuestion"
        />
      </div>
    `,
  }),
};

// 展开状态
export const Expanded: Story = {
  args: {
    queryId: 1001,
    similarQueries: mockSimilarQuestions,
    defaultExpanded: true,
    onSelectQuestion: fn(),
  },
  render: args => ({
    components: { SimilarQuestionItem },
    setup() {
      return {
        args,
      };
    },
    template: `
      <div style="width: 600px; background-color: #f5f5f5; padding: 20px;">
        <SimilarQuestionItem 
          :query-id="args.queryId"
          :similar-queries="args.similarQueries"
          :default-expanded="args.defaultExpanded"
          :onSelectQuestion="args.onSelectQuestion"
        />
      </div>
    `,
  }),
};

// 无相似问题
export const NoSimilarQuestions: Story = {
  args: {
    queryId: 1001,
    similarQueries: [],
    defaultExpanded: true,
    onSelectQuestion: fn(),
  },
  render: args => ({
    components: { SimilarQuestionItem },
    setup() {
      return {
        args,
      };
    },
    template: `
      <div style="width: 600px; background-color: #f5f5f5; padding: 20px;">
        <SimilarQuestionItem 
          :query-id="args.queryId"
          :similar-queries="args.similarQueries"
          :default-expanded="args.defaultExpanded"
          :onSelectQuestion="args.onSelectQuestion"
        />
      </div>
    `,
  }),
};

// 无查询ID，但有预设的相似问题
export const WithoutQueryId: Story = {
  args: {
    similarQueries: mockSimilarQuestions,
    defaultExpanded: true,
    onSelectQuestion: fn(),
  },
  render: args => ({
    components: { SimilarQuestionItem },
    setup() {
      return {
        args,
      };
    },
    template: `
      <div style="width: 600px; background-color: #f5f5f5; padding: 20px;">
        <SimilarQuestionItem 
          :similar-queries="args.similarQueries"
          :default-expanded="args.defaultExpanded"
          :onSelectQuestion="args.onSelectQuestion"
        />
      </div>
    `,
  }),
};
