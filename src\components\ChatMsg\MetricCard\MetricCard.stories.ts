import { Meta, StoryObj } from '@storybook/vue3';
import MetricCard from './index.vue';
import { MsgDataType } from '../../../common/type';

const meta: Meta<typeof MetricCard> = {
  component: MetricCard,
  title: 'Components/ChatMsg/MetricCard',
  tags: ['autodocs'],
  argTypes: {
    question: {
      control: 'text',
      description: '指标问题'
    },
    loading: {
      control: 'boolean',
      description: '加载状态'
    },
    onApplyAuth: {
      action: 'onApplyAuth',
      description: '申请权限回调函数'
    }
  },
  parameters: {
    docs: {
      description: {
        component: '指标卡组件'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof MetricCard>;

// 基础指标卡样例
export const Basic: Story = {
  args: {
    question: '2023年销售总额是多少?',
    loading: false,
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          bizName: 'total_sales',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: true
        }
      ],
      queryResults: [
        {
          total_sales: 15689234.56
        }
      ],
      aggregateInfo: {
        metricInfos: [
          {
            statistics: {
              '环比': '+12.5%',
              '同比': '+8.3%'
            }
          }
        ]
      },
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    }
  }
};

// 百分比指标卡样例
export const Percentage: Story = {
  args: {
    question: '2023年销售增长率是多少?',
    loading: false,
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          bizName: 'growth_rate',
          showType: 'NUMBER',
          dataFormatType: 'percent',
          dataFormat: {
            decimalPlaces: 2,
            needMultiply100: true
          },
          authorized: true
        }
      ],
      queryResults: [
        {
          growth_rate: 0.1856
        }
      ],
      aggregateInfo: {
        metricInfos: [
          {
            statistics: {
              '环比': '+2.5%',
              '同比': '-1.3%'
            }
          }
        ]
      },
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    }
  }
};

// 加载中状态
export const Loading: Story = {
  args: {
    question: '2023年销售总额是多少?',
    loading: true,
    data: {
      queryMode: 'SQL',
      queryColumns: [],
      queryResults: [],
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    }
  }
};

// 需要权限认证
export const NeedAuthorization: Story = {
  args: {
    question: '2023年销售总额是多少?',
    loading: false,
    data: {
      queryMode: 'SQL',
      queryColumns: [
        {
          bizName: 'total_sales',
          showType: 'NUMBER',
          dataFormatType: 'decimal',
          dataFormat: {
            decimalPlaces: 2
          },
          authorized: false
        }
      ],
      queryResults: [],
      entityInfo: {
        dataSetInfo: {
          name: '销售数据'
        }
      }
    },
    onApplyAuth: (model: string) => console.log('申请权限:', model)
  }
}; 