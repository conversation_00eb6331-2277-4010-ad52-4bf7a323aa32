<template>
  <div class="preview-chat">
    <DifyChat
      ref="difyChatRef"
      :current-agent="currentAgent"
      :feedback-disabled="feedbackDisabled"
      :is-debug-mode="isDebugMode"
      :is-use-upload-file="isUseUploadFile"
      :conversation-id="conversationId"
      :chart-id="chartId"
      @update:conversation-id="updateConversationId"
      @startNewConversation="confirmEdit"
      :is-public="false"
    />
    <!-- 编排修改确认弹窗 -->
    <div v-if="showOverlay" class="edit-overlay">
      <div class="overlay-content">
        <div class="icon-wrapper">
          <InfoCircleOutlined class="info-icon" />
        </div>
        <h2 class="overlay-title">编排已改变</h2>
        <p class="overlay-description">修改编排将重置调试区域，确定吗？</p>
        <div class="overlay-buttons">
          <a-button type="primary" @click="confirmEdit">重新开始</a-button>
          <a-button @click="cancelEdit">取消</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineExpose, toRefs } from "vue";
import DifyChat from "../DifyChat/index.vue";
import type { DifyAgentType } from "@/DifyChat/types/type";
import { fetchAppParams } from "@/DifyChat/service/index";
import { InfoCircleOutlined } from "@ant-design/icons-vue";
import { Button as AButton } from "ant-design-vue";
import { setAuthToken } from "@/DifyChat/service/base";
import { setKKServerUrl, setKKLocalUrl, setISMICRO } from "@/config/config";
import { validateNull } from "@/utils/utils";
const difyChatRef = ref<InstanceType<typeof DifyChat>>();

const currentAgent = ref<DifyAgentType>({
  id: "",
  name: "",
  description: "",
});

interface Props {
  feedbackDisabled?: boolean;
  isDebugMode?: boolean; // 是否处于调试模式
  isUseUploadFile?: boolean; // 是否使用上传文件
  token?: string;
  chartId?: string;
  kkServerUrl?: string;
  kkLocalUrl?: string;
  isMicro?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  feedbackDisabled: false,
  isDebugMode: true,
  isUseUploadFile: false,
});

const { token, chartId, kkServerUrl, kkLocalUrl } = toRefs(props);

const showOverlay = ref(false);
const conversationId = ref<string>("");

const updateConversationId = (id: string) => {
  conversationId.value = id;
};

// 对外暴露的打开弹窗方法
const openEditTip = () => {
  showOverlay.value = true;
};

// 确认编辑
const confirmEdit = () => {
  showOverlay.value = false;
  difyChatRef.value?.startNewConversation();

  fetchParams();
};

// 取消编辑
const cancelEdit = () => {
  showOverlay.value = false;
};

// const fetchInfo = async () => {
//   const result: any = await fetchAppInfo();

//   currentAgent.value.name = result.name as string;
//   currentAgent.value.description = result.description as string;
// };

const fetchParams = async () => {
  const res = (await fetchAppParams(chartId.value)) as any;

  if (!res.ok) return;

  const data = res.data || {};

  // currentAgent.value = {
  //   apiKey: data.apiKey,
  //   chartIcon: data.chartIcon,
  //   chartId: data.chartId,
  //   chartName: data.chartName,
  //   chartType: data.chartType,
  //   ...(res.data.difyAppParams || {}),
  // };

  console.log(data);

  currentAgent.value = data;
};

onMounted(() => {
  if (token.value) {
    setAuthToken(token.value);
  }
  if (kkServerUrl.value) {
    setKKServerUrl(kkServerUrl.value);
  }
  if (kkLocalUrl.value) {
    setKKLocalUrl(kkLocalUrl.value);
  }
  if (chartId.value) {
    fetchParams();
  }
  if (!validateNull(props.isMicro)) {
    setISMICRO(props.isMicro);
  }
});

// 对外暴露方法
defineExpose({
  openEditTip,
});
</script>

<style scoped lang="less">
.preview-chat {
  width: 100%;
  height: 100%;
  position: relative;
}

.edit-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(240, 242, 251, 0.78);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.overlay-content {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  width: 400px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.icon-wrapper {
  margin-bottom: 20px;
}

.info-icon {
  font-size: 30px;
  color: #ff9900;
  // background-color: #fff7e6;
  padding: 12px;
  border-radius: 50%;
}

.overlay-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
}

.overlay-description {
  color: #666;
  margin-bottom: 25px;
}

.overlay-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>
