<template>
  <div :class="`${tipPrefixCls}-parse-tip`">
    <div :class="`${tipPrefixCls}-title-bar`">
      <CheckCircleFilled :class="`${tipPrefixCls}-step-icon`" />
      <div :class="`${tipPrefixCls}-step-title`">
        推荐相似问题
        <span :class="`${prefixCls}-toggle-expand-btn`" @click="onToggleExpanded">
          <LoadingOutlined v-if="loading" />
          <UpOutlined v-else-if="expanded" />
          <DownOutlined v-else />
        </span>
      </div>
    </div>
    <div :class="prefixCls">
      <div v-if="expanded" :class="`${prefixCls}-content`">
        <template v-if="Array.isArray(similarQuestions) && similarQuestions.length > 0">
          <div 
            v-for="(question, index) in similarQuestions.slice(0, 5)" 
            :key="question.queryText"
            :class="`${prefixCls}-question`"
            @click="() => onSelectQuestion(question)"
          >
            {{ index + 1 }}. {{ question.queryText }}
          </div>
        </template>
        <template v-else>
          暂无推荐
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, PropType } from 'vue';
import { CheckCircleFilled, DownOutlined, LoadingOutlined, UpOutlined } from '@ant-design/icons-vue';
import { PREFIX_CLS } from '../../common/constants';
import { querySimilarQuestions } from '../../service';
import type { SimilarQuestionType } from '../../common/type';

export default defineComponent({
  name: 'SimilarQuestions',
  components: {
    CheckCircleFilled,
    DownOutlined,
    LoadingOutlined,
    UpOutlined
  },
  props: {
    queryId: {
      type: Number,
      default: undefined
    },
    similarQueries: {
      type: Array as PropType<SimilarQuestionType[]>,
      default: () => []
    },
    defaultExpanded: {
      type: Boolean,
      default: false
    },
    onSelectQuestion: {
      type: Function as PropType<(question: SimilarQuestionType) => void>,
      required: true
    }
  },
  setup(props) {
    const tipPrefixCls = `${PREFIX_CLS}-item`;
    const prefixCls = `${PREFIX_CLS}-similar-questions`;
    
    const similarQuestions = ref<SimilarQuestionType[]>(props.similarQueries || []);
    const expanded = ref(props.defaultExpanded || false);
    const loading = ref(false);
    
    const initData = async () => {
      loading.value = true;
      try {
        const res = await querySimilarQuestions(props.queryId!);
        similarQuestions.value = res.data?.similarQueries || [];
      } finally {
        loading.value = false;
      }
    };
    
    watch(
      () => [expanded.value, props.queryId],
      ([isExpanded, queryId]) => {
        if (isExpanded && similarQuestions.value.length === 0 && queryId) {
          initData();
        }
      }
    );
    
    const onToggleExpanded = () => {
      expanded.value = !expanded.value;
    };
    
    return {
      tipPrefixCls,
      prefixCls,
      similarQuestions,
      expanded,
      loading,
      onToggleExpanded
    };
  }
});
</script> 

<style lang="less" scoped>
@import './style.less';

</style>
