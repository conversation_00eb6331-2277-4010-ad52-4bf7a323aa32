<template>
  <div class="agentList">
    <div class="header">
      <div class="headerTitle">智能会话</div>
    </div>
    <div class="agentListContent">
      <div
        v-for="agent in agentList"
        :key="agent.chartId"
        :class="[
          'agentItem',
          { active: currentAgent?.chartId === agent.chartId },
        ]"
        @click="onSelectAgent(agent)"
      >
        <img :src="`${API_PREFIX}${agent.chartIcon}`" class="avatar" />

        <div class="agentInfo">
          <div class="agentName">{{ agent.chartName }}</div>
          <div class="agentDesc">{{ agent.description }}</div>
        </div>
        <div class="agentPermits" v-if="agent.usePermits === 'public'">
          公开
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRefs } from "vue";
import { API_PREFIX } from "@/config/config";

import type { ClientAgentType } from "@/types/client";

const props = defineProps<{
  agentList: ClientAgentType[];
  currentAgent?: ClientAgentType;
}>();

// 使用toRefs解构props，使其保持响应性
const { agentList, currentAgent } = toRefs(props);

const emit = defineEmits<{
  (e: "selectAgent", agent: ClientAgentType): void;
}>();

const onSelectAgent = (agent: ClientAgentType) => {
  emit("selectAgent", agent);
};
</script>

<style lang="less" scoped>
.agentList {
  position: relative;
  width: 248px;
  height: 100%;
  background: #f9f9f9;
  border-right: 1px solid #f1f1f1;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    padding: 0 16px;

    .headerTitle {
      color: var(--text-color);
      font-weight: 500;
      font-size: 15px;
    }

    .plusIcon {
      color: var(--text-color);
      font-size: 15px;
      cursor: pointer;

      &:hover {
        color: var(--chat-blue);
      }
    }
  }

  .agentListContent {
    display: flex;
    flex-direction: column;
    padding: 4px 8px;
    row-gap: 2px;
    height: calc(100% - 50px);
    overflow-y: auto;

    .agentItem {
      display: flex;
      align-items: center;
      padding: 8px 4px;
      column-gap: 8px;
      border-radius: 8px;
      cursor: pointer;
      position: relative;

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 4px;
      }

      .agentInfo {
        display: flex;
        flex-direction: column;
        row-gap: 2px;

        .agentName {
          color: #000;
          font-size: 14px;
        }

        .agentDesc {
          width: 160px;
          overflow: hidden;
          color: var(--text-color-fourth);
          font-size: 12px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .agentPermits {
        position: absolute;
        right: 4px;
        bottom: 8px;
        color: #22a5f7;
        font-size: 10px;
      }

      &:hover,
      &.active {
        background: #22a5f7;

        .agentName,
        .agentDesc {
          color: #fff;
        }

        .agentPermits {
          color: #fff;
        }
      }
    }
  }
}
</style>
