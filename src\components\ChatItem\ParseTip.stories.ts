import { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import ParseTip from './ParseTip.vue';
import { 
  ChatContextType, 
  DateInfoType, 
  EntityInfoType, 
  FieldType,
  FilterItemType,
  ModelType
} from '../../common/type';
import { ChatContextTypeQueryTypeEnum } from '../../common/constants';
import dayjs from 'dayjs';

// 模拟数据
const mockDateInfo: DateInfoType = {
  dateList: [],
  dateMode: 'ABSOLUTE',
  period: 'day',
  startDate: '2023-01-01',
  endDate: '2023-12-31',
  text: '2023年全年',
  unit: 1
};

const mockEntityInfo: EntityInfoType = {
  dataSetInfo: {
    bizName: 'product',
    itemId: 1,
    name: '商品',
    primaryEntityBizName: 'product',
    value: 'product',
    words: ['商品']
  },
  dimensions: [],
  metrics: [],
  entityId: 1
};

const mockDimensionFilters: FilterItemType[] = [
  {
    elementID: 101,
    name: '城市',
    bizName: 'city',
    operator: '=',
    value: '北京',
    type: 'STRING'
  },
  {
    elementID: 102,
    name: '销售额',
    bizName: 'sales_amount',
    operator: '>=',
    value: 1000,
    type: 'NUMBER'
  }
];

// 修正维度字段定义
const mockDimensions: FieldType[] = [
  { 
    id: 101, 
    name: '城市', 
    bizName: 'city', 
    type: 'DIMENSION',
    itemId: 1001,
    status: 1,
    model: 100,
    value: 'city'
  },
  { 
    id: 102, 
    name: '商品类别', 
    bizName: 'category', 
    type: 'DIMENSION',
    itemId: 1002,
    status: 1,
    model: 100,
    value: 'category'
  }
];

// 修正指标字段定义
const mockMetrics: FieldType[] = [
  { 
    id: 201, 
    name: '销售额', 
    bizName: 'sales_amount', 
    type: 'METRIC',
    itemId: 2001,
    status: 1,
    model: 100,
    value: 'sales_amount'
  },
  { 
    id: 202, 
    name: '订单数', 
    bizName: 'order_count', 
    type: 'METRIC',
    itemId: 2002,
    status: 1,
    model: 100,
    value: 'order_count'
  }
];

const mockParseInfo: ChatContextType = {
  id: 1001,
  queryId: 1001,
  aggType: 'SUM',
  modelId: 100,
  modelName: '销售数据',
  dataSet: {
    alias: '销售',
    bizName: 'sales_data',
    id: 1,
    model: 100,
    name: '销售数据',
    modelNames: ['销售数据'],
    type: 'BASIC',
    useCnt: 1
  } as ModelType,
  dateInfo: mockDateInfo,
  dimensions: mockDimensions,
  metrics: mockMetrics,
  entity: { alias: ['商品'], id: 1 },
  entityInfo: mockEntityInfo,
  elementMatches: [],
  nativeQuery: false,
  queryMode: 'SQL',
  queryType: ChatContextTypeQueryTypeEnum.DETAIL,
  dimensionFilters: mockDimensionFilters,
  properties: {},
  sqlInfo: {
    parsedS2SQL: 'SELECT * FROM sales_data',
    correctedS2SQL: 'SELECT * FROM sales_data WHERE city = "北京"',
    querySQL: 'SELECT * FROM sales_data WHERE city = "北京" AND sales_amount >= 1000'
  },
  textInfo: ''
};

// 定义组件的Meta
const meta = {
  title: 'ChatItem/ParseTip',
  component: ParseTip,
  tags: ['autodocs'],
  argTypes: {
    parseLoading: { 
      control: 'boolean',
      description: '是否正在加载解析结果'
    },
    parseInfoOptions: {
      control: 'object',
      description: '解析信息选项数组'
    },
    parseTip: {
      control: 'text',
      description: '解析提示信息'
    },
    currentParseInfo: {
      control: 'object',
      description: '当前选中的解析信息'
    },
    agentId: {
      control: 'number',
      description: '代理ID'
    },
    dimensionFilters: {
      control: 'object',
      description: '维度筛选条件数组'
    },
    dateInfo: {
      control: 'object',
      description: '日期信息'
    },
    entityInfo: {
      control: 'object',
      description: '实体信息'
    },
    integrateSystem: {
      control: 'text',
      description: '集成系统'
    },
    parseTimeCost: {
      control: 'number',
      description: '解析耗时（毫秒）'
    },
    isDeveloper: {
      control: 'boolean',
      description: '是否为开发者模式'
    },
    isSimpleMode: {
      control: 'boolean',
      description: '是否为简单模式'
    },
    onSelectParseInfo: { 
      action: 'onSelectParseInfo',
      description: '选择解析信息的回调'
    },
    onSwitchEntity: { 
      action: 'onSwitchEntity',
      description: '切换实体的回调'
    },
    onFiltersChange: { 
      action: 'onFiltersChange',
      description: '筛选条件改变的回调'
    },
    onDateInfoChange: { 
      action: 'onDateInfoChange',
      description: '日期信息改变的回调'
    },
    onRefresh: { 
      action: 'onRefresh',
      description: '刷新的回调'
    },
    handlePresetClick: { 
      action: 'handlePresetClick',
      description: '点击预设的回调'
    }
  },
  parameters: {
    docs: {
      description: {
        component: 'ParseTip组件用于展示聊天消息中的意图解析结果，包括加载态、失败态以及成功解析后的详细信息。'
      }
    }
  },
  decorators: [
    () => ({
      template: '<div style="max-width: 800px; margin: 20px auto;"><story /></div>'
    })
  ]
} satisfies Meta<typeof ParseTip>;

export default meta;
type Story = StoryObj<typeof meta>;

// 加载状态
export const Loading: Story = {
  args: {
    parseLoading: true,
    parseInfoOptions: [],
    dimensionFilters: [],
    dateInfo: mockDateInfo,
    entityInfo: mockEntityInfo,
    onSelectParseInfo: fn(),
    onSwitchEntity: fn(),
    onFiltersChange: fn(),
    onDateInfoChange: fn(),
    onRefresh: fn(),
    handlePresetClick: fn()
  }
};

// 解析失败状态
export const ParseFailed: Story = {
  args: {
    parseLoading: false,
    parseTip: '无法理解您的问题，请尝试重新描述或者换一种表达方式。',
    parseInfoOptions: [],
    dimensionFilters: [],
    dateInfo: mockDateInfo,
    entityInfo: mockEntityInfo,
    onSelectParseInfo: fn(),
    onSwitchEntity: fn(),
    onFiltersChange: fn(),
    onDateInfoChange: fn(),
    onRefresh: fn(),
    handlePresetClick: fn()
  }
};

// 解析成功状态
export const ParseSuccess: Story = {
  args: {
    parseLoading: false,
    parseInfoOptions: [mockParseInfo],
    currentParseInfo: mockParseInfo,
    dimensionFilters: mockDimensionFilters,
    dateInfo: mockDateInfo,
    entityInfo: mockEntityInfo,
    parseTimeCost: 123,
    isDeveloper: true,
    isSimpleMode: false,
    onSelectParseInfo: fn(),
    onSwitchEntity: fn(),
    onFiltersChange: fn(),
    onDateInfoChange: fn(),
    onRefresh: fn(),
    handlePresetClick: fn((range: [dayjs.Dayjs, dayjs.Dayjs]) => {
      console.log('预设日期范围:', range[0].format('YYYY-MM-DD'), '到', range[1].format('YYYY-MM-DD'));
    })
  }
};

// 开发者模式
export const DeveloperMode: Story = {
  args: {
    parseLoading: false,
    parseInfoOptions: [mockParseInfo],
    currentParseInfo: mockParseInfo,
    dimensionFilters: mockDimensionFilters,
    dateInfo: mockDateInfo,
    entityInfo: mockEntityInfo,
    parseTimeCost: 123,
    isDeveloper: true,
    isSimpleMode: false,
    onSelectParseInfo: fn(),
    onSwitchEntity: fn(),
    onFiltersChange: fn(),
    onDateInfoChange: fn(),
    onRefresh: fn(),
    handlePresetClick: fn()
  }
};

// 简单模式
export const SimpleMode: Story = {
  args: {
    parseLoading: false,
    parseInfoOptions: [mockParseInfo],
    currentParseInfo: mockParseInfo,
    dimensionFilters: mockDimensionFilters,
    dateInfo: mockDateInfo,
    entityInfo: mockEntityInfo,
    parseTimeCost: 123,
    isDeveloper: false,
    isSimpleMode: true,
    onSelectParseInfo: fn(),
    onSwitchEntity: fn(),
    onFiltersChange: fn(),
    onDateInfoChange: fn(),
    onRefresh: fn(),
    handlePresetClick: fn()
  }
};

// 纯文本查询模式
export const PlainTextMode: Story = {
  args: {
    parseLoading: false,
    parseInfoOptions: [{ ...mockParseInfo, queryMode: 'PLAIN_TEXT' }],
    currentParseInfo: { ...mockParseInfo, queryMode: 'PLAIN_TEXT' },
    dimensionFilters: [],
    dateInfo: mockDateInfo,
    entityInfo: mockEntityInfo,
    parseTimeCost: 45,
    isDeveloper: false,
    isSimpleMode: false,
    onSelectParseInfo: fn(),
    onSwitchEntity: fn(),
    onFiltersChange: fn(),
    onDateInfoChange: fn(),
    onRefresh: fn(),
    handlePresetClick: fn()
  }
}; 