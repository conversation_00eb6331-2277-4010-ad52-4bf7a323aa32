<script lang="ts">
import { defineComponent, computed, PropType } from 'vue';
import {
  ChatContextTypeQueryTypeEnum,
  AGG_TYPE_MAP,
  PREFIX_CLS,
} from "../../common/constants";
import { ChatContextType, FilterItemType, EntityInfoType } from '../../common/type';

export const MAX_OPTION_VALUES_COUNT = 2;
const prefixCls = `${PREFIX_CLS}-item`;

export default defineComponent({
  name: 'ParseTip',
  props: {
    parseInfo: {
      type: Object as PropType<ChatContextType>,
      required: true
    },
    dimensionFilters: {
      type: Array as PropType<FilterItemType[]>,
      default: () => []
    },
    entityInfo: {
      type: Object as PropType<EntityInfoType>,
      default: null
    }
  },
  setup(props) {
    const itemValueClass = `${prefixCls}-tip-item-value`;
    
    const entityId = computed(() => {
      return props.dimensionFilters?.length > 0 ? props.dimensionFilters[0].value : undefined;
    });

    const entityAlias = computed(() => {
      return props.parseInfo?.entity?.alias?.[0]?.split('.')?.[0];
    });

    const entityName = computed(() => {
      return props.parseInfo?.elementMatches?.find(item => item.element?.type === 'ID')?.element.name;
    });

    const agentType = computed(() => props.parseInfo?.properties?.type);
    const agentName = computed(() => props.parseInfo?.properties?.name);
    
    const dataSet = computed(() => props.parseInfo?.dataSet);
    const dimensions = computed(() => props.parseInfo?.dimensions);
    const metrics = computed(() => props.parseInfo?.metrics);
    const aggType = computed(() => props.parseInfo?.aggType);
    const queryMode = computed(() => props.parseInfo?.queryMode);
    const queryType = computed(() => props.parseInfo?.queryType);
    
    const dimensionItems = computed(() => {
      return dimensions.value?.filter(item => item.type === 'DIMENSION');
    });

    const fields = computed(() => {
      return queryMode.value === 'TAG_DETAIL' 
        ? dimensionItems.value?.concat(metrics.value || []) 
        : dimensionItems.value;
    });
    
    const getMetricsText = computed(() => {
      return metrics.value?.map(metric => metric.name).join('、') || '';
    });
    
    const getFieldsText = computed(() => {
      if (!fields.value || fields.value.length === 0) return '';
      const fieldNames = fields.value
        .slice(0, MAX_OPTION_VALUES_COUNT)
        .map(field => field.name)
        .join('、');
      return fieldNames + (fields.value.length > MAX_OPTION_VALUES_COUNT ? '...' : '');
    });
    
    const getAggTypeText = computed(() => {
      if (!aggType.value || aggType.value === 'NONE') return '';
      return AGG_TYPE_MAP[aggType.value as keyof typeof AGG_TYPE_MAP] || '';
    });

    return {
      prefixCls,
      MAX_OPTION_VALUES_COUNT,
      ChatContextTypeQueryTypeEnum,
      AGG_TYPE_MAP,
      itemValueClass,
      entityId,
      entityAlias,
      entityName,
      agentType,
      agentName,
      dataSet,
      dimensions,
      metrics,
      aggType,
      queryMode,
      queryType,
      fields,
      getMetricsText,
      getFieldsText,
      getAggTypeText
    };
  }
});
</script>

<template>
  <div :class="`${prefixCls}-tip-content`">
    <template v-if="!!agentType && queryMode !== 'LLM_S2SQL'">
      <div :class="`${prefixCls}-tip-item`">
        将由{{ agentType === 'plugin' ? '插件' : '内置' }}工具
        <span :class="itemValueClass">{{ agentName }}</span>来解答
      </div>
    </template>
    <template v-else>
      <template v-if="(queryMode?.includes('ENTITY') || queryMode === 'LLM_S2SQL') && typeof entityId === 'string' && !!entityAlias && !!entityName">
        <div :class="`${prefixCls}-tip-item`">
          <div :class="`${prefixCls}-tip-item-name`">{{ entityAlias }}：</div>
          <div :class="itemValueClass">{{ entityName }}</div>
        </div>
      </template>
      <template v-else>
        <div :class="`${prefixCls}-tip-item`">
          <div :class="`${prefixCls}-tip-item-name`">数据集：</div>
          <div :class="itemValueClass">{{ dataSet?.name }}</div>
        </div>
      </template>
      
      <template v-if="(queryType === ChatContextTypeQueryTypeEnum.AGGREGATE || queryType === 'METRIC_TAG' || queryType === 'DETAIL')">
        <div :class="`${prefixCls}-tip-item`">
          <div :class="`${prefixCls}-tip-item-name`">查询模式：</div>
          <div :class="itemValueClass">
            {{ queryType === ChatContextTypeQueryTypeEnum.AGGREGATE || queryType === 'METRIC_TAG' ? '聚合模式' : '明细模式' }}
          </div>
        </div>
      </template>
      
      <template v-if="queryType !== 'DETAIL' && metrics && metrics.length > 0 && !dimensions?.some(item => item.bizName?.includes('_id'))">
        <div :class="`${prefixCls}-tip-item`">
          <div :class="`${prefixCls}-tip-item-name`">指标：</div>
          <div :class="itemValueClass">
            {{ getMetricsText }}
          </div>
        </div>
      </template>
      
      <template v-if="['METRIC_GROUPBY', 'METRIC_ORDERBY', 'TAG_DETAIL', 'LLM_S2SQL', 'METRIC_FILTER'].includes(queryMode || '') && fields && fields.length > 0">
        <div :class="`${prefixCls}-tip-item`">
          <div :class="`${prefixCls}-tip-item-name`">
            {{ queryType === 'DETAIL' ? '查询字段' : '下钻维度' }}：
          </div>
          <div :class="itemValueClass">
            {{ getFieldsText }}
          </div>
        </div>
      </template>
      
      <template v-if="queryMode !== 'TAG_ID' && !dimensions?.some(item => item.bizName?.includes('_id'))">
        <div 
          v-for="dimension in entityInfo?.dimensions?.filter(dimension => dimension.value != null)" 
          :key="dimension.itemId"
          :class="`${prefixCls}-tip-item`"
        >
          <div :class="`${prefixCls}-tip-item-name`">{{ dimension.name }}：</div>
          <div :class="itemValueClass">{{ dimension.value }}</div>
        </div>
      </template>
      
      <template v-if="(queryMode === 'METRIC_ORDERBY' || queryMode === 'METRIC_MODEL') && aggType && aggType !== 'NONE'">
        <div :class="`${prefixCls}-tip-item`">
          <div :class="`${prefixCls}-tip-item-name`">聚合方式：</div>
          <div :class="itemValueClass">{{ getAggTypeText }}</div>
        </div>
      </template>
    </template>
  </div>
</template>

<style scoped lang="less">
@import "./style.less";
</style>
