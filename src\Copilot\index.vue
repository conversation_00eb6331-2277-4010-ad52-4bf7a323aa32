<template>
  <div>
    <div
      :class="['copilot', { 'copilotMinimized': copilotMinimized }]"
      @mouseenter="setCopilotMinimized(false)"
      @click="onToggleChatVisible"
    >
      <IconFont type="icon-copilot-fill" />
      <div class="minimizeWrapper" @click.stop="onMinimizeCopilot">
        <div class="minimize">-</div>
      </div>
    </div>
    <div
      class="copilotContent"
      :style="{ display: chatVisible ? 'block' : 'none' }"
    >
      <div
        class="chatPopover"
        :class="[{ ['c2System']: integrateSystem === 'c2' }]"
      >
        <div class="header">
          <div class="leftSection">
            <CloseOutlined class="close" @click="onCloseChat" />
            <IconFont
              v-if="onOpenChatPage"
              type="icon-weibiaoti-"
              class="transfer"
              @click="onTransferChat"
            />
          </div>
          <div class="title">{{ copilotTitle }}</div>
        </div>
        <div class="chat">
          <Chat
            :chat-visible="chatVisible"
            :agent-ids="agentIds"
            :no-input="noInput"
            :is-developer="isDeveloper"
            :integrate-system="integrateSystem"
            :is-copilot="true"
            @current-agent-change="setCurrentAgent"
            @report-msg-event="onReportMsgEvent"
            ref="chatRef"
          />
        </div>
      </div>
      <CaretRightOutlined class="rightArrow" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import IconFont from "../components/IconFont/index.vue";
import { CaretRightOutlined, CloseOutlined } from "@ant-design/icons-vue";
import Chat from "../Chat/index.vue";
import { AgentType } from "../Chat/type";
import { setToken } from "../utils/utils";
import { SendMsgParamsType } from "../common/type";
import { copilotTitle } from "../common/env";

interface Props {
  token?: string;
  agentIds?: number[];
  noInput?: boolean;
  isDeveloper?: boolean;
  integrateSystem?: string;
  apiUrl?: string;
  onReportMsgEvent?: (msg: string, valid: boolean) => void;
  onOpenChatPage?: (agentId?: number) => void;
}

interface Emits {
  (e: "update:token", value: string): void;
  (e: "update:agentIds", value: number[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  noInput: false,
  isDeveloper: false,
  agentIds: () => [],
});

const emit = defineEmits<Emits>();

const chatVisible = ref(false);
const copilotMinimized = ref(false);
const currentAgent = ref<AgentType>();
const chatRef = ref<InstanceType<typeof Chat>>();

const sendCopilotMsg = (params: SendMsgParamsType) => {
  chatRef.value?.sendCopilotMsg(params);
  updateChatVisible(true);
};

const updateChatVisible = (visible: boolean) => {
  chatVisible.value = visible;
};

const onToggleChatVisible = () => {
  updateChatVisible(!chatVisible.value);
};

const onCloseChat = () => {
  updateChatVisible(false);
};

const onTransferChat = () => {
  props.onOpenChatPage?.(currentAgent.value?.id);
};

const onMinimizeCopilot = (e: Event) => {
  e.stopPropagation();
  updateChatVisible(false);
  copilotMinimized.value = true;
};

const setCopilotMinimized = (value: boolean) => {
  copilotMinimized.value = value;
};

const setCurrentAgent = (agent: AgentType) => {
  currentAgent.value = agent;
};

watch(
  () => props.token,
  newToken => {
    if (newToken) {
      setToken(newToken);
      emit("update:token", newToken);
    }
  }
);

watch(
  () => props.apiUrl,
  newUrl => {
    if (newUrl) {
      localStorage.setItem("SUPERSONIC_CHAT_API_URL", newUrl);
    }
  }
);

watch(
  () => props.agentIds,
  newIds => {
    emit("update:agentIds", newIds);
  }
);

defineExpose({
  sendCopilotMsg,
});
</script>

<style lang="less" scoped>
.copilot {
  position: fixed;
  right: 8px;
  bottom: 220px;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 54px;
  height: 54px;
  color: #fff;
  font-size: 26px;
  background-color: var(--chat-blue);
  background-clip: padding-box;
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 8px 8px 20px 0 rgba(55, 99, 170, 0.1);
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &.copilotMinimized {
    right: -40px;
  }

  .minimizeWrapper {
    position: absolute;
    top: -18px;
    right: -6px;
    display: none;
    width: 22px;
    height: 22px;
    padding: 4px;
    cursor: pointer;

    .minimize {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      padding-bottom: 5px;
      background-color: var(--text-color-fifth-4);
      border-radius: 50%;
      transition: all 0.1s ease-in-out;
    }

    &:hover {
      .minimize {
        background-color: var(--text-color-fifth);
      }
    }
  }

  &:hover {
    text-decoration: none;
    box-shadow: 8px 8px 20px rgba(55, 99, 170, 0.3);

    .minimizeWrapper {
      display: block;
    }
  }
}

.chatPopover {
  position: fixed;
  right: 90px;
  bottom: 5vh;
  z-index: 999;
  display: flex;
  flex-direction: column;
  width: 70vw;
  min-width: 1100px;
  height: 90vh;
  overflow: hidden;
  box-shadow:
    4px 4px 10px rgba(55, 99, 170, 0.3),
    -2px -2px 16px rgba(55, 99, 170, 0.3);
  transition:
    opacity 0.3s ease-in-out,
    transform 0.3s ease-in-out,
    -webkit-transform 0.3s ease-in-out;

  &.c2System {
    width: calc(100vw - 180px);
  }

  .header {
    position: relative;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding-right: 16px;
    padding-left: 16px;
    background: linear-gradient(
      81.62deg,
      #2870ea 8.72%,
      var(--chat-blue) 85.01%
    );
    box-shadow: 1px 1px 8px #1b4aef5c;

    .title {
      color: #fff;
      font-weight: 700;
      font-size: 16px;
    }

    .leftSection {
      position: absolute;
      left: 16px;
      display: flex;
      align-items: center;
      color: #fff;
      font-size: 16px;
      column-gap: 20px;

      .close {
        font-size: 18px;
        cursor: pointer;
      }

      .transfer {
        cursor: pointer;
      }

      .fullscreen {
        font-size: 20px;
        cursor: pointer;
      }
    }
  }

  .chat {
    height: calc(90vh - 40px);
  }

  &.fullscreen {
    bottom: 0;
    left: 60px;
    width: calc(100vw - 150px);
    height: 100vh;

    .chat {
      height: calc(100vh - 50px);
    }
  }
}

.rightArrow {
  position: fixed;
  right: 69px;
  bottom: 232px;
  z-index: 999;
  color: var(--chat-blue);
  font-size: 30px;
}
</style>
