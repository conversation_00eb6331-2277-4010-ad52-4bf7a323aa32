<template>
  <div>
    <div v-if="executeLoading || executeTip">
      <div v-if="executeLoading">
        <div :class="`${prefixCls}-title-bar`">
          <check-circle-filled :class="`${prefixCls}-step-icon`" />
          <div :class="`${prefixCls}-step-title`">
            {{ titlePrefix }}查询中
            <Loading />
          </div>
        </div>
      </div>
      <div v-else-if="executeTip">
        <div :class="`${prefixCls}-title-bar`">
          <check-circle-filled :class="`${prefixCls}-step-icon`" />
          <div :class="`${prefixCls}-step-title`">
            <span>{{ titlePrefix }}查询失败</span>
            <Space v-if="executeErrorMsg">
              <info-circle-outlined style="margin-left: 5px; color: red" />
              <a @click="toggleErrorMsg">
                {{ !showErrMsg ? '查看' : '收起' }}
              </a>
            </Space>
            <span 
              v-if="!!data?.queryTimeCost && isDeveloper" 
              :class="`${prefixCls}-title-tip`"
            >
              (耗时: {{ data.queryTimeCost }}ms)
            </span>
          </div>
        </div>
        <div v-if="showErrMsg" :class="`${prefixCls}-content-container`">
          <pre :class="`${prefixCls}-code`">{{ executeErrorMsg }}</pre>
        </div>
      </div>
    </div>

    <template v-else-if="data">
      <div v-if="!isSimpleMode" :class="`${prefixCls}-title-bar`">
        <check-circle-filled :class="`${prefixCls}-step-icon`" />
        <div
          :class="`${prefixCls}-step-title ${prefixCls}-execute-title-bar`"
          style="width: 100%"
        >
          <div>
            {{ titlePrefix }}查询
            <span v-if="!!data.queryTimeCost && isDeveloper" :class="`${prefixCls}-title-tip`">
              (耗时: {{ data.queryTimeCost }}ms)
            </span>
          </div>
          <div>
            <a-switch
              v-if="
                [MsgContentTypeEnum.METRIC_TREND, MsgContentTypeEnum.METRIC_BAR].includes(
                  msgContentType as MsgContentTypeEnum
                )
              "
              checked-children="表格"
              un-checked-children="表格"
              @change="setShowMsgContentTable"
            />
          </div>
        </div>
      </div>

      <div
        :class="`${prefixCls}-content-container ${
          isSimpleMode ? `${prefixCls}-content-container-simple` : ''
        }`"
        :style="{ borderLeft: queryMode === 'PLAIN_TEXT' ? 'none' : undefined }"
      >
        <a-spin :spinning="entitySwitchLoading">
          <div v-if="data.queryAuthorization?.message" :class="`${prefixCls}-auth-tip`">
            提示：{{ data.queryAuthorization.message }}
          </div>
          <p v-if="data.textSummary" :class="`${prefixCls}-step-title`">
            <span style="margin-right: 5px">总结:</span>
            {{ data.textSummary }}
          </p>

          <component
            v-if="renderCustomExecuteNode && executeItemNode"
            :is="executeItemNode"
          ></component>
          <template v-else-if="data.queryMode === 'PLAIN_TEXT' || data.queryMode === 'WEB_SERVICE'">
            {{ data.textResult }}
          </template>
          <WebPage
            v-else-if="data.queryMode === 'WEB_PAGE'"
            :id="queryId"
            :data="data"
          />
          <ChatMsg
            v-else
            :is-simple-mode="isSimpleMode"
            :force-show-table="showMsgContentTable"
            :query-id="queryId"
            :question="question"
            :data="data"
            :chart-index="chartIndex"
            :trigger-resize="triggerResize"
            @msg-content-type-change="setMsgContentType"
          />
        </a-spin>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { Space } from 'ant-design-vue';
import { CheckCircleFilled, InfoCircleOutlined } from '@ant-design/icons-vue';
import { PREFIX_CLS, MsgContentTypeEnum } from '../../common/constants';
import type { MsgDataType } from '../../common/type';
import ChatMsg from '../ChatMsg/index.vue';
import WebPage from '../ChatMsg/WebPage/index.vue';
import Loading from './Loading.vue';

// 定义组件属性
const props = defineProps({
  queryId: {
    type: Number,
    default: undefined
  },
  question: {
    type: String,
    required: true
  },
  queryMode: {
    type: String,
    default: undefined
  },
  executeLoading: {
    type: Boolean,
    required: true
  },
  entitySwitchLoading: {
    type: Boolean,
    default: false
  },
  chartIndex: {
    type: Number,
    required: true
  },
  executeTip: {
    type: String,
    default: undefined
  },
  executeErrorMsg: {
    type: String,
    default: undefined
  },
  executeItemNode: {
    type: Object,
    default: undefined
  },
  renderCustomExecuteNode: {
    type: Boolean,
    default: undefined
  },
  data: {
    type: Object as () => MsgDataType | undefined,
    default: undefined
  },
  triggerResize: {
    type: Boolean,
    default: undefined
  },
  isDeveloper: {
    type: Boolean,
    default: undefined
  },
  isSimpleMode: {
    type: Boolean,
    default: undefined
  }
});

// 组件状态
const prefixCls = `${PREFIX_CLS}-item`;
const showMsgContentTable = ref(false);
const msgContentType = ref<MsgContentTypeEnum>();
const showErrMsg = ref(false);

// 计算标题前缀
const titlePrefix = computed(() => 
  props.queryMode === 'PLAIN_TEXT' || props.queryMode === 'WEB_SERVICE' ? '问答' : '数据'
);

// 设置表格显示状态
const setShowMsgContentTable = (checked: boolean) => {
  showMsgContentTable.value = checked;
};

// 设置消息内容类型
const setMsgContentType = (type: MsgContentTypeEnum) => {
  msgContentType.value = type;
};

// 切换错误信息显示状态
const toggleErrorMsg = () => {
  showErrMsg.value = !showErrMsg.value;
};
</script>

<style lang="less" scoped>
@import './style.less';

.sql-highlighter {
  margin: 10px 0;
  border-radius: 4px;
  overflow: auto;
}
</style>
