<template>
  <div :class="[prefixCls, { [`${PREFIX_CLS}-metric-card-dsl`]: data.queryMode === 'LLM_S2SQL' }]">
    <div :class="`${prefixCls}-top-bar`">
      <div :class="`${prefixCls}-indicator-name`">{{ question }}</div>
    </div>
    <a-spin :spinning="loading">
      <div :class="`${prefixCls}-indicator`">
        <template v-if="indicatorColumn && !indicatorColumn?.authorized">
          <ApplyAuth 
            :model="data.entityInfo?.dataSetInfo.name || ''" 
            :onApplyAuth="onApplyAuth" 
          />
        </template>
        <template v-else>
          <div style="display: flex; align-items: flex-end">
            <div :class="`${prefixCls}-indicator-value`">
              {{ formatValue }}
            </div>
            <div 
              v-if="!isNaN(+value) && +value >= 10000" 
              :class="`${prefixCls}-indicator-switch`"
              @click="handleNumberClick"
            >
              <swap-outlined />
            </div>
          </div>
        </template>
        <div v-if="metricInfos?.length > 0" :class="`${prefixCls}-period-compare`">
          <PeriodCompareItem 
            v-for="(value, key) in metricInfos[0].statistics" 
            :key="key"
            :title="key"
            :value="value"
          />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, PropType, ref } from 'vue';
import { PREFIX_CLS } from '../../../common/constants';
import { formatByDecimalPlaces, formatMetric, formatNumberWithCN } from '../../../utils/utils';
import ApplyAuth from '../ApplyAuth/index.vue';
import PeriodCompareItem from './PeriodCompareItem.vue';
import { SwapOutlined } from '@ant-design/icons-vue';
import { MsgDataType } from '../../../common/type';

export default defineComponent({
  name: 'MetricCard',
  components: {
    ApplyAuth,
    PeriodCompareItem,
    SwapOutlined
  },
  props: {
    data: {
      type: Object as PropType<MsgDataType>,
      required: true
    },
    question: {
      type: String,
      required: true
    },
    loading: {
      type: Boolean,
      required: true
    },
    onApplyAuth: {
      type: Function as PropType<(model: string) => void>,
      default: undefined
    }
  },
  setup(props) {
    const prefixCls = `${PREFIX_CLS}-metric-card`;
    const isNumber = ref(false);

    const { queryColumns, queryResults, aggregateInfo } = props.data;
    const { metricInfos } = aggregateInfo || {};

    const indicatorColumn = computed(() => 
      queryColumns?.find(column => column.showType === 'NUMBER')
    );

    const indicatorColumnName = computed(() => 
      indicatorColumn.value?.bizName || ''
    );

    const dataFormatInfo = computed(() => ({
      dataFormatType: indicatorColumn.value?.dataFormatType,
      dataFormat: indicatorColumn.value?.dataFormat
    }));

    const value = computed(() => 
      queryResults?.[0]?.[indicatorColumnName.value] || 0
    );

    const formatValue = computed(() => {
      if (typeof value.value === 'string' && isNaN(+value.value)) {
        return value.value;
      }
      if (dataFormatInfo.value.dataFormatType === 'percent' || dataFormatInfo.value.dataFormatType === 'decimal') {
        return `${formatByDecimalPlaces(
          dataFormatInfo.value.dataFormat?.needMultiply100 ? +value.value * 100 : value.value,
          dataFormatInfo.value.dataFormat?.decimalPlaces || 2
        )}${dataFormatInfo.value.dataFormatType === 'percent' ? '%' : ''}`;
      }
      return isNumber.value
        ? formatMetric(value.value) || '-'
        : formatNumberWithCN(+value.value);
    });

    const handleNumberClick = () => {
      isNumber.value = !isNumber.value;
    };

    return {
      prefixCls,
      PREFIX_CLS,
      value,
      formatValue,
      indicatorColumn,
      metricInfos,
      handleNumberClick
    };
  }
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
