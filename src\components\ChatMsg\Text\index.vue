<template>
  <div
    style="
      line-height: 24px;
      width: fit-content;
      max-width: 100%;
      overflow-x: hidden;
    "
  >
    {{ text }}
    <span 
      v-if="referenceData.length > 0" 
      :class="`${prefixCls}-check-more`" 
      @click="onToggleMore"
    >
      {{ referenceExpanded ? '收起' : '查看' }}更多
      <up-outlined v-if="referenceExpanded" :class="`${prefixCls}-arrow-icon`" />
      <down-outlined v-else :class="`${prefixCls}-arrow-icon`" />
    </span>
    <div 
      v-if="referenceData.length > 0 && referenceExpanded" 
      :class="`${prefixCls}-reference-data`"
    >
      <div 
        v-for="item in referenceData" 
        :key="item.doc_title" 
        :class="`${prefixCls}-reference-item`"
      >
        <div :class="`${prefixCls}-reference-item-title`">{{ item.doc_title }}</div>
        <div :class="`${prefixCls}-reference-item-value`">{{ item.doc_chunk }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, PropType } from 'vue';
import { UpOutlined, DownOutlined } from '@ant-design/icons-vue';
import { CLS_PREFIX } from '../../../common/constants';
import { ColumnType } from '../../../common/type';

export default defineComponent({
  name: 'Text',
  components: {
    UpOutlined,
    DownOutlined
  },
  props: {
    columns: {
      type: Array as PropType<ColumnType[]>,
      required: true
    },
    referenceColumn: {
      type: Object as PropType<ColumnType>,
      default: undefined
    },
    dataSource: {
      type: Array as PropType<any[]>,
      required: true
    }
  },
  setup(props) {
    const text = ref<string>('');
    const referenceExpanded = ref(false);
    const referenceData = ref<any[]>([]);
    
    const prefixCls = `${CLS_PREFIX}-text`;

    const initData = () => {
      if (props.columns.length > 0 && props.dataSource.length > 0) {
        const textValue = props.dataSource[0][props.columns[0].bizName];
        text.value = textValue === undefined ? '暂无数据' : textValue;
        
        if (props.referenceColumn) {
          const referenceDataValue = props.dataSource[0][props.referenceColumn.bizName];
          referenceData.value = referenceDataValue || [];
        }
      }
    };

    const onToggleMore = () => {
      referenceExpanded.value = !referenceExpanded.value;
    };

    onMounted(() => {
      initData();
    });

    return {
      text,
      referenceExpanded,
      referenceData,
      prefixCls,
      onToggleMore
    };
  }
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
