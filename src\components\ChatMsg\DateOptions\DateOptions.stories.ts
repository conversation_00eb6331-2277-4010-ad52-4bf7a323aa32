import { Meta, StoryFn } from '@storybook/vue3';
import DateOptions from './index.vue';
import { CLS_PREFIX } from '../../../common/constants';
import { fn } from '@storybook/test';

export default {
  title: 'Components/ChatMsg/DateOptions',
  component: DateOptions,
  tags: ['autodocs'],
  argTypes: {
    chatContext: {
      control: { type: 'object' },
      description: '聊天上下文，包含日期信息',
    },
    currentDateOption: {
      control: { type: 'number' },
      description: '当前选中的日期选项值',
    },
    selectDateOption: {
      action: 'selectDateOption',
      description: '选择日期选项时的回调函数',
    },
  },
  parameters: {
    docs: {
      description: {
        component: '日期选项组件，用于选择不同时间范围的数据',
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#333333' },
      ],
    },
  },
} as Meta<typeof DateOptions>;

const Template: StoryFn<typeof DateOptions> = (args) => ({
  components: { DateOptions },
  setup() {
    return { args, CLS_PREFIX };
  },
  template: `
    <div style="padding: 20px; width: 100%; border: 1px solid #eee; border-radius: 8px;">
      <DateOptions v-bind="args" @selectDateOption="args.selectDateOption" />
    </div>
  `,
});

// 日期周期为DAY
export const 日周期 = Template.bind({});
日周期.args = {
  chatContext: {
    dateInfo: {
      period: 'DAY',
      unit: 7,
    },
  },
  currentDateOption: 7,
  selectDateOption: fn((value) => console.log(`选择了日期选项: ${value}`)),
};

// 日期周期为WEEK
export const 周周期 = Template.bind({});
周周期.args = {
  chatContext: {
    dateInfo: {
      period: 'WEEK',
      unit: 4,
    },
  },
  currentDateOption: 4,
  selectDateOption: fn((value) => console.log(`选择了日期选项: ${value}`)),
};

// 日期周期为MONTH
export const 月周期 = Template.bind({});
月周期.args = {
  chatContext: {
    dateInfo: {
      period: 'MONTH',
      unit: 3,
    },
  },
  currentDateOption: 3,
  selectDateOption: fn((value) => console.log(`选择了日期选项: ${value}`)),
};

// 未选中状态
export const 未选中状态 = Template.bind({});
未选中状态.args = {
  chatContext: {
    dateInfo: {
      period: 'DAY',
    },
  },
  selectDateOption: fn((value) => console.log(`选择了日期选项: ${value}`)),
};

// 移动端显示
export const 移动端显示 = Template.bind({});
移动端显示.args = {
  ...日周期.args,
};
移动端显示.parameters = {
  viewport: {
    defaultViewport: 'mobile1',
  },
};

// 深色背景
export const 深色背景 = Template.bind({});
深色背景.args = {
  ...日周期.args,
};
深色背景.parameters = {
  backgrounds: { default: 'dark' },
};
深色背景.decorators = [
  () => ({ 
    template: '<div style="padding: 20px; color: white; background-color: #333; border-radius: 8px;"><story /></div>' 
  }),
]; 