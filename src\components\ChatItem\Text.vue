<template>
  <Message position="left" :bubbleClassName="`${prefixCls}-text-bubble`">
    <div :class="`${prefixCls}-text`">{{ data }}</div>
  </Message>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Message from '../ChatMsg/Message/index.vue';
import { PREFIX_CLS } from '../../common/constants';

export default defineComponent({
  name: 'Text',
  components: {
    Message
  },
  props: {
    data: {
      type: [String, Object, Array],
      required: true
    }
  },
  setup() {
    const prefixCls = `${PREFIX_CLS}-item`;
    
    return {
      prefixCls
    };
  }
});
</script> 