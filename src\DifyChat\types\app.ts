/* eslint-disable @typescript-eslint/no-explicit-any */
import type { ImgHTMLAttributes } from "vue";
import type { ThoughtItem } from "./type";

export type PromptVariable = {
  key: string;
  name: string;
  type: string;
  default?: string | number;
  options?: string[];
  max_length?: number;
  required: boolean;
};

export type PromptConfig = {
  prompt_template: string;
  prompt_variables: PromptVariable[];
};

export type TextTypeFormItem = {
  label: string;
  variable: string;
  required: boolean;
  max_length: number;
};

export type SelectTypeFormItem = {
  label: string;
  variable: string;
  required: boolean;
  options: string[];
};
/**
 * User Input Form Item
 */
export type UserInputFormItem =
  | {
      "text-input": TextTypeFormItem;
    }
  | {
      select: SelectTypeFormItem;
    }
  | {
      paragraph: TextTypeFormItem;
    };

export const MessageRatings = ["like", "dislike", null] as const;
export type MessageRating = (typeof MessageRatings)[number];

export type Feedbacktype = {
  rating: MessageRating;
  content?: string | null;
};

export type MessageMore = {
  time: string;
  tokens: number;
  latency: number | string;
};

export type IChatItem = {
  id: string;
  content: string;
  /**
   * Specific message type
   */
  isAnswer: boolean;
  /**
   * The user feedback result of this message
   */
  feedback?: Feedbacktype;
  /**
   * The admin feedback result of this message
   */
  adminFeedback?: Feedbacktype;
  /**
   * Whether to hide the feedback area
   */
  feedbackDisabled?: boolean;
  /**
   * More information about this message
   */
  more?: MessageMore;
  annotation?: Annotation;
  useCurrentUserAvatar?: boolean;
  isOpeningStatement?: boolean;
  suggestedQuestions?: string[];
  log?: { role: string; text: string }[];
  agent_thoughts?: ThoughtItem[];
  message_files?: (VisionFile | UploadFile)[];
  retriever_resources?: {
    document_name: string;
    fileUrl: string;
    fullUrl?: string;
  }[];
  suggested_questions?: string[];
};

export type ChatItem = IChatItem & {
  isError?: boolean;
  workflow_run_id?: string;
  workflowProcess?: WorkflowProcess;
};

export type ResponseHolder = {};

export type ConversationItem = {
  id: string;
  name: string;
  inputs: Record<string, any> | null;
  introduction: string;
  created_at: number;
  updated_at: number;
};

export type AppInfo = {
  title: string;
  description: string;
  default_language: "zh-Hans";
  copyright?: string;
  privacy_policy?: string;
};

export enum Resolution {
  low = "low",
  high = "high",
}

export enum TransferMethod {
  all = "all",
  local_file = "local_file",
  remote_url = "remote_url",
}

export type VisionSettings = {
  enabled: boolean;
  number_limits: number;
  detail: Resolution;
  transfer_methods: TransferMethod[];
  image_file_size_limit?: number | string;
};

export type ImageFile = {
  type: TransferMethod;
  _id: string;
  fileId: string;
  file?: File;
  progress: number;
  url: string;
  base64Url?: string;
  deleted?: boolean;
};

export type VisionFile = {
  id?: string;
  type: string;
  transfer_method: TransferMethod;
  url: string;
  upload_file_id: string;
  belongs_to?: string;
  filename?: string;
};

export type AgentLogItem = {
  node_execution_id: string;
  id: string;
  node_id: string;
  parent_id?: string;
  label: string;
  data: object; // debug data
  error?: string;
  status: string;
  metadata?: {
    elapsed_time?: number;
    provider?: string;
    icon?: string;
  };
};

export type IterationDurationMap = Record<string, number>;
export type LoopDurationMap = Record<string, number>;
export type LoopVariableMap = Record<string, any>;

export enum ErrorHandleTypeEnum {
  none = "none",
  failBranch = "fail-branch",
  defaultValue = "default-value",
}

export enum BlockEnum {
  Start = "start",
  End = "end",
  Answer = "answer",
  LLM = "llm",
  KnowledgeRetrieval = "knowledge-retrieval",
  QuestionClassifier = "question-classifier",
  IfElse = "if-else",
  Code = "code",
  TemplateTransform = "template-transform",
  HttpRequest = "http-request",
  VariableAssigner = "variable-assigner",
  VariableAggregator = "variable-aggregator",
  Tool = "tool",
  ParameterExtractor = "parameter-extractor",
  Iteration = "iteration",
  DocExtractor = "document-extractor",
  ListFilter = "list-operator",
  IterationStart = "iteration-start",
  Assigner = "assigner", // is now named as VariableAssigner
  Agent = "agent",
  Loop = "loop",
  LoopStart = "loop-start",
  LoopEnd = "loop-end",
}

export type AgentLogItemWithChildren = AgentLogItem & {
  hasCircle?: boolean;
  children: AgentLogItemWithChildren[];
};

export type NodeTracing = {
  id: string;
  index: number;
  predecessor_node_id: string;
  node_id: string;
  iteration_id?: string;
  loop_id?: string;
  node_type: BlockEnum;
  title: string;
  inputs: any;
  process_data: any;
  outputs?: any;
  status: string;
  parallel_run_id?: string;
  error?: string;
  elapsed_time: number;
  execution_metadata?: {
    total_tokens: number;
    total_price: number;
    currency: string;
    iteration_id?: string;
    iteration_index?: number;
    loop_id?: string;
    loop_index?: number;
    parallel_id?: string;
    parallel_start_node_id?: string;
    parent_parallel_id?: string;
    parent_parallel_start_node_id?: string;
    parallel_mode_run_id?: string;
    iteration_duration_map?: IterationDurationMap;
    loop_duration_map?: LoopDurationMap;
    error_strategy?: ErrorHandleTypeEnum;
    agent_log?: AgentLogItem[];
    tool_info?: {
      agent_strategy?: string;
      icon?: string;
    };
    loop_variable_map?: Record<string, any>;
  };
  metadata: {
    iterator_length: number;
    iterator_index: number;
    loop_length: number;
    loop_index: number;
  };
  created_at: number;
  created_by: {
    id: string;
    name: string;
    email: string;
  };
  iterDurationMap?: IterationDurationMap;
  loopDurationMap?: LoopDurationMap;
  finished_at: number;
  extras?: any;
  expand?: boolean; // for UI
  details?: NodeTracing[][]; // iteration or loop detail
  retryDetail?: NodeTracing[]; // retry detail
  retry_index?: number;
  parallelDetail?: {
    // parallel detail. if is in parallel, this field will be set
    isParallelStartNode?: boolean;
    parallelTitle?: string;
    branchTitle?: string;
    children?: NodeTracing[];
  };
  parallel_id?: string;
  parallel_start_node_id?: string;
  parent_parallel_id?: string;
  parent_parallel_start_node_id?: string;
  agentLog?: AgentLogItemWithChildren[]; // agent log
};

export enum NodeRunningStatus {
  NotStart = "not-start",
  Waiting = "waiting",
  Running = "running",
  Succeeded = "succeeded",
  Failed = "failed",
}

export enum WorkflowRunningStatus {
  Waiting = "waiting",
  Running = "running",
  Succeeded = "succeeded",
  Failed = "failed",
  Stopped = "stopped",
}

export type WorkflowProcess = {
  status: WorkflowRunningStatus;
  tracing: NodeTracing[];
  expand?: boolean; // for UI
};

export enum CodeLanguage {
  python3 = "python3",
  javascript = "javascript",
  json = "json",
}

export type LogAnnotation = {
  content: string;
  account: {
    id: string;
    name: string;
    email: string;
  };
  created_at: number;
};

export type Annotation = {
  id: string;
  authorName: string;
  logAnnotation?: LogAnnotation;
  created_at?: number;
};

export type UploadFileRespond = {
  id: string;
  name: string;
  size: number;
  extension: string;
  mimeType: string;
};

export interface RcFile extends File {
  uid: string;
}
export interface FileType extends RcFile {
  readonly lastModifiedDate: Date;
}
export type UploadFileStatus =
  | "error"
  | "success"
  | "done"
  | "uploading"
  | "removed";
export interface HttpRequestHeader {
  [key: string]: string;
}
export interface UploadFile<T = UploadFileRespond> {
  uid: string;
  size?: number;
  name: string;
  fileName?: string;
  lastModified?: number;
  lastModifiedDate?: Date;
  url?: string;
  status?: UploadFileStatus;
  percent?: number;
  thumbUrl?: string;
  crossOrigin?: ImgHTMLAttributes["crossorigin"];
  originFileObj?: FileType;
  response?: T;
  error?: any;
  linkProps?: any;
  type?: string;
  xhr?: T;
  preview?: string;
}

export enum InputVarType {
  textInput = "text-input",
  paragraph = "paragraph",
  select = "select",
  number = "number",
  url = "url",
  files = "files",
  json = "json", // obj, array
  contexts = "contexts", // knowledge retrieval
  iterator = "iterator", // iteration input
  singleFile = "file",
  multiFiles = "file-list",
  loop = "loop", // loop input
}
