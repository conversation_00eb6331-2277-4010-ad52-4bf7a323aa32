<template>
  <div :class="chatFooterClass">
    <div class="tools">
      <div class="tool-item" @click="onAddConversation">
        <IconFont type="icon-c003xiaoxiduihua" class="tool-icon" />
        <div>新对话</div>
      </div>
      <div class="tool-item" @click="onToggleHistoryVisible">
        <IconFont type="icon-lishi" class="tool-icon" />
        <div>历史对话</div>
      </div>
      <div class="tool-item" @click="onOpenAgents">
        <IconFont type="icon-zhinengzhuli" class="tool-icon" />
        <div>智能会话</div>
      </div>
    </div>
    <div class="composer">
      <div class="composer-input-wrapper">
        <AutoComplete
          ref="inputRef"
          id="chatInput"
          :value="inputMsg"
          @update:value="handleInputChange"
          :class="['composer-input']"
          :placeholder="placeholderText"
          :open="open"
          :popup-class-name="autoCompleteDropdownClass"
          :default-active-first-option="false"
          :get-popup-container="getPopupContainer"
          :dropdownMatchSelectWidth="false"
          :list-height="500"
          :allow-clear="false"
          :options="autoCompleteOptions"
          @select="onSelect"
          @focus="inputFocus"
          @blur="inputBlur"
          @keydown="handleKeyDown"
        >
          <!-- allow-clear -->
          <template #option="option">
            <div class="option-content">
              <template v-if="option.options">
                <div>
                  {{ option.label }}
                </div>
              </template>
              <template v-else>
                <div>
                  <a-tag
                    v-if="option.schemaElementType"
                    :class="['semantic-type']"
                    :color="getSemanticTypeColor(option.schemaElementType)"
                  >
                    {{
                      SEMANTIC_TYPE_MAP[option.schemaElementType] ||
                      option.schemaElementType ||
                      "维度"
                    }}
                  </a-tag>
                  {{ option.subRecommend }}
                </div>
              </template>
            </div>
          </template>
        </AutoComplete>
        <div
          :class="['send-btn', { 'send-btn-active': inputMsg?.length > 0 }]"
          @click="sendMsg(inputMsg)"
        >
          <IconFont type="icon-ios-send" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import { debounce } from "lodash";
import { AutoComplete, Tag as ATag } from "ant-design-vue";
// import type { SelectProps } from "ant-design-vue";
import IconFont from "@/components/IconFont/index.vue";
import { getTextWidth, groupByColumn, isMobile } from "@/utils/utils";
import { SemanticTypeEnum, SEMANTIC_TYPE_MAP, HOLDER_TAG } from "../constants";
import type { AgentType, ModelType } from "../type";
import { searchRecommend } from "@/service/index";
import { ClientAgentType } from "@/types/client";

// 类型声明
interface RecommendItem {
  dataSetName?: string;
  recommend: string;
  subRecommend: string;
  dataSetIds?: number;
  dataSetId?: number;
  schemaElementType?: SemanticTypeEnum;
}

const props = defineProps<{
  inputMsg: string;
  chatId?: number;
  currentAgent?: AgentType; // 保留 currentAgent 以便推荐接口使用
  // agentList: AgentType[]; // 移除 agentList
}>();

const emit = defineEmits<{
  (e: "update:inputMsg", value: string): void;
  (e: "toggleHistoryVisible"): void;
  (e: "openAgents"): void;
  (e: "sendMsg", msg: string, dataSetId?: number): void;
  (e: "addConversation"): void;
  // (e: "selectAgent", agent: AgentType): void; // 移除 selectAgent
}>();

// 状态
const isPinyin = ref(false);
const isSelect = ref(false);
const modelOptions = ref<(ModelType | AgentType)[]>([]);
const stepOptions = ref<Record<string, RecommendItem[]>>({});
const open = ref(false);
const focused = ref(false);
const inputRef = ref<HTMLInputElement>();
const fetchRef = ref(0);

// 计算属性
const chatFooterClass = computed(() => ({
  "chat-footer": true,
  mobile: isMobile,
}));

const autoCompleteDropdownClass = computed(() => {
  const classNames = ["auto-complete-dropdown"];

  if (isMobile) {
    classNames.push("mobile");
  }

  if (modelOptions.value.length > 0) {
    classNames.push("model-options");
  }

  return classNames.join(" ");
});

const placeholderText = "请输入聊天内容...";

// 方法
const onAddConversation = () => {
  emit("addConversation");
};

const onToggleHistoryVisible = () => {
  emit("toggleHistoryVisible");
};

const onOpenAgents = () => {
  emit("openAgents");
};

const compositionStartEvent = () => {
  isPinyin.value = true;
};

const compositionEndEvent = () => {
  isPinyin.value = false;
};

const initEvents = () => {
  const autoCompleteEl = document.getElementById("chatInput");
  if (autoCompleteEl) {
    autoCompleteEl.addEventListener("compositionstart", compositionStartEvent);
    autoCompleteEl.addEventListener("compositionend", compositionEndEvent);
  }
};

const removeEvents = () => {
  const autoCompleteEl = document.getElementById("chatInput");
  if (autoCompleteEl) {
    autoCompleteEl.removeEventListener(
      "compositionstart",
      compositionStartEvent
    );
    autoCompleteEl.removeEventListener("compositionend", compositionEndEvent);
  }
};

/**
 * 从stepOptions中补全数据
 */
const autoCompleteOptions = computed(() => {
  // 当显示推荐内容时
  if (Object.keys(stepOptions.value).length === 0) {
    return [];
  }

  // 组装options数据
  const options: { label?: string; options?: any[]; value?: string }[] = [];

  // 遍历每个分组
  Object.keys(stepOptions.value).forEach(key => {
    // 分组下的选项
    const groupOptions = stepOptions.value[key].map(option => {
      return {
        value: `${getOptionValue(option)}${HOLDER_TAG}`,
        label: option.subRecommend,
        schemaElementType: option.schemaElementType,
        subRecommend: option.subRecommend,
      };
    });

    // 添加分组
    options.push({
      label: key,
      options: groupOptions,
    });
  });

  return options;
});

const getStepOptions = (recommends: RecommendItem[]) => {
  const data = groupByColumn(recommends, "dataSetName");
  return isMobile && recommends.length > 6
    ? Object.keys(data)
        .slice(0, 4)
        .reduce(
          (result, key) => {
            result[key] = data[key].slice(
              0,
              Object.keys(data).length > 2
                ? 2
                : Object.keys(data).length > 1
                  ? 3
                  : 6
            );
            return result;
          },
          {} as Record<string, RecommendItem[]>
        )
    : data;
};

const processMsg = (msg: string) => {
  let msgValue = msg;
  let dataSetId: number | undefined;
  // 移除 agentList 相关逻辑
  return { msgValue, dataSetId };
};

const debounceGetWords = debounce(
  async (msg: string, chatId?: number, currentAgent?: AgentType) => {
    console.log(currentAgent)
    if (isPinyin.value) {
      return;
    }
    if (msg === "" || (msg.length === 1 && msg[0] === "@")) {
      return;
    }
    fetchRef.value += 1;
    const fetchId = fetchRef.value;
    const { msgValue, dataSetId } = processMsg(msg);
    const res = await searchRecommend(
      msgValue.trim(),
      chatId,
      dataSetId,
      currentAgent?.id
    );
    if (fetchId !== fetchRef.value) {
      return;
    }
    const recommends = msgValue ? ((res.data || []) as RecommendItem[]) : [];
    const stepOptionList = recommends.map(item => item.subRecommend);
    if (
      stepOptionList.length > 0 &&
      stepOptionList.every(item => item !== null)
    ) {
      stepOptions.value = getStepOptions(recommends);
    } else {
      stepOptions.value = {};
    }
    open.value = recommends.length > 0;
  },
  200
);

const getSemanticTypeColor = (type: SemanticTypeEnum): string => {
  switch (type) {
    case SemanticTypeEnum.DIMENSION:
    case SemanticTypeEnum.MODEL:
      return "blue";
    case SemanticTypeEnum.VALUE:
      return "geekblue";
    default:
      return "cyan";
  }
};

const getOptionValue = (option: RecommendItem): string => {
  let optionValue =
    Object.keys(stepOptions.value).length === 1
      ? option.recommend
      : `${option.dataSetName || ""}${option.recommend}`;
  // 移除 agentList 相关逻辑
  return optionValue;
};

const sendMsg = (value: string) => {
  const option = Object.keys(stepOptions.value)
    .reduce((result: RecommendItem[], item) => {
      result = result.concat(stepOptions.value[item]);
      return result;
    }, [])
    .find(item =>
      Object.keys(stepOptions.value).length === 1
        ? item.recommend === value
        : `${item.dataSetName || ""}${item.recommend}` === value
    );

  if (option && isSelect.value) {
    emit("sendMsg", option.recommend, option.dataSetIds);
  } else {
    emit("sendMsg", value.trim(), option?.dataSetId);
  }
};

const onSelect = (value: string) => {
  isSelect.value = true;
  // 移除 agentList 相关逻辑
  emit("update:inputMsg", value.replace(HOLDER_TAG, ""));
  open.value = false;
  setTimeout(() => {
    isSelect.value = false;
  }, 200);
};

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.code === "Enter" || e.code === "NumpadEnter") {
    const chatInputEl = document.getElementById(
      "chatInput"
    ) as HTMLInputElement;
    if (!isSelect.value && !isPinyin.value) {
      sendMsg(chatInputEl.value);
      open.value = false;
    }
  }
};

// const handleFocus = () => {
//   focused.value = true;
// };

// const handleBlur = () => {
//   focused.value = false;
// };

const inputFocus = () => {
  focused.value = true;
};

const inputBlur = () => {
  focused.value = false;
};

defineExpose({
  inputFocus,
  inputBlur,
});

const handleInputChange = (value: string) => {
  emit("update:inputMsg", value);
};

const getPopupContainer = (triggerNode: HTMLElement): HTMLElement => {
  return triggerNode.parentNode as HTMLElement;
};

// 生命周期钩子
onMounted(() => {
  initEvents();
});

onUnmounted(() => {
  removeEvents();
});

// 监听
watch(
  () => props.inputMsg,
  newMsg => {
    if (!isSelect.value) {
      debounceGetWords(newMsg, props.chatId, props.currentAgent);
    } else {
      isSelect.value = false;
    }
    if (!newMsg) {
      stepOptions.value = {};
      fetchRef.value = 0;
    }
  }
);

watch(focused, newFocused => {
  if (!newFocused) {
    open.value = false;
  }
});

watch(stepOptions, () => {
  nextTick(() => {
    const autoCompleteDropdown = document.querySelector(
      ".auto-complete-dropdown"
    ) as HTMLElement;

    if (!autoCompleteDropdown) {
      return;
    }
    const textWidth = getTextWidth(props.inputMsg);
    if (Object.keys(stepOptions.value).length > 0) {
      autoCompleteDropdown.style.marginLeft = `${textWidth}px`;
    } else {
      setTimeout(() => {
        autoCompleteDropdown.style.marginLeft = "0px";
      }, 200);
    }
  });
});
</script>

<style lang="less" scoped>
.chat-footer {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  margin: 6px 20px 20px;

  .tools {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    column-gap: 8px;

    .tool-item {
      display: flex;
      align-items: center;
      padding: 2px 6px;
      color: var(--text-color-secondary);
      font-size: 12px;
      column-gap: 6px;
      background-color: #f6f6f6;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        background-color: #f1f1f1;
      }
    }
  }

  .composer {
    display: flex;
    height: 70px;

    .collapse-btn {
      height: 46px;
      margin: 0 10px;
      color: var(--text-color-third);
      font-size: 20px;
      line-height: 46px;
      cursor: pointer;

      &:hover {
        color: var(--chat-blue);
      }
    }

    .add-conversation {
      height: 46px;
      margin: 0 20px 0 10px;
      color: var(--text-color-fourth);
      font-size: 26px;
      line-height: 54px;
      cursor: pointer;

      &:hover {
        color: var(--chat-blue);
      }
    }

    .composer-input-wrapper {
      position: relative;
      flex: 1;

      .composer-input {
        width: 100%;
        height: 100%;
        :deep(.ant-select-selector) {
          box-sizing: border-box;
          height: 100%;
          overflow: hidden;
          color: rgba(0, 0, 0, 0.87);
          font-size: 14px;
          word-break: break-all;
          background: #f9f9f9;
          border: 2px solid transparent;
          border-radius: 8px;
          transition: all 0.15s ease-in-out;
          resize: none;

          .ant-select-selection-search-input {
            height: 40px !important;
            padding: 0 12px;
          }

          .ant-select-selection-search {
            right: 0 !important;
            left: 0 !important;
          }

          .ant-select-selection-placeholder {
            line-height: 40px;
            margin-bottom: 30px;
          }
        }

        :deep(.ant-select-clear) {
          right: auto;
          left: 500px;
          width: 16px;
          height: 16px;
          margin-top: -8px;
          font-size: 16px;
        }
      }

      :deep(.ant-select-focused) {
        .ant-select-selector {
          box-shadow:
            0 6px 16px 0 rgba(0, 0, 0, 0.08),
            0 3px 6px -4px rgba(0, 0, 0, 0.12),
            0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
          border: 2px solid #1677ff !important;
        }
      }
    }
  }

  .send-btn {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: #fff;
    font-size: 20px;
    background-color: rgb(184, 184, 191);
    border: unset;
    border-radius: 50%;
    transition: background-color 0.3s ease 0s;

    &.send-btn-active {
      background-color: var(--chat-blue);
    }
  }

  &.mobile {
    margin: 6px 10px 10px;

    .add-conversation {
      height: 40px;
      margin: 0 12px 0 4px;
    }

    .composer {
      height: 40px;
      :deep(.ant-select-selector) {
        font-size: 14px !important;
      }

      :deep(.ant-select-selection-search-input) {
        padding: 0 10px !important;
      }

      :deep(.ant-select-selection-placeholder) {
        margin-bottom: 0 !important;
        padding-left: 0 !important;
        line-height: 38px !important;
      }
    }

    .send-btn {
      right: 4px;
      bottom: 6px;
    }
  }
}

.search-option {
  padding: 6px 20px;
  color: #212121;
  font-size: 16px;
}

.mobile {
  .search-option {
    min-height: 26px;
    padding: 2px 12px;
    font-size: 14px;
  }
}

.model {
  margin-top: 2px;
  color: var(--text-color-fourth);
  font-size: 13px;
  line-height: 12px;
}

.auto-complete-dropdown {
  left: 20px !important;
  width: fit-content !important;
  min-width: 100px !important;
  border-radius: 6px;

  &.model-options {
    width: 150px !important;

    .search-option {
      padding: 0 10px;
      color: var(--text-color-secondary);
      font-size: 14px;
    }

    :deep(.ant-select-item) {
      height: 30px !important;
      line-height: 30px !important;
    }
  }
}

.semantic-type {
  margin-right: 10px;
}

.quote-text {
  color: var(--chat-blue);
}
</style>
