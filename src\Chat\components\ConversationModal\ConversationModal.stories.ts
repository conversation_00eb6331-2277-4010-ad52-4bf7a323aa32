// import { fn } from "@storybook/test";
import type { <PERSON>a, StoryObj } from "@storybook/vue3";
import { ref } from "vue";

import ConversationModal from "./index.vue";
import type { ConversationDetailType } from "../../type";
// 确保引入Ant Design Vue组件
import { Form, Input, Modal } from "ant-design-vue";

// 模拟数据
const mockConversation: ConversationDetailType = {
  chatId: 12345,
  chatName: "示例对话名称",
  createTime: "2023-10-20 12:30:45",
  creator: "张三",
  lastQuestion: "最新的问题是什么？",
  lastTime: "2023-10-21 09:15:20"
};

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta = {
  title: "Chat/ConversationModal",
  component: ConversationModal,
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  argTypes: {
    visible: {
      control: "boolean",
      description: "是否显示对话框",
    },
    editConversation: {
      control: "object",
      description: "需要编辑的对话信息",
    },
    onClose: {
      action: "closed",
      description: "关闭对话框的回调",
    },
    onFinish: {
      action: "finished",
      description: "完成编辑的回调",
    },
  },
  args: {
    visible: true,
    editConversation: mockConversation,
  },
  parameters: {
    docs: {
      description: {
        component: '对话框名称修改弹窗。',
      },
    },
  },
} satisfies Meta<typeof ConversationModal>;

export default meta;
type Story = StoryObj<typeof meta>;

interface StoryArgs {
  visible: boolean;
  editConversation: ConversationDetailType;
  onClose?: () => void;
  onFinish?: (name: string) => void;
}

// 基本展示
export const Default: Story = {
  args: {
    visible: false,
    editConversation: mockConversation,
  },
  render: (args) => ({
    components: { 
      ConversationModal,
      AModal: Modal,
      AForm: Form,
      AFormItem: Form.Item,
      AInput: Input,
    },
    setup() {
      const visible = ref((args as StoryArgs).visible);
      const editConversation = ref((args as StoryArgs).editConversation);
      
      const handleClose = () => {
        visible.value = false;
        console.log("对话框已关闭");
      };
      
      const handleFinish = (name: string) => {
        visible.value = false;
        console.log(`修改完成，新名称: ${name}`);
      };
      
      return { 
        visible, 
        editConversation, 
        handleClose, 
        handleFinish 
      };
    },
    template: `
      <div>
        <ConversationModal
          :visible="visible"
          :editConversation="editConversation"
          :onClose="handleClose"
          :onFinish="handleFinish"
        />
      </div>
    `,
  }),
};

// 交互式示例
export const Interactive: Story = {
  args: {
    visible: false,
    editConversation: mockConversation,
  },
  render: (args) => ({
    components: { 
      ConversationModal,
      AModal: Modal,
      AForm: Form,
      AFormItem: Form.Item,
      AInput: Input,
    },
    setup() {
      const visible = ref((args as StoryArgs).visible);
      const editConversation = ref((args as StoryArgs).editConversation);
      
      const showModal = () => {
        visible.value = true;
      };
      
      const handleClose = () => {
        visible.value = false;
        console.log("对话框已关闭");
      };
      
      const handleFinish = (name: string) => {
        visible.value = false;
        editConversation.value.chatName = name;
        console.log(`修改完成，新名称: ${name}`);
      };
      
      return { 
        visible, 
        editConversation, 
        showModal,
        handleClose, 
        handleFinish 
      };
    },
    template: `
      <div>
        <button @click="showModal" style="padding: 8px 16px; margin-bottom: 16px;">打开修改对话名称弹窗</button>
        <p>当前对话名称: {{ editConversation.chatName }}</p>
        <ConversationModal
          :visible="visible"
          :editConversation="editConversation"
          :onClose="handleClose"
          :onFinish="handleFinish"
        />
      </div>
    `,
  }),
};

// 空名称验证示例
export const EmptyValidation: Story = {
  args: {
    visible: false,
    editConversation: {
      ...mockConversation,
      chatName: "", // 空名称
    },
  },
  render: (args) => ({
    components: { 
      ConversationModal,
      AModal: Modal,
      AForm: Form,
      AFormItem: Form.Item,
      AInput: Input,
    },
    setup() {
      const visible = ref((args as StoryArgs).visible);
      const editConversation = ref((args as StoryArgs).editConversation);
      
      const handleClose = () => {
        visible.value = false;
      };
      
      const handleFinish = (name: string) => {
        console.log("修改完成，新名称: ", name);
        visible.value = false;
      };
      
      return { 
        visible, 
        editConversation, 
        handleClose, 
        handleFinish 
      };
    },
    template: `
      <div>
        <ConversationModal
          :visible="visible"
          :editConversation="editConversation"
          :onClose="handleClose"
          :onFinish="handleFinish"
        />
      </div>
    `,
  }),
}; 