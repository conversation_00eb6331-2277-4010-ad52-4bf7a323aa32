<template>
  <div :class="prefixCls">
    <div :class="chartHolderClass" />
    <div :class="`${prefixCls}-no-permission`">
      <ApplyAuth :model="model" @apply-auth="onApplyAuth" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import classNames from 'classnames';
import { CLS_PREFIX } from '../../../common/constants';
import ApplyAuth from '../ApplyAuth/index.vue';

const props = defineProps<{
  model: string;
  chartType?: string;
}>();

const emit = defineEmits<{
  (e: 'applyAuth', model: string): void;
}>();

const prefixCls = `${CLS_PREFIX}-no-permission-chart`;

const chartHolderClass = computed(() => 
  classNames(`${prefixCls}-holder`, {
    [`${prefixCls}-bar-chart-holder`]: props.chartType === 'barChart',
  })
);

const onApplyAuth = (model: string) => {
  emit('applyAuth', model);
};
</script>

<style lang="less" scoped>
@import './style.less';
</style>
