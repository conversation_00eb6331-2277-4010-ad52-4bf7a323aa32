<template>
  <div :class="prefixCls">
    <div v-if="!isMobile" :class="`${prefixCls}-feedback`">
      <!-- <div>这个回答正确吗？</div> -->

      <div :class="`${prefixCls}-feedback-left`">
        <template v-if="!isParserError">
          <AButton
            size="small"
            @click="handleExportData"
            type="text"
            :loading="exportLoading"
          >
            <template #icon><DownloadOutlined /></template>
            <span :class="`${prefixCls}-font-style`">导出数据</span>
          </AButton>
          <AButton
            v-if="!isSimpleMode"
            size="small"
            @click="handleDownloadImage"
            type="text"
          >
            <template #icon><FileJpgOutlined /></template>
            <span :class="`${prefixCls}-font-style`">导出图片</span>
          </AButton>
          <AButton
            v-if="isLastMessage"
            size="small"
            @click="handleReExecute"
            type="text"
          >
            <template #icon><RedoOutlined /></template>
            <span :class="`${prefixCls}-font-style`">再试一次</span>
          </AButton>
        </template>
      </div>
      <div :class="`${prefixCls}-feedback-left`">
        <LikeOutlined 
          :class="likeClass" 
          @click="like" 
          style="margin-right: 10px" 
        />
        <DislikeOutlined
          :class="dislikeClass"
          @click="handleDislike"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject } from 'vue';
import { 
  DislikeOutlined,
  LikeOutlined,
  DownloadOutlined,
  RedoOutlined,
  FileJpgOutlined,
} from '@ant-design/icons-vue';
import { Button as AButton } from 'ant-design-vue';
import { isMobile } from '../../utils/utils';
import { CLS_PREFIX } from '../../common/constants';
import { updateQAFeedback } from '../../service';

// 定义组件的props
const props = defineProps({
  queryId: {
    type: Number,
    required: true
  },
  scoreValue: {
    type: Number,
    default: 0
  },
  isLastMessage: {
    type: Boolean,
    default: false
  },
  isParserError: {
    type: Boolean,
    default: false
  },
  isSimpleMode: {
    type: Boolean,
    default: false
  },
  onExportData: {
    type: Function,
    default: () => {}
  },
  onReExecute: {
    type: Function,
    default: () => {}
  }
});

const score = ref(props.scoreValue || 0);
const exportLoading = ref(false);
const prefixCls = `${CLS_PREFIX}-tools`;

// 在Vue3中使用inject替代React的useContext
const chartItemContext = inject('chartItemContext', {
  call: (method: string) => {
    console.warn(`Method ${method} not implemented`);
  }
});

const like = () => {
  score.value = 5;
  updateQAFeedback(props.queryId, 5);
};

const dislike = () => {
  score.value = 1;
  updateQAFeedback(props.queryId, 1);
};

const handleDislike = (e: Event) => {
  e.stopPropagation();
  dislike();
};

const handleExportData = () => {
  exportLoading.value = true;
  props.onExportData?.();
  setTimeout(() => {
    exportLoading.value = false;
  }, 1000);
};

const handleDownloadImage = () => {
  chartItemContext.call('downloadChartAsImage');
};

const handleReExecute = () => {
  props.onReExecute?.(props.queryId);
};

const likeClass = computed(() => {
  return {
    [`${prefixCls}-like`]: true,
    [`${prefixCls}-feedback-active`]: score.value === 5,
  };
});

const dislikeClass = computed(() => {
  return {
    [`${prefixCls}-dislike`]: true,
    [`${prefixCls}-feedback-active`]: score.value === 1,
  };
});
</script>

<style lang="less" scoped>
@import './style.less';
</style>
