.showCase {
  position: relative;
  height: 100%;
  padding: 0 20px;
  overflow-y: auto;
  padding-bottom: 2px;

  .showCaseContent {
    column-count: 2;
    column-gap: 20px;

    .showcaseLoading {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 400px;
    }

    .showCaseItem {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      padding: 12px;
      margin-bottom: 20px;
      overflow-y: auto;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.14), 0 0 2px rgba(0, 0, 0, 0.12);
      background: linear-gradient(180deg, rgba(23, 74, 228, 0) 29.44%, rgba(23, 74, 228, 0.06) 100%),
      linear-gradient(90deg, #f3f3f7 0%, #f3f3f7 20%, #ebf0f9 60%, #f3f3f7 80%, #f3f3f7 100%);

      .showCaseChatItem {
        display: flex;
        flex-direction: column;
        row-gap: 12px;
      }
    }
  }

  &.mobile {
    padding: 0 4px;
    .showCaseContent {
      column-count: 1;
    }
  }
}
