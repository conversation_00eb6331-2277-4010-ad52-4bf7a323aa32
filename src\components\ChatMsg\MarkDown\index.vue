<template>
  <div :class="`${prefixCls} markdown-body`" style="font-size: 14px">
    <a-spin :spinning="loading">
      <!-- <v-md-preview :text="markdown" /> -->
      <MdPreview :id="id" :modelValue="markdown" />
      <!-- <MdCatalog :editorId="id" :scrollElement="scrollElement" /> -->
    </a-spin>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";
import { CLS_PREFIX } from "../../../common/constants";
import { Spin } from "ant-design-vue";

import { MdPreview, MdCatalog, config } from "md-editor-v3";
import "md-editor-v3/lib/preview.css";

import screenfull from "screenfull";

import katex from "katex";
import "katex/dist/katex.min.css";

import Cropper from "cropperjs";
import "cropperjs/dist/cropper.css";

import mermaid from "mermaid";

import highlight from "highlight.js";
import "highlight.js/styles/atom-one-dark.css";

import * as prettier from "prettier";
import parserMarkdown from "prettier/plugins/markdown";

export default defineComponent({
  name: "MarkDown",
  components: {
    // VMdPreview,
    ASpin: Spin,
    MdPreview,
    MdCatalog,
  },
  props: {
    markdown: {
      type: String,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    onApplyAuth: {
      type: Function as PropType<(model: string) => void>,
      default: undefined,
    },
  },
  setup() {
    const prefixCls = `${CLS_PREFIX}-markdown`;

    const scrollElement = document.documentElement;
    const id = "preview-only";

    config({
      editorExtensions: {
        prettier: {
          prettierInstance: prettier,
          parserMarkdownInstance: parserMarkdown,
        },
        highlight: {
          instance: highlight,
        },
        screenfull: {
          instance: screenfull,
        },
        katex: {
          instance: katex,
        },
        cropper: {
          instance: Cropper,
        },
        mermaid: {
          instance: mermaid,
        },
      },
    });

    return {
      prefixCls,
      id,
      scrollElement,
    };
  },
});
</script>

<style lang="less" scoped>
@import "./style.less";
:deep(.md-editor-preview) {
  font-size: 14px;
}

:deep(.md-editor) {
  background: #fcfcfd;
}

:deep(.think-container) {
  // color: #999;
  background-color: #f5f7f9;
  border-radius: 6px;
  padding: 10px 12px;
}
</style>
