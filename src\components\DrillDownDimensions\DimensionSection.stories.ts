import type { Meta, StoryObj } from '@storybook/vue3';
import DimensionSection from './DimensionSection.vue';
import { ref } from 'vue';
import type { DrillDownDimensionType } from '../../common/type';

// 引入必要的样式
import './index.vue';

const meta: Meta<typeof DimensionSection> = {
  title: 'Components/DrillDownDimensions/DimensionSection',
  component: DimensionSection,
  tags: ['autodocs'],
  argTypes: {
    drillDownDimension: { control: 'object' },
    dimensions: { control: 'object' },
    isSecondDrillDown: { control: 'boolean' },
    onSelectDimension: { action: 'onSelectDimension' },
    onCancelDrillDown: { action: 'onCancelDrillDown' },
  },
};

export default meta;
type Story = StoryObj<typeof DimensionSection>;

// 模拟数据
const mockDimensions: DrillDownDimensionType[] = [
  { id: 1, name: '维度1', bizName: 'dimension1', model: 1 },
  { id: 2, name: '维度2', bizName: 'dimension2', model: 1 },
  { id: 3, name: '维度3', bizName: 'dimension3', model: 1 },
  { id: 4, name: '维度4', bizName: 'dimension4', model: 1 },
  { id: 5, name: '维度5', bizName: 'dimension5', model: 1 },
  { id: 6, name: '维度6', bizName: 'dimension6', model: 1 },
  { id: 7, name: '维度7', bizName: 'dimension7', model: 1 },
];

// 基础故事
export const Default: Story = {
  render: (args) => ({
    components: { DimensionSection },
    setup() {
      const selectedDimension = ref<DrillDownDimensionType | undefined>(undefined);
      const selectDimension = (dimension?: DrillDownDimensionType) => {
        selectedDimension.value = dimension;
        if (args.onSelectDimension) {
          args.onSelectDimension(dimension);
        }
      };
      const cancelDrillDown = () => {
        if (args.onCancelDrillDown) {
          args.onCancelDrillDown();
        }
      };
      return { 
        dimensions: args.dimensions, 
        isSecondDrillDown: args.isSecondDrillDown, 
        selectedDimension, 
        selectDimension, 
        cancelDrillDown 
      };
    },
    template: `
      <DimensionSection
        :drillDownDimension="selectedDimension"
        :dimensions="dimensions"
        :isSecondDrillDown="isSecondDrillDown"
        :onSelectDimension="selectDimension"
        :onCancelDrillDown="cancelDrillDown"
      />
    `,
  }),
  args: {
    dimensions: mockDimensions,
    isSecondDrillDown: false,
  },
};

// 已选择维度的故事
export const WithSelectedDimension: Story = {
  render: (args) => ({
    components: { DimensionSection },
    setup() {
      const selectedDimension = ref<DrillDownDimensionType>(mockDimensions[0]);
      const selectDimension = (dimension?: DrillDownDimensionType) => {
        selectedDimension.value = dimension || mockDimensions[0];
        if (args.onSelectDimension) {
          args.onSelectDimension(dimension);
        }
      };
      const cancelDrillDown = () => {
        if (args.onCancelDrillDown) {
          args.onCancelDrillDown();
        }
      };
      return { 
        dimensions: args.dimensions, 
        isSecondDrillDown: args.isSecondDrillDown, 
        selectedDimension, 
        selectDimension, 
        cancelDrillDown 
      };
    },
    template: `
      <DimensionSection
        :drillDownDimension="selectedDimension"
        :dimensions="dimensions"
        :isSecondDrillDown="isSecondDrillDown"
        :onSelectDimension="selectDimension"
        :onCancelDrillDown="cancelDrillDown"
      />
    `,
  }),
  args: {
    dimensions: mockDimensions,
    isSecondDrillDown: false,
  },
};

// 二级下钻维度的故事
export const SecondDrillDown: Story = {
  render: (args) => ({
    components: { DimensionSection },
    setup() {
      const selectedDimension = ref<DrillDownDimensionType>(mockDimensions[1]);
      const selectDimension = (dimension?: DrillDownDimensionType) => {
        selectedDimension.value = dimension || mockDimensions[1];
        if (args.onSelectDimension) {
          args.onSelectDimension(dimension);
        }
      };
      const cancelDrillDown = () => {
        if (args.onCancelDrillDown) {
          args.onCancelDrillDown();
        }
      };
      return { 
        dimensions: args.dimensions, 
        isSecondDrillDown: args.isSecondDrillDown, 
        selectedDimension, 
        selectDimension, 
        cancelDrillDown 
      };
    },
    template: `
      <DimensionSection
        :drillDownDimension="selectedDimension"
        :dimensions="dimensions"
        :isSecondDrillDown="isSecondDrillDown"
        :onSelectDimension="selectDimension"
        :onCancelDrillDown="cancelDrillDown"
      />
    `,
  }),
  args: {
    dimensions: mockDimensions,
    isSecondDrillDown: true,
  },
}; 