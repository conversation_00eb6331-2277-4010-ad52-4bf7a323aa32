<template>
  <a-spin :spinning="loading" size="large">
    <div :class="showCaseClass" :style="{ height }" ref="showcaseRef">
      <div class="showCaseContent">
        <div v-for="showCaseItem in showCaseList" :key="showCaseItem.caseId" class="showCaseItem">
          <div 
            v-for="chatItem in showCaseItem.msgList
              .filter(item => !!item.queryResult)
              .slice(0, 1)" 
            :key="chatItem.questionId" 
            class="showCaseChatItem"
          >
            <Text position="right" :data="chatItem.queryText" :anonymousUser="true" />
            <ChatItem
              :msg="chatItem.queryText"
              :parseInfos="chatItem.parseInfos"
              :msgData="chatItem.queryResult"
              :conversationId="chatItem.chatId"
              :agentId="agentId"
              integrateSystem="showcase"
              :score="chatItem.score"
              @sendMsg="onSendMsg"
            />
          </div>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { ShowCaseItemType } from './type';
import { queryShowCase } from './service';
import Text from '../Chat/components/Text/index.vue';
import ChatItem from '../components/ChatItem/index.vue';
import { HistoryMsgItemType } from '../common/type';
import { Spin } from 'ant-design-vue';
import classNames from 'classnames';
import { isMobile } from '../utils/utils';
import { throttle } from 'lodash';

export default defineComponent({
  name: 'ShowCase',
  components: {
    Text,
    ChatItem,
    ASpin: Spin
  },
  props: {
    height: {
      type: [Number, String],
      default: undefined
    },
    agentId: {
      type: Number,
      required: true
    }
  },
  emits: ['sendMsg'],
  setup(props, { emit }) {
    const showCaseList = ref<ShowCaseItemType[]>([]);
    const loading = ref(false);
    const pageNo = ref(1);
    const showcaseRef = ref<HTMLElement | null>(null);

    const updateData = async (pageNoValue: number) => {
      if (pageNoValue === 1) {
        loading.value = true;
      }
      const res = await queryShowCase(props.agentId, pageNoValue, isMobile ? 10 : 20);
      if (pageNoValue === 1) {
        loading.value = false;
      }
      const showCaseMapRes: any = res.data.showCaseMap || {};
      const list = Object.keys(showCaseMapRes)
        .reduce((result: ShowCaseItemType[], key: string) => {
          result.push({ msgList: showCaseMapRes[key], caseId: key });
          return result;
        }, [])
        .sort((a, b) => {
          return (b.msgList?.[0]?.score || 3) - (a.msgList?.[0]?.score || 3);
        });
      showCaseList.value = pageNoValue === 1 ? list : [...showCaseList.value, ...list];
    };

    const handleScroll = throttle((e: Event) => {
      const target = e.target as HTMLElement;
      const bottom =
        target.scrollHeight - target.scrollTop === target.clientHeight ||
        target.scrollHeight - target.scrollTop === target.clientHeight + 0.5;
      if (bottom) {
        updateData(pageNo.value + 1);
        pageNo.value = pageNo.value + 1;
      }
    }, 200);

    const showCaseClass = computed(() => {
      return classNames('showCase', { mobile: isMobile });
    });

    const onSendMsg = (msg: string) => {
      emit('sendMsg', msg);
    };

    onMounted(() => {
      if (!isMobile && showcaseRef.value) {
        showcaseRef.value.addEventListener('scroll', handleScroll);
      }
    });

    onUnmounted(() => {
      if (!isMobile && showcaseRef.value) {
        showcaseRef.value.removeEventListener('scroll', handleScroll);
      }
    });

    watch(() => props.agentId, (newAgentId) => {
      if (newAgentId) {
        showCaseList.value = [];
        updateData(1);
        pageNo.value = 1;
      }
    });

    return {
      showCaseList,
      loading,
      showcaseRef,
      showCaseClass,
      onSendMsg
    };
  }
});
</script>

<style lang="less" scoped>
.showCase {
  position: relative;
  height: 100%;
  padding: 0 20px;
  overflow-y: auto;
  padding-bottom: 2px;

  .showCaseContent {
    column-count: 2;
    column-gap: 20px;

    .showcaseLoading {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 400px;
    }

    .showCaseItem {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      padding: 12px;
      margin-bottom: 20px;
      overflow-y: auto;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.14), 0 0 2px rgba(0, 0, 0, 0.12);
      background: linear-gradient(180deg, rgba(23, 74, 228, 0) 29.44%, rgba(23, 74, 228, 0.06) 100%),
      linear-gradient(90deg, #f3f3f7 0%, #f3f3f7 20%, #ebf0f9 60%, #f3f3f7 80%, #f3f3f7 100%);

      .showCaseChatItem {
        display: flex;
        flex-direction: column;
        row-gap: 12px;
      }
    }
  }

  &.mobile {
    padding: 0 4px;
    .showCaseContent {
      column-count: 1;
    }
  }
}
</style> 