<template>
  <a-modal
    :title="'修改问答名称'"
    :open="visible"
    @cancel="onClose"
    @ok="onConfirm"
    :confirm-loading="loading"
  >
    <a-form
      :model="formState"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      ref="formRef"
    >
      <a-form-item
        name="conversationName"
        label="名称"
        :rules="[
          { required: true, message: '请输入问答名称' },
        ]"
      >
        <a-input
          v-model:value="formState.conversationName"
          placeholder="请输入问答名称"
          ref="conversationNameInputRef"
          @pressEnter="onConfirm"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, watch, nextTick } from "vue";
import type { ConversationDetailType } from "../../type";
import { updateConversationName } from "../../service";
import { Form, Input, Modal } from "ant-design-vue";
import { PropType } from "vue";

export default defineComponent({
  name: "ConversationModal",
  components: {
    AModal: Modal,
    AForm: Form,
    AFormItem: Form.Item,
    AInput: Input,
  },
  props: {
    // 我们使用visible属性，但在a-modal组件中使用:open属性，以满足新版Ant Design Vue的要求
    visible: {
      type: Boolean,
      required: true,
    },
    editConversation: {
      type: Object as () => ConversationDetailType,
      default: null,
    },
    onClose: {
      type: Function as PropType<() => void>,
      required: true,
    },
    onFinish: {
      type: Function as PropType<(name: string) => void>,
      required: true,
    },
  },
  setup(props) {
    const loading = ref(false);
    // 使用正确的HTMLInputElement类型
    const conversationNameInputRef = ref<HTMLInputElement | null>(null);
    const formState = reactive({
      conversationName: "",
    });

    watch(
      () => props.visible,
      newVisible => {
        if (newVisible && props.editConversation) {
          formState.conversationName = props.editConversation.chatName;
          nextTick(() => {
            if (conversationNameInputRef.value) {
              // 使用DOM原生方法
              conversationNameInputRef.value.focus();
              // 全选文本内容
              conversationNameInputRef.value.select();
            }
          });
        }
      }
    );

    // 创建Form实例，用于表单校验
    const formRef = ref();

    const onConfirm = async () => {
      // 首先进行表单校验
      try {
        // 使用Form组件的validate方法进行校验
        await formRef.value.validate();
        
        // 校验通过后，确保名称不为空（额外检查）
        if (!formState.conversationName || formState.conversationName.trim() === '') {
          return;
        }

        loading.value = true;
        try {
          await updateConversationName(
            formState.conversationName.trim(),
            props.editConversation.chatId
          );
          props.onFinish(formState.conversationName.trim());
        } finally {
          loading.value = false;
        }
      } catch (error) {
        // 表单校验失败
        console.error('表单校验失败:', error);
        // 不继续后续操作
      }
    };

    return {
      formState,
      loading,
      conversationNameInputRef,
      onConfirm,
      formRef, // 暴露formRef给模板使用
    };
  },
});
</script>
