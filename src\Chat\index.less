.hr-chat-box {
    height: 100%;
    overflow: hidden;
    background: linear-gradient(180deg, rgba(23, 74, 228, 0) 29.44%, rgba(23, 74, 228, 0.06) 100%),
      linear-gradient(90deg, #f3f3f7 0%, #f3f3f7 20%, #ebf0f9 60%, #f3f3f7 80%, #f3f3f7 100%);
  
    .chatSection {
      display: flex;
      width: 100%;
      height: 100%;
      overflow: hidden;
  
      .chatApp {
        display: flex;
        flex: 1;
        flex-direction: column;
        height: 100%;
        color: rgba(0, 0, 0, 0.87);
  
        .chatBody {
          display: flex;
          flex: 1;
          height: 100%;
  
          .chatContent {
            position: relative;
            display: flex;
            flex-direction: column;
            width: 100%;
  
            .chatHeader {
              position: absolute;
              top: 0;
              z-index: 9;
              display: flex;
              align-items: baseline;
              width: 100%;
              padding: 14px 16px;
              background: rgba(243, 243, 247, 0.85);
              backdrop-filter: blur(2px);
  
              .chatHeaderTitle {
                color: var(--text-color);
                font-weight: 500;
                font-size: 15px;
              }
  
              .chatHeaderTip {
                max-width: 600px;
                margin-left: 5px;
                overflow: hidden;
                color: var(--text-color-third);
                font-size: 12px;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
    }
  
    &.historyVisible {
      .chatSection {
        .chatApp {
          width: calc(100% - 707px);
        }
      }
    }
  
    &.mobile {
      .chatSection {
        .chatApp {
          width: 100%;
        }
      }
    }
  }
  
  .showCaseModal {
    :deep(.ant-modal-content) {
      border-radius: 8px;
  
      .ant-modal-header {
        border-radius: 8px 8px 0 0;
      }
  
      .ant-modal-body {
        padding: 20px 0 !important;
      }
    }
  }
  
  .showCaseDrawer {
    :deep(.ant-drawer-content) {
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
      .ant-drawer-body {
        padding: 4px 0 !important;
      }
    }
  }
  
  :deep(.ss-chat-recommend-options) {
    .ant-table-thead .ant-table-cell {
      padding: 8px !important;
    }
  
    .ant-table-tbody .ant-table-cell {
      padding: 8px !important;
      border-bottom: 1px solid #f0f0f0;
    }
  }
  