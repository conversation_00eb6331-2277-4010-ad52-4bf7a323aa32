<template>
  <div :class="`${tipPrefixCls}-parse-tip`" v-if="shouldRender">
    <div :class="`${tipPrefixCls}-title-bar`">
      <check-circle-filled :class="`${tipPrefixCls}-step-icon`" />
      <div :class="`${tipPrefixCls}-step-title`">
        SQL生成
        <span v-if="!!sqlTimeCost" :class="`${tipPrefixCls}-title-tip`">(耗时: {{ sqlTimeCost }}ms)</span>
        ：
        <span v-if="sqlType" :class="`${prefixCls}-toggle-expand-btn`" @click="onCollapse">
          <up-outlined />
        </span>
      </div>
      <div :class="`${tipPrefixCls}-content-options`">
        <div
          v-if="llmReq"
          :class="[
            `${tipPrefixCls}-content-option`,
            sqlType === 'schemaMap' ? `${tipPrefixCls}-content-option-active` : ''
          ]"
          @click="setSqlType(sqlType === 'schemaMap' ? '' : 'schemaMap')"
        >
          Schema映射
        </div>
        <div
          v-if="fewShots.length > 0"
          :class="[
            `${tipPrefixCls}-content-option`,
            sqlType === 'fewShots' ? `${tipPrefixCls}-content-option-active` : ''
          ]"
          @click="setSqlType(sqlType === 'fewShots' ? '' : 'fewShots')"
        >
          Few-shot示例
        </div>
        <div
          v-if="sqlInfo.parsedS2SQL"
          :class="[
            `${tipPrefixCls}-content-option`,
            sqlType === 'parsedS2SQL' ? `${tipPrefixCls}-content-option-active` : ''
          ]"
          @click="setSqlType(sqlType === 'parsedS2SQL' ? '' : 'parsedS2SQL')"
        >
          {{ queryMode === 'LLM_S2SQL' || queryMode === 'PLAIN_TEXT' ? 'LLM' : 'Rule' }}解析S2SQL
        </div>
        <div
          v-if="sqlInfo.correctedS2SQL"
          :class="[
            `${tipPrefixCls}-content-option`,
            sqlType === 'correctedS2SQL' ? `${tipPrefixCls}-content-option-active` : ''
          ]"
          @click="setSqlType(sqlType === 'correctedS2SQL' ? '' : 'correctedS2SQL')"
        >
          修正S2SQL
        </div>
        <div
          v-if="sqlInfo.querySQL"
          :class="[
            `${tipPrefixCls}-content-option`,
            sqlType === 'querySQL' ? `${tipPrefixCls}-content-option-active` : ''
          ]"
          @click="setSqlType(sqlType === 'querySQL' ? '' : 'querySQL')"
        >
          最终执行SQL
        </div>
        <a-button :class="`${prefixCls}-export-log`" size="small" @click="onExportLog">
          <template #icon><download-outlined /></template>
          导出日志
        </a-button>
      </div>
    </div>
    <div
      :class="[
        prefixCls,
        isEmbedded ? `${prefixCls}-copilot` : ''
      ]"
    >
      <div v-if="sqlType === 'schemaMap'" :class="`${prefixCls}-code`">
        <div v-if="schema?.fieldNameList?.length > 0" :class="`${prefixCls}-schema-row`">
          <div :class="`${prefixCls}-schema-title`">名称：</div>
          <div :class="`${prefixCls}-schema-content`">
            {{ schema.fieldNameList.join('、') }}
          </div>
        </div>
        <div v-if="schema?.values?.length > 0" :class="`${prefixCls}-schema-row`">
          <div :class="`${prefixCls}-schema-title`">取值：</div>
          <div :class="`${prefixCls}-schema-content`">
            {{ schema.values.map(item => `${item.fieldName}: ${item.fieldValue}`).join('、') }}
          </div>
        </div>
        <div v-if="priorExts" :class="`${prefixCls}-schema-row`">
          <div :class="`${prefixCls}-schema-title`">附加：</div>
          <div :class="`${prefixCls}-schema-content`">{{ priorExts }}</div>
        </div>
        <div v-if="terms?.length > 0" :class="`${prefixCls}-schema-row`">
          <div :class="`${prefixCls}-schema-title`">术语：</div>
          <div :class="`${prefixCls}-schema-content`">
            {{ terms.map(item => `${item.name}${item.alias?.length > 0 ? `(${item.alias.join(',')})` : ''}: ${item.description}`).join('、') }}
          </div>
        </div>
      </div>
      <div v-if="sqlType === 'fewShots'" :class="`${prefixCls}-code`">
        <div v-for="(item, index) in fewShots" :key="index" :class="`${prefixCls}-few-shot-item`">
          <div :class="`${prefixCls}-few-shot-title`">示例{{ index + 1 }}：</div>
          <div :class="`${prefixCls}-few-shot-content`">
            <div :class="`${prefixCls}-few-shot-content-item`">
              <div :class="`${prefixCls}-few-shot-content-title`">问题：</div>
              <div :class="`${prefixCls}-few-shot-content-text`">{{ item.question }}</div>
            </div>
            <div :class="`${prefixCls}-few-shot-content-item`">
              <div :class="`${prefixCls}-few-shot-content-title`">SQL：</div>
              <div :class="`${prefixCls}-few-shot-content-text`">
                <div :class="`${prefixCls}-few-shot-code`">{{ item.sql }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template v-if="sqlType && sqlInfo[sqlType as keyof SqlInfoType]">
        <div :class="`${prefixCls}-code`">
          {{ formattedSql }}
        </div>
        <a-button 
          :class="`${prefixCls}-copy-btn`" 
          type="primary" 
          size="small" 
          @click="() => handleCopy(formattedSql, true)"
        >
          复制代码
        </a-button>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { format } from 'sql-formatter';
import { message, Button as AButton } from 'ant-design-vue';
import { PREFIX_CLS } from '../../common/constants';
import { 
  CheckCircleFilled, 
  DownloadOutlined, 
  UpOutlined 
} from '@ant-design/icons-vue';
import { SqlInfoType } from '../../common/type';
import { exportTextFile } from '../../utils/utils';

// 定义组件属性
const props = defineProps<{
  agentId?: number;
  queryId?: number;
  question: string;
  llmReq?: any;
  llmResp?: any;
  integrateSystem?: string;
  queryMode?: string;
  sqlInfo: SqlInfoType;
  sqlTimeCost?: number;
  executeErrorMsg: string;
}>();

// 状态
const sqlType = ref('');

// 计算属性
const tipPrefixCls = `${PREFIX_CLS}-item`;
const prefixCls = `${PREFIX_CLS}-sql-item`;
const isEmbedded = computed(() => 
  props.integrateSystem && 
  props.integrateSystem !== 'wiki' && 
  typeof window !== 'undefined' && 
  !window.location.pathname.includes('/chat')
);

// 如果没有内容则不渲染
const shouldRender = computed(() => {
  return !!props.llmReq || 
         !!props.sqlInfo.parsedS2SQL || 
         !!props.sqlInfo.correctedS2SQL || 
         !!props.sqlInfo.querySQL;
});

// 从 llmReq 和 llmResp 中获取数据
const schema = computed(() => props.llmReq?.schema || {});
const terms = computed(() => props.llmReq?.terms || []);
const priorExts = computed(() => props.llmReq?.priorExts);
const fewShots = computed(() => {
  const sqlRespMap = props.llmResp?.sqlRespMap || {};
  const firstKey = Object.keys(sqlRespMap)[0];
  return firstKey ? (sqlRespMap[firstKey]?.fewShots || []) : [];
});

// 格式化当前 SQL
const formattedSql = computed(() => {
  if (sqlType.value && props.sqlInfo && (sqlType.value in props.sqlInfo)) {
    return format(props.sqlInfo[sqlType.value as keyof SqlInfoType] as string);
  }
  return '';
});

// 方法
const setSqlType = (type: string) => {
  sqlType.value = type;
};

const onCollapse = () => {
  sqlType.value = '';
};

const handleCopy = (_: string, result: boolean) => {
  result ? message.success('复制SQL成功', 1) : message.error('复制SQL失败', 1);
};

// 获取不同部分的文本
const getSchemaMapText = () => {
  return `
Schema映射
${schema.value?.fieldNameList?.length > 0 ? `名称：${schema.value.fieldNameList.join('、')}` : ''}${
    schema.value?.values?.length > 0
      ? `
取值：${schema.value.values
          .map((item: any) => {
            return `${item.fieldName}: ${item.fieldValue}`;
          })
          .join('、')}`
      : ''
  }${
    priorExts.value
      ? `
附加：${priorExts.value}`
      : ''
  }${
    terms.value?.length > 0
      ? `
术语：${terms.value
          .map((item: any) => {
            return `${item.name}${item.alias?.length > 0 ? `(${item.alias.join(',')})` : ''}: ${
              item.description
            }`;
          })
          .join('、')}`
      : ''
  }

`;
};

const getFewShotText = () => {
  return `
Few-shot示例${fewShots.value
    .map((item: any, index: number) => {
      return `

示例${index + 1}：
问题：${item.question}
SQL：
${format(item.sql)}
`;
    })
    .join('')}
`;
};

const getParsedS2SQLText = () => {
  return `
${props.queryMode === 'LLM_S2SQL' || props.queryMode === 'PLAIN_TEXT' ? 'LLM' : 'Rule'}解析S2SQL

${format(props.sqlInfo.parsedS2SQL)}
`;
};

const getCorrectedS2SQLText = () => {
  return `
修正S2SQL

${format(props.sqlInfo.correctedS2SQL)}
`;
};

const getQuerySQLText = () => {
  return `
最终执行SQL

${format(props.sqlInfo.querySQL)}
`;
};

const getErrorMsgText = () => {
  return `
异常日志

${props.executeErrorMsg}
`;
};

const onExportLog = () => {
  let text = '';
  if (props.question) {
    text += `
问题：${props.question}
`;
  }
  if (props.llmReq) {
    text += getSchemaMapText();
  }
  if (fewShots.value.length > 0) {
    text += getFewShotText();
  }
  if (props.sqlInfo.parsedS2SQL) {
    text += getParsedS2SQLText();
  }
  if (props.sqlInfo.correctedS2SQL) {
    text += getCorrectedS2SQLText();
  }
  if (props.sqlInfo.querySQL) {
    text += getQuerySQLText();
  }
  if (!!props.executeErrorMsg) {
    text += getErrorMsgText();
  }
  exportTextFile(text, `supersonic-debug-${props.agentId}-${props.queryId}.log`);
};
</script>

<style lang="less" scoped>
@import './style.less';
</style>
