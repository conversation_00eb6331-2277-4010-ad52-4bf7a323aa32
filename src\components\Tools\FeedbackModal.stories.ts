import { Meta, StoryFn } from '@storybook/vue3';
import FeedbackModal from './FeedbackModal.vue';
import { ref } from 'vue';
import { fn } from '@storybook/test';

export default {
  title: 'Components/FeedbackModal',
  component: FeedbackModal,
  tags: ['autodocs'],
  argTypes: {
    visible: {
      control: { type: 'boolean' },
      description: '是否显示模态框',
    },
    feedbackValue: {
      control: { type: 'text' },
      description: '反馈内容的初始值',
    },
    submit: {
      action: 'submit',
      description: '提交反馈的回调',
    },
    close: {
      action: 'close',
      description: '关闭模态框的回调',
    },
  },
  args: {
    visible: false,
    feedbackValue: '',
  },
  parameters: {
    docs: {
      description: {
        component: '反馈模态框组件，用于收集用户的文字反馈',
      },
    },
  },
} as Meta<typeof FeedbackModal>;

const Template: StoryFn<typeof FeedbackModal> = (args: any) => ({
  components: { FeedbackModal },
  setup() {
    const visible = ref(args.visible);
    const onSubmit = (feedback: string) => {
      console.log('提交的反馈:', feedback);
      args.submit(feedback);
      visible.value = false;
    };
    const onClose = () => {
      console.log('关闭模态框');
      args.close();
      visible.value = false;
    };
    
    return { 
      args,
      visible,
      onSubmit,
      onClose
    };
  },
  template: `
    <div>
      <button @click="visible = true">打开反馈模态框</button>
      <FeedbackModal 
        :visible="visible" 
        :feedbackValue="args.feedbackValue"
        @submit="onSubmit"
        @close="onClose"
      />
    </div>
  `,
});

export const 默认 = Template.bind({});
默认.args = {
  visible: false,
  feedbackValue: '',
};

export const 预填内容 = Template.bind({});
预填内容.args = {
  visible: false,
  feedbackValue: '这是一个预先填写的反馈内容',
  submit: fn(),
  close: fn(),
};

export const 已打开 = Template.bind({});
已打开.args = {
  visible: true,
  feedbackValue: '',
}; 