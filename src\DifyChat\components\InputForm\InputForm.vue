<template>
  <div class="input-form-container">
    <div class="input-form-title" @click="toggleExpand">
      <div class="input-form-title-text">对话设置</div>
      <div
        class="input-form-title-close"
        :class="{ 'has-error': hasValidateError }"
      >
        <span>{{ isExpanded ? "收起" : "展开" }}</span>
        <span class="arrow-icon" :class="{ 'arrow-down': !isExpanded }">↑</span>
      </div>
    </div>
    <transition name="slide-fade">
      <div v-show="isExpanded" class="form-content">
        <Form ref="formRef" :model="formState" layout="vertical">
          <div v-for="item in inputsForms" :key="item.variable">
            <template v-if="item.type === InputVarType.textInput">
              <FormItem
                :label="item.label"
                :name="item.variable"
                :rules="
                  item.required
                    ? [{ required: true, message: `${item.label}是必填项` }]
                    : []
                "
              >
                <Input
                  v-model:value="formState[item.variable]"
                  :placeholder="item.label"
                  :maxlength="item.max_length"
                  show-count
                />
              </FormItem>
            </template>

            <template v-if="item.type === InputVarType.paragraph">
              <FormItem
                :label="item.label"
                :name="item.variable"
                :rules="
                  item.required
                    ? [{ required: true, message: `${item.label}是必填项` }]
                    : []
                "
              >
                <Textarea
                  v-model:value="formState[item.variable]"
                  :placeholder="item.label"
                  :maxlength="item.max_length"
                  show-count
                />
              </FormItem>
            </template>

            <template v-if="item.type === InputVarType.select">
              <FormItem
                :label="item.label"
                :name="item.variable"
                :rules="
                  item.required
                    ? [{ required: true, message: `${item.label}是必填项` }]
                    : []
                "
              >
                <Select
                  v-model:value="formState[item.variable]"
                  :placeholder="item.label"
                >
                  <SelectOption
                    v-for="option in item.options"
                    :key="option"
                    :value="option"
                  >
                    {{ option }}
                  </SelectOption>
                </Select>
              </FormItem>
            </template>

            <template v-if="item.type === InputVarType.number">
              <FormItem
                :label="item.label"
                :name="item.variable"
                :rules="
                  item.required
                    ? [{ required: true, message: `${item.label}是必填项` }]
                    : []
                "
              >
                <InputNumber
                  v-model:value="formState[item.variable]"
                  :placeholder="item.label"
                  style="width: 100%"
                />
              </FormItem>
            </template>

            <template v-if="item.type === InputVarType.singleFile">
              <FormItem
                :label="item.label"
                :name="item.variable"
                :rules="
                  item.required
                    ? [{ required: true, message: `${item.label}是必填项` }]
                    : []
                "
              >
                <Upload
                  :before-upload="beforeUpload"
                  :action="uploadUrl"
                  :headers="{
                    Authorization: `Bearer ${authToken}`,
                  }"
                  :file-list="formState[item.variable]"
                  :max-count="1"
                  @change="info => handleChange(info, item.variable)"
                  :accept="item.accept"
                >
                  <Button>
                    <UploadOutlined />
                    Upload
                  </Button>
                </Upload>
              </FormItem>
            </template>

            <template v-if="item.type === InputVarType.multiFiles">
              <FormItem
                :label="item.label"
                :name="item.variable"
                :rules="
                  item.required
                    ? [{ required: true, message: `${item.label}是必填项` }]
                    : []
                "
              >
                <Upload
                  :before-upload="beforeUpload"
                  :action="uploadUrl"
                  :headers="{
                    Authorization: `Bearer ${authToken}`,
                  }"
                  :file-list="formState[item.variable]"
                  :max-count="item.max_count"
                  @change="info => handleChange(info, item.variable)"
                  :accept="item.accept"
                >
                  <Button>
                    <UploadOutlined />
                    Upload
                  </Button>
                </Upload>
              </FormItem>
            </template>
          </div>
        </Form>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import type { ClientAgentType } from "@/types/client";
import { computed, ref, watch } from "vue";
import { InputVarType } from "@/DifyChat/types/app";
import { SupportUploadFileTypes } from "@/DifyChat/types/type";
import {
  FormItem,
  Form,
  Input,
  Textarea,
  Select,
  SelectOption,
  InputNumber,
  Upload,
  message as Message,
  Button,
} from "ant-design-vue";
import { UploadOutlined } from "@ant-design/icons-vue";
import { API_PREFIX, adaptationUrl } from "@/config/config";
import { getToken } from "@/utils/utils";
import { getSupportFileType, FILE_EXTS } from "@/DifyChat/utils/file-uploader";
import { TransferMethod } from "@/DifyChat/types/app";
interface Props {
  currentAgent: ClientAgentType;
}

const props = defineProps<Props>();

const formRef = ref<any>(null);
const formState = ref<Record<string, any>>({});
const isExpanded = ref(true);
const hasValidateError = ref(false);

const inputsForms = ref<any[]>([]);

const uploadUrl = `${API_PREFIX}${adaptationUrl("/knowledge/kbAssistantConfig/upload")}`;
const authToken = getToken();

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const system_parameters = computed(() => {
  return (
    props.currentAgent.difyAppParams?.system_parameters || {
      file_size_limit: 20,
      image_file_size_limit: 20,
    }
  );
});

watch(
  () => props.currentAgent,
  newVal => {
    const userInputForm = newVal.difyAppParams?.user_input_form || [];
    inputsForms.value = [];
    formState.value = {};

    inputsForms.value = userInputForm.map(item => {
      if (item["text-input"]) {
        formState.value[item["text-input"].variable] = "";
        return {
          ...item["text-input"],
          type: InputVarType.textInput,
        };
      }

      if (item.paragraph) {
        formState.value[item.paragraph.variable] = "";
        return {
          ...item.paragraph,
          type: InputVarType.paragraph,
        };
      }

      if (item.select) {
        formState.value[item.select.variable] = "";
        return {
          ...item.select,
          type: InputVarType.select,
        };
      }

      if (item.number) {
        formState.value[item.number.variable] = "";
        return {
          ...item.number,
          type: InputVarType.number,
        };
      }

      if (item.file) {
        formState.value[item.file.variable] = [];

        const allowed_file_types = item.file.allowed_file_types || [];
        const accepts = allowed_file_types
          .map((type: any) => {
            return FILE_EXTS[type].map(ext => `.${ext}`).join(",");
          })
          .join(",");

        return {
          ...item.file,
          type: InputVarType.singleFile,
          accept: accepts,
        };
      }

      if (item["file-list"]) {
        formState.value[item["file-list"].variable] = [];

        const allowed_file_types = item["file-list"].allowed_file_types || [];
        const accepts = allowed_file_types
          .map((type: any) => {
            return FILE_EXTS[type].map(ext => `.${ext}`).join(",");
          })
          .join(",");

        return {
          ...item["file-list"],
          type: InputVarType.multiFiles,
          accept: accepts,
        };
      }
    });
  },
  { immediate: true }
);

const beforeUpload = (file: any) => {
  const fileName = file.name;
  const fileType = file.type || "";
  const fileSize = file.size || 0;

  // 检查文件类型是否支持
  const supportType = getSupportFileType(fileName, fileType);

  if (!supportType) {
    Message.error("不支持的文件类型");
    file.status = "error"; // 手动设置文件状态为错误
    return false;
  }

  // 根据文件类型判断文件大小限制
  const maxSize =
    supportType === SupportUploadFileTypes.image
      ? system_parameters.value.image_file_size_limit
      : system_parameters.value.file_size_limit;

  // 转换为MB
  const fileSizeInMB = fileSize / (1024 * 1024);

  if (fileSizeInMB > maxSize) {
    Message.error(`文件大小不能超过 ${maxSize}MB`);
    file.status = "error"; // 手动设置文件状态为错误
    return false;
  }

  return true;
};

const handleChange = (info: any, variable: string) => {
  // 获取当前文件和文件列表
  const { fileList } = info;

  const fileListRef: any[] = [];

  // 过滤掉错误文件
  fileList.forEach((f: any) => {
    if (f.status !== "error") {
      fileListRef.push(f);
    }
  });

  formState.value[variable] = fileListRef;
};

const getFormData = async () => {
  // 表单校验
  try {
    await formRef.value?.validate();
    hasValidateError.value = false;

    // 处理文件转化
    const formData: Record<string, any> = {};
    Object.keys(formState.value).forEach(key => {
      // 查找当前字段对应的表单项定义
      const formItem = inputsForms.value.find(item => item.variable === key);

      if (formState.value[key] instanceof Array) {
        const fileList = formState.value[key];
        const validFiles = fileList.filter(
          file => file.status === "success" || file.status === "done"
        );

        const fileData = validFiles.map(item => {
          const fileType = getSupportFileType(
            item?.response?.name as string,
            item?.response?.mimeType as string
          );

          return {
            transfer_method: TransferMethod.local_file,
            upload_file_id: item?.response?.id,
            url: "",
            type: fileType,
          };
        });

        // 根据表单项类型决定是返回单个文件还是文件数组
        if (
          formItem &&
          formItem.type === InputVarType.singleFile &&
          fileData.length > 0
        ) {
          formData[key] = fileData[0]; // 单文件上传只取第一个文件
        } else {
          formData[key] = fileData; // 多文件上传返回文件数组
        }
      } else {
        formData[key] = formState.value[key];
      }
    });

    return formData;
  } catch (error) {
    // 校验失败，标记有错误
    hasValidateError.value = true;

    // 如果校验失败且表单收起，自动展开表单
    if (!isExpanded.value) {
      isExpanded.value = true;
    }
    return Promise.reject(error);
  }
};

const setFormData = (data: any) => {
  // 处理表单的单个文件上传和多文件上传
  // const formData: Record<string, any> = { ...data };

  // 遍历所有表单项
  inputsForms.value.forEach(item => {
    const key = item.variable;

    // 处理文件类型的数据
    if (
      item.type === InputVarType.singleFile ||
      item.type === InputVarType.multiFiles
    ) {
      const fileData = data[key];

      // 如果没有数据，初始化为空数组
      if (!fileData) {
        formState.value[key] = [];
        return;
      }

      // 处理单文件格式 - 如果是单个文件对象，转换为数组格式以适应UI组件
      if (item.type === InputVarType.singleFile && !Array.isArray(fileData)) {
        // 将单个文件对象转换为fileList格式
        formState.value[key] = [
          {
            uid: fileData.related_id,
            name: fileData.filename || fileData.related_id,
            status: "done",
            response: {
              id: fileData.related_id,
              name: fileData.filename || fileData.related_id,
              mimeType: fileData.mime_type,
            },
          },
        ];
      }
      // 处理多文件格式
      else if (Array.isArray(fileData)) {
        // 将文件数组转换为fileList格式
        formState.value[key] = fileData.map((file: any) => ({
          uid: file.related_id,
          name: file.filename || file.related_id,
          status: "done",
          response: {
            id: file.related_id,
            name: file.filename || file.related_id,
            mimeType: file.mime_type,
          },
        }));
      } else {
        formState.value[key] = [];
      }
    } else {
      // 处理非文件类型的数据
      formState.value[key] = data[key] || "";
    }
  });
};

defineExpose({
  getFormData,
  setFormData,
});
</script>

<style lang="less" scoped>
.input-form-container {
  background: #fff;
  padding: 16px;
  border-radius: 10px;
  margin-bottom: 20px;
  overflow: hidden;
}

.input-form-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 16px;
  cursor: pointer;
  user-select: none;
  // padding-bottom: 12px;
  // border-bottom: 1px solid #f0f0f0;
}

.input-form-title-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.input-form-title-close {
  display: flex;
  align-items: center;
  color: #1890ff;
  font-size: 14px;

  &.has-error {
    color: #ff4d4f;
  }
}

.arrow-icon {
  margin-left: 4px;
  transition: transform 0.3s;
}

.arrow-down {
  transform: rotate(180deg);
}

// .form-content {
//   // margin-top: 16px;
// }

/* 过渡动画样式 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
  max-height: 1000px;
  opacity: 1;
  overflow: hidden;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  max-height: 0;
  opacity: 0;
}
</style>
