import { fn } from "@storybook/test";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";

import Message from "./index.vue";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta = {
  title: "Components/ChatMsg/Message",
  component: Message,
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  argTypes: {
    position: {
      control: "select",
      options: ["left", "right"],
      description: "消息气泡的位置",
    },
    width: {
      control: "text",
      description: "消息气泡的宽度",
    },
    maxWidth: {
      control: "text",
      description: "消息气泡的最大宽度",
    },
    height: {
      control: "text",
      description: "消息气泡的高度",
    },
    queryMode: {
      control: "select",
      options: ["METRIC_ID", "TAG_DETAIL", "TAG_LIST_FILTER"],
      description: "查询模式",
    },
    bubbleClassName: {
      control: "text",
      description: "气泡额外的类名",
    },
    isMobileMode: {
      control: "boolean",
      description: "是否为移动端模式",
    },
    title: {
      control: "text",
      description: "消息标题",
    },
    followQuestions: {
      control: "array",
      description: "跟随问题列表",
    },
    chatContext: {
      control: "object",
      description: "聊天上下文信息",
    },
    entityInfo: {
      control: "object",
      description: "实体信息",
    },
    onClick: fn(),
  },
  args: {
    position: "left",
    width: "500px",
    height: "auto",
  },
} satisfies Meta<typeof Message>;

export default meta;
type Story = StoryObj<typeof meta>;
/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */

export const Default: Story = {
  args: {
    position: "left",
    width: "500px",
    maxWidth: "800px",
    height: "auto",
    chatContext: {
      modelName: "用户画像",
      dateInfo: {
        startDate: "2024-03-01",
        endDate: "2024-03-31",
      },
      dimensionFilters: [
        {
          name: "年龄段",
          value: "18-25岁",
        },
        {
          name: "性别",
          value: "男",
        },
      ],
    },
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>这是消息内容</div>
        </Message>
      `,
  }),
};

export const Unauthorized: Story = {
  args: {
    ...Default.args,
    queryColumns: [
      {
        name: "数据",
        bizName: "data",
        showType: "TEXT",
        authorized: false,
      },
    ],
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>未授权的消息内容</div>
        </Message>
      `,
  }),
};

export const MetricIdMode: Story = {
  args: {
    position: "left",
    width: "500px",
    queryMode: "METRIC_ID",
    entityInfo: {
      dimensions: [
        {
          name: "用户ID",
          bizName: "user_id",
          value: "12345",
        },
        {
          name: "姓名",
          bizName: "name",
          value: "张三",
        },
        {
          name: "年龄",
          bizName: "age",
          value: "25",
        },
        {
          name: "职业",
          bizName: "occupation",
          value: "工程师",
        },
      ],
    },
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>指标详情展示</div>
        </Message>
      `,
  }),
};

export const TagListFilterMode: Story = {
  args: {
    position: "left",
    width: "500px",
    queryMode: "TAG_LIST_FILTER",
    chatContext: {
      modelName: "用户画像",
      dateInfo: {
        startDate: "2024-03-01",
        endDate: "2024-03-31",
      },
      dimensionFilters: [
        {
          name: "地区",
          value: ["北京", "上海", "广州"],
        },
        {
          name: "用户等级",
          value: "VIP",
        },
      ],
    },
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>标签列表筛选结果</div>
        </Message>
      `,
  }),
};

export const RightPosition: Story = {
  args: {
    ...Default.args,
    position: "right",
  },
  render: Default.render,
};

export const WithTitle: Story = {
  args: {
    ...Default.args,
    title: "消息标题",
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>带标题的消息</div>
        </Message>
      `,
  }),
};

export const WithFollowQuestions: Story = {
  args: {
    ...Default.args,
    followQuestions: [
      "这个指标是如何计算的？",
      "可以查看更多详情吗？",
      "如何提高这个指标？",
    ],
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>带跟随问题的消息</div>
        </Message>
      `,
  }),
};

export const CustomWidth: Story = {
  args: {
    ...Default.args,
    width: "300px",
  },
  render: Default.render,
};

export const CustomMaxWidth: Story = {
  args: {
    ...Default.args,
    maxWidth: "400px",
  },
  render: Default.render,
};

export const CustomHeight: Story = {
  args: {
    ...Default.args,
    height: "200px",
  },
  render: Default.render,
};

export const CustomBubbleClassName: Story = {
  args: {
    ...Default.args,
    bubbleClassName: "custom-bubble",
  },
  render: Default.render,
};

export const MobileMode: Story = {
  args: {
    ...Default.args,
    isMobileMode: true,
    width: "90%",
  },
  render: Default.render,
};

export const TagDetailMode: Story = {
  args: {
    position: "left",
    width: "500px",
    queryMode: "TAG_DETAIL",
    entityInfo: {
      dimensions: [
        {
          name: "标签名称",
          bizName: "tag_name",
          value: "高活跃用户",
        },
        {
          name: "覆盖人数",
          bizName: "coverage",
          value: "12,345",
        },
        {
          name: "更新时间",
          bizName: "update_time",
          value: "2024-03-15",
        },
      ],
    },
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>标签详情展示</div>
        </Message>
      `,
  }),
};

export const WithComplexChatContext: Story = {
  args: {
    ...Default.args,
    chatContext: {
      modelName: "用户行为分析",
      dateInfo: {
        startDate: "2024-01-01",
        endDate: "2024-03-31",
        dateMode: "quarter",
        period: "Q1",
        text: "2024年第一季度",
      },
      dimensionFilters: [
        {
          name: "用户类型",
          value: "新用户",
        },
        {
          name: "渠道",
          value: ["自然搜索", "付费广告", "社交媒体"],
        },
        {
          name: "设备",
          value: "移动端",
        },
      ],
    },
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>复杂上下文的消息内容</div>
        </Message>
      `,
  }),
};

export const WithImageDimension: Story = {
  args: {
    position: "left",
    width: "500px",
    queryMode: "METRIC_ID",
    entityInfo: {
      dimensions: [
        {
          name: "头像",
          bizName: "user_photo",
          value: "https://via.placeholder.com/40",
        },
        {
          name: "姓名",
          bizName: "name",
          value: "李四",
        },
        {
          name: "部门",
          bizName: "department",
          value: "技术部",
        },
      ],
    },
  },
  render: args => ({
    components: { Message },
    setup() {
      return { args };
    },
    template: `
        <Message v-bind="args">
          <div>带图片维度的消息</div>
        </Message>
      `,
  }),
};
