/// <reference types="vitest" />
import path from "path";
import { defineConfig } from "vite";
import packageJson from "./package.json";
import vue from "@vitejs/plugin-vue";

const getPackageName = () => {
  return packageJson.name;
};

const getPackageNameCamelCase = () => {
  try {
    return getPackageName().replace(/-./g, char => char[1].toUpperCase());
  } catch {
    throw new Error("Name property in package.json is missing.");
  }
};

const fileName = {
  es: `${getPackageName()}.js`,
};

const formats = Object.keys(fileName) as Array<keyof typeof fileName>;

export default defineConfig({
  base: "./",
  build: {
    outDir: "./build/dist",
    lib: {
      entry: path.resolve(__dirname, "src/index.ts"),
      name: getPackageNameCamelCase(),
      formats,
      fileName: format => fileName[format],
    },
    rollupOptions: {
      // 关键配置：外部化 Vue 依赖 "vue-types"
      external: [
        "vue",
        "screenfull",
        "katex",
        "cropperjs",
        "mermaid",
        "highlight.js",
        "prettier",
      ], // 可以继续添加其他需要排除的依赖
      output: {
        globals: {
          vue: "Vue", // 为 UMD 格式指定全局变量
          katex: "katex",
          mermaid: "mermaid",
          "highlight.js": "highlight.js",
          cropperjs: "cropperjs",
          screenfull: "screenfull",
          prettier: "prettier",
          // "vue-types": "VueTypes",
          // $: "$", // 显式保留 $ 符号
        },
      },
    },
    // 添加terser配置
    // minify: "terser",
    // terserOptions: {
    //   mangle: {
    //     reserved: ["$", "Vue"], // 保留$符号
    //   },
    // },
    // minify: false,
    // sourcemap: 'inline' // 方便调试
  },
  test: {
    watch: false,
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@@": path.resolve(__dirname),
    },
  },
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 允许$符号在模板中使用
          isCustomElement: tag => tag.includes("$"),
        },
      },
    }),
  ],
});
