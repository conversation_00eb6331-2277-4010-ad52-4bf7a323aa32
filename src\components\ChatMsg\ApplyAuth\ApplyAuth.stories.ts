import { Meta, StoryFn } from '@storybook/vue3';
import ApplyAuth from './index.vue';
import { PREFIX_CLS } from '../../../common/constants';
import { fn } from '@storybook/test';

export default {
  title: 'Components/ChatMsg/ApplyAuth',
  component: ApplyAuth,
  tags: ['autodocs'],
  argTypes: {
    model: {
      control: { type: 'text' },
      description: '需要申请的模型名称',
    },
    onApplyAuth: {
      action: 'applyAuth',
      description: '申请权限的回调函数',
    },
  },
  parameters: {
    docs: {
      description: {
        component: '权限申请组件，用于用户没有特定模型权限时申请访问权限',
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#333333' },
      ],
    },
  },
} as Meta<typeof ApplyAuth>;

const Template: StoryFn<typeof ApplyAuth> = (args) => ({
  components: { ApplyAuth },
  setup() {
    return { args, PREFIX_CLS };
  },
  template: `
    <div style="padding: 20px; max-width: 400px; border: 1px solid #eee; border-radius: 8px;">
      <ApplyAuth v-bind="args" />
    </div>
  `,
});

export const 默认 = Template.bind({});
默认.args = {
  model: 'GPT-4',
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};

export const 无申请按钮 = Template.bind({});
无申请按钮.args = {
  model: 'GPT-4',
};

export const 不同模型 = Template.bind({});
不同模型.args = {
  model: 'Claude-3',
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};

export const 深色背景 = Template.bind({});
深色背景.args = {
  model: 'GPT-4',
  onApplyAuth: fn((model) => console.log(`申请访问模型 ${model} 的权限`)),
};
深色背景.parameters = {
  backgrounds: { default: 'dark' },
};
深色背景.decorators = [
  () => ({ 
    template: '<div style="padding: 20px; color: white; background-color: #333; border-radius: 8px;"><story /></div>' 
  }),
]; 